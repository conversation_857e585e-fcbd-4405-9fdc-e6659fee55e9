# Guide de Déploiement et Lancement - EMA Companion AI

## 🚀 Prérequis Système

### Environnement Technique
- **Node.js** : Version 16.x ou supérieure
- **npm** : Version 7.x ou supérieure  
- **Python** : Version 3.8+ (pour les services API)
- **Système d'exploitation** : Windows 10/11, macOS, Linux

### Services Externes Requis
- **Anthropic API Key** : Accès à Claude 3.7 Sonnet
- **Base de données SQL** : SQL Server ou 4D
- **Qdrant Vector DB** : Pour la recherche documentaire
- **Stockage PDF** : Accès aux documents médicaux

## 📦 Installation Complète

### 1. Clonage et Installation des Dépendances

```bash
# Cloner le repository
git clone [URL_DU_REPOSITORY]
cd react-langraph-agent

# Installation des dépendances Node.js
npm install

# Vérification de l'installation
npm list --depth=0
```

### 2. Configuration des Variables d'Environnement

Créer un fichier `.env` à la racine du projet :

```bash
# Copier le template
cp .env.example .env

# Éditer le fichier .env
nano .env
```

**Contenu du fichier `.env` :**
```env
# === CONFIGURATION ANTHROPIC ===
ANTHROPIC_API_KEY=sk-ant-api03-[VOTRE_CLE_API]

# === CONFIGURATION BASE DE DONNÉES ===
API_BASE_URL=http://localhost:8086
DB_DRIVER=ODBC Driver 17 for SQL Server
DB_SERVER=localhost
DB_NAME=EMA_DATABASE
DB_USER=ema_user
DB_PASSWORD=ema_password

# === CONFIGURATION DOCUMENTS ===
DOCUMENT_API_URL=http://localhost:8087
QDRANT_HOST=localhost
QDRANT_PORT=6333

# === CONFIGURATION SERVEUR ===
FRONTEND_PORT=3000
BACKEND_PORT=3005
DB_API_PORT=8086
DOCUMENT_API_PORT=8087

# === CONFIGURATION LOGGING ===
LOG_LEVEL=info
LOG_FILE=./logs/ema-companion.log
```

### 3. Installation des Services Python

```bash
# Service API Base de Données
cd db-api-service
pip install -r requirements.txt

# Service Documents Vectoriels  
cd ../db-vector-service
pip install -r requirements.txt

# Retour au répertoire principal
cd ..
```

## 🔧 Configuration des Services

### 1. Configuration Base de Données

**Fichier : `db-api-service/.env`**
```env
DB_DRIVER=ODBC Driver 17 for SQL Server
DB_SERVER=**********
DB_NAME=EMA_PRODUCTION
DB_USER=ema_readonly
DB_PASSWORD=[MOT_DE_PASSE_SECURISE]
PORT=8086
HOST=0.0.0.0
```

**Test de connexion :**
```bash
cd db-api-service
python -c "from main import get_pyodbc_connection; print('Test:', get_pyodbc_connection())"
```

### 2. Configuration Documents Vectoriels

**Fichier : `db-vector-service/.env`**
```env
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_COLLECTION=ema_documents
DOCUMENT_PATH=/path/to/pdf/documents
PORT=8087
HOST=0.0.0.0
```

**Initialisation Qdrant :**
```bash
# Démarrer Qdrant (Docker)
docker run -p 6333:6333 qdrant/qdrant

# Ou installation locale
cd db-qdrant-vector
./start-qdrant.sh
```

### 3. Configuration Frontend/Backend

**Vérification de la configuration TypeScript :**
```bash
# Compilation du code
npm run build

# Vérification des types
npx tsc --noEmit
```

## 🚀 Méthodes de Lancement

### Méthode 1 : Lancement Complet Automatique (Recommandée)

```bash
# Démarrage de tous les services
npm run dev

# Ce script lance automatiquement :
# - Service API Base de Données (port 8086)
# - Service Documents Vectoriels (port 8087)  
# - Backend LangGraph Multi-Agents (port 3005)
# - Frontend React (port 3000)
```

**Vérification du démarrage :**
```bash
# Vérifier que tous les ports sont actifs
netstat -an | grep -E "(3000|3005|8086|8087)"

# Ou sur macOS/Linux
lsof -i :3000 -i :3005 -i :8086 -i :8087
```

### Méthode 2 : Lancement Manuel par Service

#### A. Service API Base de Données
```bash
cd db-api-service
python main.py

# Vérification
curl http://localhost:8086/
# Réponse attendue: {"message": "Database API Service is running"}
```

#### B. Service Documents Vectoriels
```bash
cd db-vector-service  
python main.py

# Vérification
curl http://localhost:8087/health
# Réponse attendue: {"status": "healthy"}
```

#### C. Backend Multi-Agents
```bash
# Compilation préalable
npm run build

# Lancement du serveur
npm run server

# Ou directement avec le fichier compilé
node dist/server.js
```

#### D. Frontend React
```bash
# Dans un nouveau terminal
npm run frontend:dev

# Accès via navigateur
open http://localhost:3000
```

### Méthode 3 : Lancement avec Docker (Production)

**Fichier : `docker-compose.yml`**
```yaml
version: '3.8'
services:
  ema-frontend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      
  ema-backend:
    build: .
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=production
      
  ema-db-api:
    build: ./db-api-service
    ports:
      - "8086:8086"
      
  ema-vector-api:
    build: ./db-vector-service
    ports:
      - "8087:8087"
      
  qdrant:
    image: qdrant/qdrant
    ports:
      - "6333:6333"
```

**Commandes Docker :**
```bash
# Construction et lancement
docker-compose up --build

# Lancement en arrière-plan
docker-compose up -d

# Arrêt des services
docker-compose down
```

## 🧪 Tests et Vérification

### 1. Tests de Connectivité

```bash
# Test API Base de Données
curl -X GET http://localhost:8086/tables

# Test API Documents
curl -X POST http://localhost:8087/search \
  -H "Content-Type: application/json" \
  -d '{"query": "test"}'

# Test Backend Multi-Agents
curl -X POST http://localhost:3005/api/message \
  -H "Content-Type: application/json" \
  -d '{"message": "Test de connexion"}'
```

### 2. Tests Fonctionnels

**Test SQL Workflow :**
```bash
curl -X POST http://localhost:3005/api/message \
  -H "Content-Type: application/json" \
  -d '{"message": "Quels sont les patients présents aujourd'\''hui?"}'
```

**Test Document Workflow :**
```bash
curl -X POST http://localhost:3005/api/message \
  -H "Content-Type: application/json" \
  -d '{"message": "Recherche des documents PDF pour le patient 12345"}'
```

### 3. Tests de Performance

```bash
# Test de charge simple
for i in {1..5}; do
  curl -X POST http://localhost:3005/api/message \
    -H "Content-Type: application/json" \
    -d '{"message": "Test performance '$i'"}' &
done
wait
```

## 🔍 Monitoring et Logs

### 1. Logs par Service

**Backend Multi-Agents :**
```bash
# Logs en temps réel
tail -f logs/backend.log

# Recherche d'erreurs
grep -i error logs/backend.log
```

**Service API Base de Données :**
```bash
# Logs FastAPI
tail -f db-api-service/api.log
```

**Service Documents :**
```bash
# Logs Qdrant
tail -f db-vector-service/vector.log
```

### 2. Monitoring des Performances

**Métriques système :**
```bash
# Utilisation CPU/Mémoire
top -p $(pgrep -f "node\|python")

# Connexions réseau
netstat -an | grep -E "(3000|3005|8086|8087)"
```

**Métriques applicatives :**
```bash
# Temps de réponse moyen
curl -w "@curl-format.txt" -X POST http://localhost:3005/api/message \
  -H "Content-Type: application/json" \
  -d '{"message": "Test performance"}'
```

## 🚨 Dépannage

### Problèmes Courants

#### 1. Erreur de Port Occupé
```bash
# Identifier le processus
lsof -i :3000
kill -9 [PID]

# Ou changer le port
export PORT=3001
npm run frontend:dev
```

#### 2. Erreur de Base de Données
```bash
# Vérifier la connexion
telnet localhost 1433

# Tester les credentials
sqlcmd -S localhost -U ema_user -P ema_password
```

#### 3. Erreur Anthropic API
```bash
# Vérifier la clé API
curl -H "Authorization: Bearer $ANTHROPIC_API_KEY" \
  https://api.anthropic.com/v1/messages
```

#### 4. Erreur de Compilation TypeScript
```bash
# Nettoyer et recompiler
rm -rf dist/
npm run build

# Vérifier les types
npx tsc --noEmit --strict
```

### Logs de Debug

**Activation du mode debug :**
```bash
# Variables d'environnement
export DEBUG=*
export LOG_LEVEL=debug

# Relancement avec logs détaillés
npm run dev
```

## 📋 Checklist de Déploiement

### Avant le Déploiement
- [ ] Variables d'environnement configurées
- [ ] Base de données accessible
- [ ] Qdrant Vector DB démarré
- [ ] Clé API Anthropic valide
- [ ] Compilation TypeScript réussie
- [ ] Tests de connectivité passés

### Après le Déploiement
- [ ] Tous les services démarrés
- [ ] Interface web accessible
- [ ] Tests fonctionnels réussis
- [ ] Logs sans erreurs critiques
- [ ] Performance acceptable
- [ ] Monitoring en place

### Production
- [ ] HTTPS configuré
- [ ] Certificats SSL valides
- [ ] Firewall configuré
- [ ] Sauvegardes automatiques
- [ ] Monitoring avancé
- [ ] Alertes configurées

## 🎯 Accès à l'Application

Une fois tous les services démarrés :

- **Interface Web** : http://localhost:3000
- **API Backend** : http://localhost:3005
- **API Base de Données** : http://localhost:8086
- **API Documents** : http://localhost:8087
- **Qdrant Dashboard** : http://localhost:6333/dashboard

**Première utilisation :**
1. Accéder à http://localhost:3000
2. Créer une nouvelle conversation
3. Tester avec : "Bonjour, peux-tu me présenter tes capacités ?"
4. Vérifier le flux d'agents dans les logs

L'architecture multi-agents EMA Companion AI est maintenant opérationnelle ! 🎉
