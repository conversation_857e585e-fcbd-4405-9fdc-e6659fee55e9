# Service de Recherche Vectorielle

Ce service expose une API pour la vectorisation de documents PDF et la recherche sémantique utilisant Qdrant.

## Prérequis

### Installation de Qdrant en local (sans Docker)

#### Méthode 1: Installation via Binaires précompilés

1. Téléchargez la dernière version de Qdrant depuis la [page des releases GitHub](https://github.com/qdrant/qdrant/releases)
   - Pour macOS: téléchargez le fichier `.dmg` ou `.tar.gz`
   - Pour Windows: téléchargez le fichier `.zip` ou `.exe`
   - Pour Linux: téléchargez le fichier `.tar.gz` approprié pour votre architecture

2. Extraction et installation:
   - **macOS**:
     ```bash
     # Si vous avez téléchargé le .tar.gz
     tar -xzf qdrant-x.x.x-darwin-amd64.tar.gz
     cd qdrant-x.x.x-darwin-amd64
     ```
     
     Ou installez via le fichier .dmg en le double-cliquant et en suivant les instructions.

   - **Windows**:
     - Extrayez le fichier .zip dans un dossier de votre choix
     - Ou exécutez le fichier .exe et suivez les instructions d'installation

   - **Linux**:
     ```bash
     tar -xzf qdrant-x.x.x-linux-amd64.tar.gz
     cd qdrant-x.x.x-linux-amd64
     ```

3. Configuration:
   - Créez un dossier pour stocker les données:
     ```bash
     mkdir -p qdrant_data
     ```

   - Créez un dossier `config` dans le même répertoire que l'exécutable Qdrant:
     ```bash
     mkdir -p config
     ```

   - Créez un fichier de configuration `config/production.yaml` (ou `config/development.yaml` pour l'environnement de développement):
     ```yaml
     storage:
       storage_path: ./qdrant_data
     
     service:
       host: 0.0.0.0
       port: 6333
       grpc_port: 6334
     ```

   Note: Qdrant cherche par défaut un fichier de configuration dans le dossier `config/`. Si ce dossier n'existe pas, Qdrant fonctionnera quand même avec les paramètres par défaut, mais vous verrez un avertissement dans les logs.

4. Démarrage de Qdrant:
   - **macOS/Linux**:
     ```bash
     ./qdrant
     ```
   
   - **Windows**:
     ```
     qdrant.exe
     ```

#### Méthode 2: Installation via Cargo (Rust)

Si vous avez Rust installé, vous pouvez installer Qdrant via Cargo:

1. Installez Rust si ce n'est pas déjà fait:
   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   ```

2. Installez Qdrant via Cargo:
   ```bash
   cargo install qdrant-cli
   ```

3. Créez un dossier pour les données:
   ```bash
   mkdir -p qdrant_data
   ```

4. Démarrez Qdrant:
   ```bash
   qdrant --storage-path ./qdrant_data
   ```

#### Méthode 3: Installation via Homebrew (macOS uniquement)

```bash
brew install qdrant
brew services start qdrant
```

### Vérification de l'installation

Pour vérifier que Qdrant fonctionne correctement:

```bash
curl http://localhost:6333/
```

Vous devriez recevoir une réponse JSON indiquant que Qdrant est en cours d'exécution.

### Installation des dépendances Python

```bash
pip install -r requirements.txt
```

## Configuration du code pour l'installation locale

Le code est déjà configuré pour se connecter à une instance locale de Qdrant. Dans `main.py`, la connexion est établie avec:

```python
client = QdrantClient("localhost", port=6333)
```

Si vous avez modifié les paramètres par défaut (hôte ou port) lors de l'installation, vous devrez mettre à jour cette ligne en conséquence.

## Démarrage du service

```bash
python main.py
```

Le service démarrera sur http://localhost:8087

## Utilisation de l'API

### 1. Vectorisation des documents

Pour vectoriser les PDFs d'un dossier:

```bash
curl -X POST "http://localhost:8087/vectorize?folder_path=E:/HEMADIALYSE/HMD_PJ" -H "Content-Type: application/json"
```

### 2. Recherche dans les documents

Pour effectuer une recherche sémantique:

```bash
curl -X POST "http://localhost:8087/search" \
     -H "Content-Type: application/json" \
     -d '{
           "query": "Je cherche des bilans bio",
           "limit": 5
         }'
```

## Documentation API

Une fois le service démarré, vous pouvez accéder à:
- Documentation OpenAPI: http://localhost:8087/docs
- Documentation ReDoc: http://localhost:8087/redoc

## Architecture

Le service utilise:
- FastAPI pour l'API REST
- Qdrant comme base de données vectorielle
- BGE (BAAI/bge-small-en-v1.5) pour les embeddings
- LangChain pour le traitement des documents

Les documents sont découpés en chunks de 500 caractères avec un chevauchement de 50 caractères pour une meilleure recherche contextuelle.

## Dépannage

### Problèmes courants avec l'installation locale de Qdrant

1. **Erreur de connexion à Qdrant**:
   - Vérifiez que Qdrant est bien en cours d'exécution
   - Vérifiez que les ports 6333 et 6334 sont disponibles et non bloqués par un pare-feu
   - Assurez-vous que l'hôte et le port dans le code correspondent à votre configuration

2. **Erreur de permission**:
   - Assurez-vous que vous avez les droits d'écriture dans le dossier `qdrant_data`
   - Sur Linux/macOS, vous pourriez avoir besoin d'exécuter avec `sudo` si vous utilisez des ports privilégiés

3. **Problèmes de mémoire**:
   - Qdrant peut nécessiter beaucoup de mémoire pour les grandes collections
   - Ajustez les paramètres de mémoire dans le fichier `config.yaml` si nécessaire:
     ```yaml
     storage:
       optimizers:
         default_segment_number: 2
         memmap_threshold: 20000
     ```

4. **Problèmes de performance**:
   - Pour améliorer les performances, vous pouvez ajuster les paramètres de la collection dans le code:
     ```python
     client.create_collection(
         collection_name=collection_name,
         vectors_config={
             "size": 384,
             "distance": "Cosine"
         },
         optimizers_config={
             "default_segment_number": 2
         }
     )
     ```
