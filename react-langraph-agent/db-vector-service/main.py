import os
import re 
import logging
from enum import Enum
from datetime import datetime
from typing import  Optional
from pydantic import BaseModel
from fastapi import FastAP<PERSON>, HTTPException
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PyPDFLoader
from langchain_qdrant import QdrantVectorStore
from langchain.embeddings.base import Embeddings
from FlagEmbedding import FlagModel
from qdrant_client import QdrantClient, models
from fastapi import Query

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("vector-api.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = FastAPI(title="Vector Search API")

logger.info("Starting Vector Search API")

class SearchQuery(BaseModel):
    query: str
    limit: Optional[int] = 5

class BGEEmbeddings(Embeddings):
    def __init__(self):
        self.model = FlagModel('BAAI/bge-small-en-v1.5', use_fp16=True)
    
    def embed_documents(self, texts):
        if not texts:
            return []
        texts = [str(text) for text in texts]
        try:
            return self.model.encode(texts)
        except Exception as e:
            print(f"Error during embedding: {e}")
            return [self.model.encode([text])[0] for text in texts]
    
    def embed_query(self, text):
        embedding = self.model.encode([text])[0]
        return embedding

# Initialisation des composants globaux
embeddings = BGEEmbeddings()
client = QdrantClient("localhost", port=6333)

# Une collection dans Qdrant est comme une table de base de données vectorielle
# qui stocke les embeddings (vecteurs) de nos documents avec leurs métadonnées.
# Chaque document sera transformé en un vecteur de 384 dimensions par le modèle BGE,
# et ce vecteur sera stocké dans la collection pour permettre la recherche sémantique.

# Création de la collection si elle n'existe pas
collection_name = "documents"
try:
    client.get_collection(collection_name)
except Exception:
    client.create_collection(
        collection_name=collection_name,
        vectors_config={
            "size": 384,  # Dimension des vecteurs générés par le modèle BGE
            "distance": "Cosine",  # Mesure de similarité utilisée pour la recherche
            "on_disk": True
        }
    )

# Let's inspect the QdrantVectorStore to see if it has any parameters that control upsert behavior
logger.info("Initializing QdrantVectorStore with the following parameters:")
logger.info(f"  client: {client}")
logger.info(f"  collection_name: {collection_name}")
logger.info(f"  embedding: {embeddings}")

# Check if there's a way to disable automatic deletion/upsert behavior
import inspect
logger.info("QdrantVectorStore parameters:")
logger.info(str(inspect.signature(QdrantVectorStore.__init__)))

# Initialize the vectorstore
vectorstore = QdrantVectorStore(
    client=client,
    collection_name=collection_name,
    embedding=embeddings
)

# Let's also add a wrapper around add_documents to see what's happening
original_add_documents = vectorstore.add_documents

def add_documents_with_logging(*args, **kwargs):
    logger.info(f"add_documents called with args: {args}")
    logger.info(f"add_documents called with kwargs: {kwargs}")
    try:
        result = original_add_documents(*args, **kwargs)
        logger.info("add_documents completed successfully")
        return result
    except Exception as e:
        logger.error(f"Error in add_documents: {e}")
        raise

vectorstore.add_documents = add_documents_with_logging

def get_all_text_files(folder):
    logger.info(f"Checking folder path: {folder}")
    logger.info(f"Folder path type: {type(folder)}")
    logger.info(f"Current working directory: {os.getcwd()}")
    
    # Try to list the directory contents to see if we can access it
    try:
        logger.info(f"Attempting to list directory contents of: {folder}")
        dir_contents = os.listdir(folder)
        logger.info(f"Directory contents: {dir_contents[:10]}{'...' if len(dir_contents) > 10 else ''}")
    except Exception as e:
        logger.error(f"Error listing directory contents: {str(e)}")
    
    if not os.path.exists(folder):
        logger.error(f"os.path.exists({folder}) returned False")
        # Try to diagnose why the path doesn't exist
        parent_dir = os.path.dirname(folder)
        logger.info(f"Parent directory: {parent_dir}")
        if os.path.exists(parent_dir):
            logger.info(f"Parent directory exists, but target folder doesn't")
        else:
            logger.info(f"Parent directory doesn't exist either")
        
        # Check if this is a network path
        if folder.startswith('\\\\') or folder.startswith('//') or ':' in folder:
            logger.info(f"This appears to be a network path or mapped drive")
            logger.info(f"Network paths may require special handling or permissions")
        
        raise ValueError(f"Folder path does not exist: {folder}")
    
    files = []
    for root, _, filenames in os.walk(folder):
        for filename in filenames:
            if filename.endswith('.pdf'):
                file_path = os.path.join(root, filename)
                if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                    files.append(file_path)
                else:
                    print(f"Skipping {file_path}: File is empty or does not exist")
    return files

def extract_patient_id(file_path):
    # Utilise une regex pour trouver l'ID juste après HMD_PJ\ ou HMD_PJ/
    match = re.search(r"HMD_PJ[\\/](\d+)", file_path)
    if match:
        return match.group(1)
    return None

def process_pdf(file_path, splitter, vectorstore):
    try:
        logger.info(f"Starting to process file: {file_path}")
        if not os.path.exists(file_path):
            logger.error(f"ERROR: File does not exist: {file_path}")
            return False
        if os.path.getsize(file_path) == 0:
            logger.error(f"ERROR: File is empty: {file_path}")
            return False

        logger.info(f"Loading PDF file: {file_path}")
        try:
            loader = PyPDFLoader(file_path)
            docs = loader.load()
        except Exception as pdf_error:
            logger.error(f"ERROR: Failed to load PDF {file_path}: {str(pdf_error)}")
            return False
            
        if not docs:
            logger.error(f"ERROR: No content extracted from: {file_path}")
            return False

        logger.info(f"Splitting document into chunks: {file_path}")
        try:
            # Découpage en chunks
            chunks = splitter.split_documents(docs)
            logger.info(f"Created {len(chunks)} chunks from {file_path}")
        except Exception as split_error:
            logger.error(f"ERROR: Failed to split document {file_path}: {str(split_error)}")
            return False

        # Extraction de l'ID patient
        patient_id = extract_patient_id(file_path)
        logger.info(f"Extracted patient ID: {patient_id} from {file_path}")

        # Ajout de métadonnées à chaque chunk
        for idx, chunk in enumerate(chunks):
            chunk.metadata["source"] = os.path.basename(file_path)  # Nom du fichier
            # chunk.metadata["file_path"] = file_path                 # Chemin complet
            chunk.metadata["chunk_id"] = idx                        # Index du chunk
            chunk.metadata["vectorized_at"] = datetime.utcnow().isoformat()  # Date de vectorisation
            chunk.metadata["patient_id"] = patient_id               # Identifiant patient
            # Ajout du numéro de page si disponible
            if "page" in chunk.metadata:
                chunk.metadata["page"] = chunk.metadata["page"]
            else:
                chunk.metadata["page"] = None

        logger.info(f"About to add {len(chunks)} chunks to vector store for {file_path}")
        try:
            # This is where delete operations might be happening internally
            vectorstore.add_documents(chunks)
            logger.info(f"Successfully added chunks to vector store for {file_path}")
        except Exception as vector_error:
            logger.error(f"ERROR: Failed to add chunks to vector store for {file_path}: {str(vector_error)}")
            return False
            
        logger.info(f"Successfully processed: {file_path}")
        return True

    except Exception as e:
        logger.error(f"ERROR: Unexpected error processing {file_path}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

class VectorizeAction(str, Enum):
    vectorize = "vectorize"
    amend = "amend"
    delete = "delete"
    fromScratch = "fromScratch"  # Action to delete and recreate the entire collection

@app.post("/vectorize")
async def vectorize_folder(
    folder_path: str,
    action: str = Query("vectorize", description="Action to perform: 'vectorize', 'amend', 'delete', or 'fromScratch'")
):
    # Convert string to enum
    try:
        action = VectorizeAction(action)
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid action: {action}. Must be one of: vectorize, amend, delete, fromScratch")
    """
    Vectorise, amende, supprime ou réinitialise les PDFs d'un dossier dans Qdrant selon l'action choisie.
    """
    logger.info(f"Vectorize endpoint called with folder_path={folder_path}, action={action}")
    
    # Log detailed information about the folder path
    logger.info(f"Detailed folder path information:")
    logger.info(f"  Raw folder_path: {folder_path}")
    logger.info(f"  Path type: {type(folder_path)}")
    logger.info(f"  Current working directory: {os.getcwd()}")
    
    # Check if this is a network path or mapped drive
    is_network_path = folder_path.startswith('\\\\') or folder_path.startswith('//') 
    is_mapped_drive = ':' in folder_path
    
    if is_network_path:
        logger.info(f"  This appears to be a UNC network path")
    if is_mapped_drive:
        logger.info(f"  This appears to be a mapped drive path")
    
    # Try to normalize the path
    try:
        normalized_path = os.path.normpath(folder_path)
        logger.info(f"  Normalized path: {normalized_path}")
    except Exception as e:
        logger.error(f"  Error normalizing path: {str(e)}")
    
    # Check if the path exists
    if not os.path.exists(folder_path):
        logger.error(f"ERROR: Folder path does not exist: {folder_path}")
        
        # Try to diagnose why the path doesn't exist
        try:
            parent_dir = os.path.dirname(folder_path)
            logger.info(f"  Parent directory: {parent_dir}")
            if os.path.exists(parent_dir):
                logger.info(f"  Parent directory exists, but target folder doesn't")
            else:
                logger.info(f"  Parent directory doesn't exist either")
                
            # If this is a network path, try some alternative formats
            if is_network_path or is_mapped_drive:
                logger.info(f"  Attempting alternative network path formats:")
                
                # Try with forward slashes
                alt_path = folder_path.replace('\\', '/')
                logger.info(f"  Alternative path (forward slashes): {alt_path}")
                logger.info(f"  Exists? {os.path.exists(alt_path)}")
                
                # If it's a UNC path, try with different numbers of backslashes
                if is_network_path:
                    # Remove any leading slashes and add exactly two
                    clean_path = folder_path.lstrip('\\').lstrip('/')
                    alt_path2 = f"\\\\{clean_path}"
                    logger.info(f"  Alternative path (clean UNC): {alt_path2}")
                    logger.info(f"  Exists? {os.path.exists(alt_path2)}")
        except Exception as e:
            logger.error(f"  Error during path diagnosis: {str(e)}")
        
        raise HTTPException(status_code=404, detail=f"Folder path does not exist: {folder_path}")

    files = get_all_text_files(folder_path)
    logger.info(f"Found {len(files)} PDF files in folder {folder_path}")
    
    if not files:
        logger.error(f"ERROR: No valid PDF files found in folder {folder_path}")
        raise HTTPException(status_code=404, detail="No valid PDF files found in the specified folder.")

    file_names = [os.path.basename(f) for f in files]
    logger.info(f"Processing files: {', '.join(file_names[:5])}{'...' if len(file_names) > 5 else ''}")

    # Handle fromScratch action - delete and recreate the entire collection
    if action == "fromScratch":
        logger.info(f"Action is fromScratch, deleting and recreating the entire collection: {collection_name}")
        try:
            # Delete the collection
            client.delete_collection(collection_name=collection_name)
            logger.info(f"Collection {collection_name} deleted successfully")
            
            # Recreate the collection
            client.create_collection(
                collection_name=collection_name,
                vectors_config={
                    "size": 384,  # Dimension des vecteurs générés par le modèle BGE
                    "distance": "Cosine",  # Mesure de similarité utilisée pour la recherche
                    "on_disk": True
                }
            )
            logger.info(f"Collection {collection_name} recreated successfully")
            
            # Reinitialize the vectorstore with the new collection
            global vectorstore, original_add_documents
            vectorstore = QdrantVectorStore(
                client=client,
                collection_name=collection_name,
                embedding=embeddings
            )
            logger.info(f"Vectorstore reinitialized with the new collection")
            
            # Reapply the wrapper around add_documents
            original_add_documents = vectorstore.add_documents
            vectorstore.add_documents = add_documents_with_logging
            logger.info(f"Reapplied logging wrapper to add_documents")
            
            # Continue with vectorization
            action = VectorizeAction.vectorize
            logger.info(f"Proceeding with vectorization after fromScratch")
        except Exception as e:
            logger.error(f"ERROR during fromScratch: {e}")
            raise HTTPException(status_code=500, detail=f"Error during fromScratch: {str(e)}")
    elif action in ["amend", "delete"]:
        logger.info(f"Action is {action}, should delete existing vectors before proceeding")
        # Suppression des vecteurs liés aux fichiers du dossier
        for file_name in file_names:
            filter = {
                "must": [
                    {"key": "source", "match": {"value": file_name}}
                ]
            }
            logger.info(f"Would delete vectors for file: {file_name} with filter: {filter}")
            # This line is commented out, but delete operations are still happening
            # client.delete(collection_name=collection_name, points_selector=models.Filter(**filter))

    if action == "delete":
        logger.info(f"Action is delete, returning with deleted_files={len(files)}")
        return {
            "status": "success",
            "deleted_files": len(files)
        }

    # Si on est en mode "vectorize", "amend" ou après un "reset", on vectorise les fichiers
    logger.info(f"Action is {action}, proceeding to vectorize files")
    splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
    processed_count = 0
    for file_path in files:
        logger.info(f"Processing file {processed_count+1}/{len(files)}: {file_path}")
        if process_pdf(file_path, splitter, vectorstore):
            processed_count += 1
        else:
            logger.error(f"Failed to process file: {file_path}")

    logger.info(f"Finished processing. Successfully processed {processed_count} out of {len(files)} files")
    return {
        "status": "success",
        "processed_files": processed_count,
        "total_files": len(files)
    }

@app.post("/search")
async def search_documents(
    query: SearchQuery,
    limit: Optional[int] = Query(100, description="Override the number of results to return")
    ):
    """
    Recherche sémantique dans les documents vectorisés
    """
   
    try:
        logger.info(f"Search request received: {query.query}, limit={limit}")
        results = vectorstore.similarity_search(
            query.query,
            k=limit
        )

        logger.info(f"Search returned {len(results)} results")
        
        # Ajouter une propriété file_link identique à file_path pour l'utiliser comme lien
        processed_results = []
        for doc in results:
            result = {
                "content": doc.page_content,
                "metadata": doc.metadata.copy()
            }
            
            # Si un chemin de fichier existe, l'utiliser directement comme lien
            if "file_path" in doc.metadata:
                # Utiliser le file_path tel quel comme lien
                result["metadata"]["file_link"] = doc.metadata["file_path"]
                logger.info(f"Added file_link: {result['metadata']['file_link']}")
            
            processed_results.append(result)
            
        return {
            "results": processed_results
        }
    except Exception as e:
        logger.error(f"Search error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    logger.info("Starting uvicorn server on 0.0.0.0:8087")
    uvicorn.run(app, host="0.0.0.0", port=8087)
