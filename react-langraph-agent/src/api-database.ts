import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get API URL from environment variable or use default
const SQL_API_BASE_URL = process.env.SQL_API_BASE_URL || 'http://10.1.16.55:8086';

/**
 * API-based database wrapper that connects to the FastAPI service
 */
export class SQLDatabase {
  private baseUrl: string;
  public dialect: string = 'mssql'; // Set to match your actual database type

  constructor(baseUrl: string = SQL_API_BASE_URL) {
    this.baseUrl = baseUrl;
    console.log(`Initialized API database connection to: ${this.baseUrl}`);
  }

  /**
   * Create a SQLDatabase from a URI
   */
  static fromURI(uri: string): SQLDatabase {
    // For compatibility with the original code, we'll ignore the URI
    // and use the SQL_API_BASE_URL environment variable instead
    return new SQLDatabase();
  }

  /**F
   * Checking query via API
   */
  async checkQuery(query: string): Promise<string[]> {
    try {
      console.log(`Checking query: ${query}`);
      const response = await axios.post(`${this.baseUrl}/query_checker`, {
        query: query
      });
      return response.data.issues || [];
    } catch (error: any) {
      console.error('Error checking query:', error.message);
      if (error.response) {
        console.error('API response:', error.response.data);
      }
      throw new Error(`Error checking query: ${error.message}`);
    }
  }

  /**
   * Get all table names from the database via API
   */
  async getTableNames(): Promise<string[]> {
    try {
      console.log(`Fetching tables from ${this.baseUrl}/tables`);
      const response = await axios.get(`${this.baseUrl}/tables`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching tables:', error.message);
      if (error.response) {
        console.error('API response:', error.response.data);
      }
      throw new Error(`Error fetching tables: ${error.message}`);
    }
  }

  /**
   * Get all table names from the database via API
   */
  async getContext(): Promise<string[]> {
    try {
      console.log(`Fetching tables from ${this.baseUrl}/context`);
      const response = await axios.get(`${this.baseUrl}/context`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching tables:', error.message);
      if (error.response) {
        console.error('API response:', error.response.data);
      }
      throw new Error(`Error fetching tables: ${error.message}`);
    }
  }
  /**
   * Get the schema for a specific table via API
   */
  async getTableInfo(tableName: string): Promise<string> {
    try {
      console.log(`Fetching schema for table ${tableName}`);
      const response = await axios.post(`${this.baseUrl}/schema`, {
        table_name: tableName
      });
      return response.data;
    } catch (error: any) {
      console.error(`Error fetching schema for table ${tableName}:`, error.message);
      if (error.response) {
        console.error('API response:', error.response.data);
      }
      throw new Error(`Error fetching schema for table ${tableName}: ${error.message}`);
    }
  }

  /**
   * Run a SQL query via API
   */
  async run(query: string): Promise<string> {
    try {
      console.log(`Executing query: ${query}`);

      // First check the query for common mistakes
      const checkResponse = await axios.post(`${this.baseUrl}/query_checker`, {
        query: query
      });

      const issues = checkResponse.data.issues;
      if (issues && issues.length > 0) {
        console.warn('Query checker found potential issues:', issues);
      }

      // Execute the query
      const response = await axios.post(`${this.baseUrl}/query`, {
        query: query
      });

      // Return the results as a JSON string
      return JSON.stringify(response.data, null, 2);
    } catch (error: any) {
      console.error('Error executing query:', error.message);
      if (error.response) {
        console.error('API response:', error.response.data);
      }
      return `Error executing query: ${error.response?.data?.detail || error.message}`;
    }
  }
}

/**
 * Initializes the API database connection
 * This function maintains the same interface as the original initializeDatabase
 * but connects to the API instead of directly to the database
 */
export async function initializeDatabase(): Promise<SQLDatabase> {
  try {
    const apiUrl = process.env.SQL_API_BASE_URL || 'http://10.1.16.55:8086';
    console.log(`Initializing API database connection to: ${apiUrl}`);

    const db = new SQLDatabase(apiUrl);

    // // Test connection by getting tables
    // console.log('Testing connection...');
    // const tables = await db.getTableNames();
    // console.log(`Available tables: ${tables.join(', ')}`);

    // // Run a sample query to verify connection
    // if (tables.length > 0) {
    //   const sampleTable = tables[0];
    //   const sampleQuery = `SELECT TOP 5 * FROM ${sampleTable}`;
    //   console.log(`Running sample query: ${sampleQuery}`);
    //   const result = await db.run(sampleQuery);
    //   console.log(`Sample query result: ${result}`);
    // }

    return db;
  } catch (error: any) {
    console.error('Error initializing API database connection:', error.message);
    throw error;
  }
}
