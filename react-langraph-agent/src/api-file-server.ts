import express from 'express';
import path from 'path';
import fileRepository from './file-repository.js';

const router = express.Router();

/**
 * Endpoint pour vérifier si un fichier existe
 * GET /api/files/exists?path=...
 */
router.get('/exists', async (req, res) => {
  try {
    const filePath = req.query.path as string;
    
    if (!filePath) {
      return res.status(400).json({ error: 'Le paramètre path est requis' });
    }
    
    const exists = await fileRepository.fileExists(filePath);
    res.json({ exists });
  } catch (error) {
    console.error('Erreur lors de la vérification du fichier:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la vérification du fichier' });
  }
});

/**
 * Endpoint pour obtenir les métadonnées d'un fichier
 * GET /api/files/metadata?path=...
 */
router.get('/metadata', async (req, res) => {
  try {
    const filePath = req.query.path as string;
    
    if (!filePath) {
      return res.status(400).json({ error: 'Le paramètre path est requis' });
    }
    
    const metadata = await fileRepository.getFileMetadata(filePath);
    
    if (!metadata) {
      return res.status(404).json({ error: 'Fichier non trouvé' });
    }
    
    res.json(metadata);
  } catch (error) {
    console.error('Erreur lors de la récupération des métadonnées:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération des métadonnées' });
  }
});

/**
 * Endpoint pour servir un fichier
 * GET /api/files/view?path=...
 */
router.get('/view', async (req, res) => {
  try {
    const filePath = req.query.path as string;
    
    if (!filePath) {
      return res.status(400).json({ error: 'Le paramètre path est requis' });
    }
    
    // Récupérer les métadonnées du fichier
    const metadata = await fileRepository.getFileMetadata(filePath);
    
    if (!metadata) {
      return res.status(404).json({ error: 'Fichier non trouvé' });
    }
    
    // Récupérer le contenu du fichier
    const content = await fileRepository.getFileContent(filePath);
    
    if (!content) {
      return res.status(500).json({ error: 'Erreur lors de la lecture du fichier' });
    }
    
    // Définir les en-têtes de réponse
    res.setHeader('Content-Type', metadata.contentType);
    res.setHeader('Content-Disposition', `inline; filename="${metadata.name}"`);
    
    // Envoyer le contenu du fichier
    res.send(content);
  } catch (error) {
    console.error('Erreur lors de la récupération du fichier:', error);
    res.status(500).json({ error: 'Erreur serveur lors de la récupération du fichier' });
  }
});

/**
 * Endpoint pour télécharger un fichier
 * GET /api/files/download?path=...
 */
router.get('/download', async (req, res) => {
  try {
    const filePath = req.query.path as string;
    
    if (!filePath) {
      return res.status(400).json({ error: 'Le paramètre path est requis' });
    }
    
    // Récupérer les métadonnées du fichier
    const metadata = await fileRepository.getFileMetadata(filePath);
    
    if (!metadata) {
      return res.status(404).json({ error: 'Fichier non trouvé' });
    }
    
    // Récupérer le contenu du fichier
    const content = await fileRepository.getFileContent(filePath);
    
    if (!content) {
      return res.status(500).json({ error: 'Erreur lors de la lecture du fichier' });
    }
    
    // Définir les en-têtes de réponse pour le téléchargement
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename="${metadata.name}"`);
    
    // Envoyer le contenu du fichier
    res.send(content);
  } catch (error) {
    console.error('Erreur lors du téléchargement du fichier:', error);
    res.status(500).json({ error: 'Erreur serveur lors du téléchargement du fichier' });
  }
});

/**
 * Endpoint pour obtenir une liste de fichiers en cache
 * GET /api/files/cache-status
 */
router.get('/cache-status', (req, res) => {
  try {
    // Cette route pourrait être utilisée pour afficher des statistiques sur le cache
    // ou pour des fins de débogage
    res.json({
      status: 'Cache actif',
      message: 'Le service de cache de fichiers est opérationnel'
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du statut du cache:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

export default router;
