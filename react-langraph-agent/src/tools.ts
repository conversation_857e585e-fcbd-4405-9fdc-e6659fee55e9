import { DynamicStructuredTool } from '@langchain/core/tools';
import { z } from 'zod';
import { SQLDatabase } from './api-database.js'; 
import { DocumentDatabase } from './api-document.js';  

/**
 * Creates a set of tools for interacting with a SQL database via API
 */
export function createSQLDatabaseTools(db: SQLDatabase) {
  // Tool to get business context - ALWAYS USE FIRST
  const generateContextQueryTool = new DynamicStructuredTool({
    name: 'sql_db_context_query',
    description: 'ALWAYS USE THIS TOOL FIRST. Retrieves the business context and pre-validated SQL queries that may be relevant to the user question.',
    schema: z.object({
      query: z.string().optional().describe('Optional. You can leave this empty.'),
    }),
    func: async ({ query }: { query?: string }) => {
      try {
        const context = await db.getContext();
        return context;
      } catch (error: any) {
        return `Error retrieving context: ${error.message}`;
      }
    },
  });

  // Tool to list all tables in the database
  const listTablesTool = new DynamicStructuredTool({
    name: 'sql_db_list_tables',
    description: 'Lists all tables in the SQL database. Use this instead of SQL queries like SHOW TABLES.',
    schema: z.object({}),
    func: async () => {
      try {
        const tables = await db.getTableNames();
        return `Available tables: ${tables.join(', ')}`;
      } catch (error: any) {
        return `Error listing tables: ${error.message}`;
      }
    },
  });

  // Tool to get schema for a specific table
  const getSchemaTool = new DynamicStructuredTool({
    name: 'sql_db_schema',
    description: 'Get the schema of a specific table in the SQL database. Use this instead of SQL queries against information_schema.',
    schema: z.object({
      table_name: z.string().describe('The name of the table to get the schema for'),
    }),
    func: async ({ table_name }: { table_name: string }) => {
      try {
        return await db.getTableInfo(table_name);
      } catch (error: any) {
        return `Error getting schema for table ${table_name}: ${error.message}`;
      }
    },
  });

  // Tool to run a SQL query
  const runQueryTool = new DynamicStructuredTool({
    name: 'sql_db_query',
    description: 'Execute a SQL query on the database and return the results. Do not use this for listing tables or getting schema information.',
    schema: z.object({
      query: z.string().describe('The SQL query to execute'),
    }),
    func: async ({ query }: { query: string }) => {
      try {
        return await db.run(query);
      } catch (error: any) {
        return `Error executing query: ${error.message}`;
      }
    },
  });

  // Tool to check a SQL query for common mistakes
  const checkQueryTool = new DynamicStructuredTool({
    name: 'sql_db_query_checker',
    description: 'Check a SQL query for common mistakes before executing it.',
    schema: z.object({
      query: z.string().describe('The SQL query to check'),
    }),
    func: async ({query}: { query: string }) => {
      try {
        // First check the query for common mistakes
        return await db.checkQuery(query);

        
      } catch (error: any) {
        return `Error checking query: ${error.message}`;
      }
    },
  });




  // Return tools in the order they should be used
  return [
    generateContextQueryTool,
    listTablesTool,
    getSchemaTool,
    runQueryTool,
    checkQueryTool,
  ];
}


export function createDocumentDatabaseTools(db: DocumentDatabase) {
 // Tool to check a SQL query for common mistakes
 const documentTool = new DynamicStructuredTool({
  name: 'document_search',
  description: `
  Recherche de documents PDF liés à la question de l'utilisateur. 
  CRITIQUE: Tu DOIS faire EXACTEMENT UNE SEULE recherche avec les termes EXACTS de la question de l'utilisateur.
  NE FAIS JAMAIS plusieurs recherches avec différents termes ou variations. Cette instruction est ABSOLUE.
  À utiliser pour toutes les recherches de documents.
  N'essaie JAMAIS de trouver ces informations dans la base SQL.
  Fourni la liste des documents trouvé.
  IMPORTANT: Pour chaque document trouvé, tu DOIS ABSOLUMENT inclure le chemin complet du fichier (file_path) des métadonnées pour permettre à l'utilisateur de télécharger le document directement. 
  Pour chaque document, affiche une ligne avec "Chemin du fichier: " suivi du chemin complet. Exemple: "Chemin du fichier: Y:\\HEMADIALYSE\\HMD_PJ\\000001870\\mail\\125277\\PJ_Ordonnance.pdf". Fais cela pour CHAQUE document,
    `,
  schema: z.object({
    query: z.string().describe(''),
  }),
  func: async ({query}: { query: string }) => {
    try {
      // First check the query for common mistakes
      return await db.getDocuments(query);

      
    } catch (error: any) {
      return `Error checking query: ${error.message}`;
    }
  },
});

return [documentTool];
}
