/**
 * Gestionnaire de protocole personnalisé pour ouvrir des fichiers
 */

/**
 * Enregistre un protocole personnalisé pour ouvrir des fichiers
 * Note: Cela nécessite une configuration côté système
 */
export function registerProtocolHandler() {
  if (navigator.registerProtocolHandler) {
    try {
      // Enregistrer le protocole 'web+fileopen'
      // Note: Les protocoles personnalisés doivent commencer par 'web+'
      navigator.registerProtocolHandler(
        'web+fileopen',
        `${window.location.origin}/open-file?path=%s`
      );
      console.log('Protocole file-open enregistré avec succès');
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du protocole:', error);
      return false;
    }
  }
  return false;
}

/**
 * Crée une URL avec le protocole personnalisé pour ouvrir un fichier
 * @param filePath Chemin du fichier à ouvrir
 * @returns URL avec le protocole personnalisé
 */
export function createFileOpenUrl(filePath: string): string {
  // Encoder le chemin du fichier pour l'URL
  const encodedPath = encodeURIComponent(filePath);
  return `file-open://${encodedPath}`;
}

/**
 * Tente d'ouvrir un fichier en utilisant différentes méthodes
 * @param filePath Chemin du fichier à ouvrir
 */
export function openFile(filePath: string): void {
  // Essayer d'abord avec le protocole personnalisé
  // Essayer d'abord avec le protocole file://
  try {
    // Normaliser le chemin pour le protocole file://
    const normalizedPath = filePath.replace(/\\/g, '/');
    window.open(`file:///${normalizedPath}`, '_blank');
  } catch (error) {
    console.error('Erreur lors de l\'ouverture avec le protocole personnalisé:', error);
    
    // Si cela échoue, essayer avec le protocole personnalisé
    const protocolUrl = createFileOpenUrl(filePath);
    window.location.href = protocolUrl;
  }
}
