/**
 * Utility functions for handling file paths
 */

/**
 * Normalizes a file path by converting backslashes to forward slashes
 * @param path The file path to normalize
 * @returns The normalized path
 */
export function normalizePath(path: string): string {
  return path.replace(/\\/g, '/');
}

/**
 * Extracts the filename from a file path
 * @param path The file path
 * @returns The filename
 */
export function getFileName(path: string): string {
  // Handle both forward and backslashes
  const normalizedPath = normalizePath(path);
  const parts = normalizedPath.split('/');
  return parts[parts.length - 1];
}

/**
 * Extracts the file extension from a file path
 * @param path The file path
 * @returns The file extension (without the dot)
 */
export function getFileExtension(path: string): string {
  const fileName = getFileName(path);
  const parts = fileName.split('.');
  return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
}

/**
 * Checks if a file path is a PDF
 * @param path The file path
 * @returns True if the file is a PDF
 */
export function isPdfFile(path: string): boolean {
  return getFileExtension(path) === 'pdf';
}

/**
 * Formats a file path for display by shortening it if it's too long
 * @param path The file path
 * @param maxLength The maximum length of the path
 * @returns The formatted path
 */
export function formatPathForDisplay(path: string, maxLength: number = 60): string {
  if (path.length <= maxLength) {
    return path;
  }
  
  const fileName = getFileName(path);
  const pathWithoutFile = path.substring(0, path.length - fileName.length);
  
  // If the filename itself is very long
  if (fileName.length > maxLength / 2) {
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
    const ext = fileName.substring(fileName.lastIndexOf('.'));
    
    // Shorten the name part but keep the extension
    const shortenedName = nameWithoutExt.substring(0, Math.max(3, maxLength / 3)) + '...';
    return shortenedName + ext;
  }
  
  // Otherwise, shorten the path part
  const shortenedPath = pathWithoutFile.substring(0, Math.max(3, maxLength - fileName.length - 3)) + '...';
  return shortenedPath + fileName;
}
