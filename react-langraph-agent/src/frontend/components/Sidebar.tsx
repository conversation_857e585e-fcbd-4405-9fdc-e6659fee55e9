import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash, faPen, faPlus, faComments, faRobot, faCog, faQuestionCircle } from '@fortawesome/free-solid-svg-icons';
import { getConversations, createConversation, getCurrentConversationData } from '../services/api.js';
import { deleteConversation, renameConversation } from '../services/localStorage.js';
import { Conversation } from '../types.js';

const Sidebar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState('');

  useEffect(() => {
    const fetchConversations = async () => {
      try {
        // Récupérer la conversation courante
        const current = await getCurrentConversationData();
        setCurrentConversation(current);
        
        // Récupérer les conversations récentes
        const data = await getConversations();
        setConversations(data);
      } catch (error) {
        console.error('Error fetching conversations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchConversations();
  }, [location.pathname]); // Rafraîchir quand l'URL change
  
  const handleNewConversation = async () => {
    try {
      const newConversation = await createConversation();
      setCurrentConversation(newConversation);
      
      // Rafraîchir la liste des conversations récentes
      const data = await getConversations();
      setConversations(data);
      
      navigate(`/chat/${newConversation.id}`);
    } catch (error) {
      console.error('Error creating new conversation:', error);
    }
  };
  
  const handleDeleteConversation = (e: React.MouseEvent, id: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    deleteConversation(id);
    setConversations(prev => prev.filter(c => c.id !== id));
  };
  
  const handleRenameClick = (e: React.MouseEvent, conversation: Conversation) => {
    e.preventDefault();
    e.stopPropagation();
    
    setEditingId(conversation.id);
    setEditTitle(conversation.title);
  };
  
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditTitle(e.target.value);
  };
  
  const handleTitleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (editingId && editTitle.trim()) {
      renameConversation(editingId, editTitle.trim());
      
      // Mettre à jour l'UI
      if (currentConversation && currentConversation.id === editingId) {
        setCurrentConversation({
          ...currentConversation,
          title: editTitle.trim()
        });
      } else {
        setConversations(prev => 
          prev.map(c => c.id === editingId ? { ...c, title: editTitle.trim() } : c)
        );
      }
      
      setEditingId(null);
    }
  };
  
  const handleTitleBlur = () => {
    if (editingId && editTitle.trim()) {
      renameConversation(editingId, editTitle.trim());
      
      // Mettre à jour l'UI
      if (currentConversation && currentConversation.id === editingId) {
        setCurrentConversation({
          ...currentConversation,
          title: editTitle.trim()
        });
      } else {
        setConversations(prev => 
          prev.map(c => c.id === editingId ? { ...c, title: editTitle.trim() } : c)
        );
      }
    }
    setEditingId(null);
  };
  
  // Formater la date et l'heure
  const formatDateTime = (dateString?: string) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    
    return `${day}/${month}/${year} ${hours}:${minutes}`;
  };
  
  return (
    <aside className="flex flex-col h-full p-3 text-center">
      <div className="mb-4">
        <h1 className="text-lg font-bold px-6 py-2 flex items-center">
          <FontAwesomeIcon icon={faRobot} className="mr-2 text-primary" />
          Hémadialyse Assistant
        </h1>
        <p className="text-xs text-gray-500 px-6 text-center">Votre assistant HMD !</p>
      </div>
      
      {/* Recent conversations */}
      <div className="flex-1 overflow-y-auto">
        <div className="space-y-2">
          {isLoading ? (
            <div className="text-center py-4">
              <div className="inline-block h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            </div>
          ) : conversations.length === 0 ? (
            <div className="text-center py-4 text-gray-500">
              Pas de conversations récentes
            </div>
          ) : (
            conversations.map((conversation) => (
              <div 
                key={conversation.id} 
                className="relative group"
              >
                {editingId === conversation.id ? (
                  <form onSubmit={handleTitleSubmit} className="p-2">
                    <input
                      type="text"
                      value={editTitle}
                      onChange={handleTitleChange}
                      onBlur={handleTitleBlur}
                      autoFocus
                      className="w-full p-2 text-sm border border-gray-300 rounded-xl"
                    />
                  </form>
                ) : (
                  <Link
                    to={`/chat/${conversation.id}`}
                    className={`bg-white rounded-xl shadow-sm border border-chat-border hover:border-primary transition-colors p-3 block`}
                  >
                    <div className="w-full">
                      <div className="flex justify-between items-center">
                        <span className="truncate pr-2 font-medium">{conversation.title}</span>
                        <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <button
                            onClick={(e) => handleRenameClick(e, conversation)}
                            className="text-gray-400 hover:text-primary"
                            aria-label="Rename conversation"
                          >
                            <FontAwesomeIcon icon={faPen} className="h-3 w-3" />
                          </button>
                          <button
                            onClick={(e) => handleDeleteConversation(e, conversation.id)}
                            className="text-gray-400 hover:text-red-500"
                            aria-label="Delete conversation"
                          >
                            <FontAwesomeIcon icon={faTrash} className="h-3 w-3" />
                          </button>
                        </div>
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>{conversation.messages?.length || 0} messages</span>
                        <span>{formatDateTime(conversation.createdAt)}</span>
                      </div>
                    </div>
                  </Link>
                )}
              </div>
            ))
          )}
        </div>
      </div>
      
      {/* Bottom actions */}
      <div className="mt-auto pt-4 flex justify-between items-center px-3">
        <button className="bg-white rounded-xl shadow-sm border border-chat-border p-2 text-gray-500 hover:border-primary transition-colors">
          <FontAwesomeIcon icon={faQuestionCircle} />
        </button>
        <button className="bg-white rounded-xl shadow-sm border border-chat-border p-2 text-gray-500 hover:border-primary transition-colors">
          <FontAwesomeIcon icon={faCog} />
        </button>
        <button 
          onClick={handleNewConversation}
          className="bg-white rounded-xl shadow-sm border border-chat-border px-4 py-2 text-xs font-medium text-gray-700 hover:border-primary transition-colors flex items-center"
        >
          <FontAwesomeIcon icon={faPlus} className="mr-1" />
          Nouveau Chat
        </button>
      </div>
    </aside>
  );
};

export default Sidebar;
