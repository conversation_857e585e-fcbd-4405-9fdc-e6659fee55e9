import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import FilePathLink from './FilePathLink.js';
import './ContentWithFileLinks.css';

interface ContentWithFileLinksProps {
  content: string;
}

const ContentWithFileLinks: React.FC<ContentWithFileLinksProps> = ({ content }) => {
  // Regex to match file paths like Y:\HEMADIALYSE\HMD_PJ\000001870\mail\134041\PJ_Ordonnance 22-02-2022.pdf
  // Allow for optional spaces around the colon
  const filePathRegex = /Chemin du fichier\s*:\s*([A-Z]:.+?\.pdf)/g;
  
  // Define the section type
  type ContentSection = {
    type: 'markdown' | 'filepath';
    content?: string;
    prefix?: string;
    path?: string;
    key: string;
  };

  // Split the content into sections: those with file paths and those without
  const sections: ContentSection[] = [];
  let currentText = '';
  let lastIndex = 0;
  
  // Create a copy of the regex to avoid issues with lastIndex
  const regex = new RegExp(filePathRegex);
  
  // Find all matches
  let match;
  while ((match = regex.exec(content)) !== null) {
    const [fullMatch, filePath] = match;
    const matchIndex = match.index;
    
    // Add text before the match to the current section
    if (matchIndex > lastIndex) {
      currentText += content.substring(lastIndex, matchIndex);
    }
    
    // If we have accumulated text, add it as a markdown section
    if (currentText.trim()) {
      sections.push({
        type: 'markdown',
        content: currentText,
        key: `md-${sections.length}`
      });
      currentText = '';
    }
    
    // Add the file path section
    // Extract the actual prefix from the full match to preserve original formatting
    const actualPrefix = fullMatch.substring(0, fullMatch.length - filePath.length);
    sections.push({
      type: 'filepath',
      prefix: actualPrefix,
      path: filePath,
      key: `file-${sections.length}`
    });
    
    lastIndex = matchIndex + fullMatch.length;
  }
  
  // Add any remaining text as a markdown section
  if (lastIndex < content.length) {
    currentText += content.substring(lastIndex);
    if (currentText.trim()) {
      sections.push({
        type: 'markdown',
        content: currentText,
        key: `md-${sections.length}`
      });
    }
  }
  
  return (
    <div className="content-with-file-links">
      {sections.map(section => {
        if (section.type === 'markdown') {
          return (
            <ReactMarkdown 
              key={section.key}
              remarkPlugins={[remarkGfm]}
            >
              {section.content}
            </ReactMarkdown>
          );
        } else {
          return (
            <div key={section.key} className="file-path-section">
              <span className="file-path-prefix">{section.prefix}</span>
              <FilePathLink filePath={section.path || ''} />
            </div>
          );
        }
      })}
    </div>
  );
};

export default ContentWithFileLinks;
