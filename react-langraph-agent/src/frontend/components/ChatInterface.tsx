import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import ContentWithFileLinks from './ContentWithFileLinks.js';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPaperPlane, faBars, faEdit, faShare, faEllipsisV, faImage, faSmile, faCode, faPaperclip, faRobot } from '@fortawesome/free-solid-svg-icons';
import { sendMessage, getConversation } from '../services/api.js';
import { renameConversation } from '../services/localStorage.js';
import { Message, Conversation } from '../types.js';
import Lottie from 'react-lottie';
import loaderAnimation from '../assets/loader.json' with { type: 'json' };
// Add type assertion for Lottie component
const LottieComponent = Lottie as any;


interface ChatInterfaceProps {
  toggleSidebar: () => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ toggleSidebar }) => {
  const { id } = useParams<{ id: string }>();
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [editTitle, setEditTitle] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const titleInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const lottieOptions = {
    loop: true,
    autoplay: true,
    animationData: loaderAnimation,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice'
    }
  };

  // Charger les messages de la conversation
  useEffect(() => {
    const loadConversation = async () => {
      if (id) {
        try {
          const conv = await getConversation(id);
          setConversation(conv);
          if (conv.messages) {
            setMessages(conv.messages);
          }
        } catch (error) {
          console.error('Error loading conversation:', error);
          navigate('/');
        }
      }
    };

    loadConversation();
  }, [id, navigate]);

  // Auto-resize textarea as user types
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [input]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus on title input when editing
  useEffect(() => {
    if (isEditingTitle && titleInputRef.current) {
      titleInputRef.current.focus();
    }
  }, [isEditingTitle]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleSubmit = async () => {
    if (!input.trim()) return;

    // Add user message to chat
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // Call the AI agent API
      const response = await sendMessage({
        message: userMessage.content,
        conversationId: id,
      });

      // Add assistant response to chat
      setMessages((prev) => [...prev, response]);
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditTitle = () => {
    if (conversation) {
      setEditTitle(conversation.title);
      setIsEditingTitle(true);
    }
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditTitle(e.target.value);
  };

  const handleTitleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (id && editTitle.trim()) {
      renameConversation(id, editTitle.trim());

      if (conversation) {
        setConversation({
          ...conversation,
          title: editTitle.trim()
        });
      }

      setIsEditingTitle(false);
    }
  };

  const handleTitleBlur = () => {
    if (id && editTitle.trim()) {
      renameConversation(id, editTitle.trim());

      if (conversation) {
        setConversation({
          ...conversation,
          title: editTitle.trim()
        });
      }
    }
    setIsEditingTitle(false);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Chat header */}
      <div className="p-4 border-b border-chat-border flex justify-between items-center">
        <div className="flex flex-col">
          <button
            onClick={toggleSidebar}
            className="text-gray-500 hover:text-primary mr-3 md:hidden"
          >
            <FontAwesomeIcon icon={faBars} className="h-5 w-5" />
          </button>
          {isEditingTitle ? (
            <form onSubmit={handleTitleSubmit} className="flex-1">
              <input
                ref={titleInputRef}
                type="text"
                value={editTitle}
                onChange={handleTitleChange}
                onBlur={handleTitleBlur}
                className="w-full p-1 text-lg font-medium border border-primary rounded-md"
              />
            </form>
          ) : (
            <>
              <h1 className="text-lg font-medium">{conversation?.title || 'Nouvelle conversation'}</h1>
              <span className="text-xs text-gray-500">{messages.length} messages</span>
            </>
          )}
        </div>
        <div className="flex space-x-2">
          <button
            className="nextchat-icon-button"
            onClick={handleEditTitle}
          >
            <FontAwesomeIcon icon={faEdit} />
          </button>
          <button className="nextchat-icon-button hidden">
            <FontAwesomeIcon icon={faShare} />
          </button>
          <button className="nextchat-icon-button hidden">
            <FontAwesomeIcon icon={faEllipsisV} />
          </button>
        </div>
      </div>

      {/* Chat messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-md">
              <div className="mb-4 flex justify-center">
                <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center text-primary">
                  <FontAwesomeIcon icon={faRobot} className="h-6 w-6" />
                </div>
              </div>
              <h2 className="text-xl font-medium mb-2"> Bonjour ! Comment puis je vous aider aujourd'hui?</h2>
              <p className="text-gray-500">
                Je suis votre assistant IA. Posez-moi n’importe quelle question ou dites-moi en quoi je peux vous aider.
              </p>
              <div className="text-xs text-gray-400 mt-2">
               ..aperçue de conversation..
              </div>
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-3xl chat-message ${message.role === 'user'
                  ? 'user-message'
                  : 'assistant-message'
                  }`}
              >
                <div className="message-markdown">
                  {message.content.includes("Chemin du fichier:") ? (
                    <ContentWithFileLinks content={message.content} />
                  ) : (
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {message.content}
                    </ReactMarkdown>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />

        {isLoading && (
          <div className="loading-animation">
            <LottieComponent options={lottieOptions} height={150} width={150} />
          </div>
        )}
      </div>

      {/* Chat input */}
      <div className="p-4 border-t border-chat-border">
        <div className="relative rounded-xl border border-primary bg-white overflow-hidden">
          <div className="flex space-x-2 px-3 py-2 border-b border-chat-border hidden">
            <button className="nextchat-icon-button text-gray-400">
              <FontAwesomeIcon icon={faImage} />
            </button>
            <button className="nextchat-icon-button text-gray-400">
              <FontAwesomeIcon icon={faSmile} />
            </button>
            <button className="nextchat-icon-button text-gray-400">
              <FontAwesomeIcon icon={faCode} />
            </button>
            <button className="nextchat-icon-button text-gray-400">
              <FontAwesomeIcon icon={faPaperclip} />
            </button>
          </div>
          <div className="chat-input-container">
            <textarea
              ref={textareaRef}
              value={input}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder="Appuyez sur Entrée pour envoyer, Maj + Entrée pour aller à la ligne, / pour rechercher des suggestions, : pour utiliser des commandes."
              className="chat-input w-full p-3 border-none focus:ring-0 focus:outline-none text-sm bg-white"
              rows={1}
            />
            <button
              onClick={handleSubmit}
              disabled={!input.trim() || isLoading}
              className={`absolute right-3 bottom-3 p-2 rounded-lg ${input.trim() && !isLoading
                ? 'bg-primary text-white'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                }`}
            >
              <FontAwesomeIcon icon={faPaperPlane} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
