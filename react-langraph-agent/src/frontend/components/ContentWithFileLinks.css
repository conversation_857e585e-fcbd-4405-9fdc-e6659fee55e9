.content-with-file-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.file-path-section {
  display: flex;
  align-items: baseline;
  margin: 0.5rem 0;
  flex-wrap: wrap;
}

.file-path-prefix {
  font-weight: 500;
  margin-right: 0.25rem;
  color: #555;
}

/* Style pour les sections de chemins de fichiers dans les messages de l'assistant */
.assistant-message .file-path-section {
  background-color: rgba(0, 102, 204, 0.05);
  padding: 0.5rem;
  border-radius: 0.25rem;
  border-left: 3px solid rgba(0, 102, 204, 0.3);
}

/* Style pour les sections de chemins de fichiers dans les messages de l'utilisateur */
.user-message .file-path-section {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.5rem;
  border-radius: 0.25rem;
  border-left: 3px solid rgba(255, 255, 255, 0.3);
}

/* Espacement entre les sections Markdown */
.content-with-file-links > * {
  margin-bottom: 0.5rem;
}

/* <PERSON><PERSON><PERSON> les marges excessives dans les paragraphes Markdown */
.content-with-file-links p:first-child {
  margin-top: 0;
}

.content-with-file-links p:last-child {
  margin-bottom: 0;
}
