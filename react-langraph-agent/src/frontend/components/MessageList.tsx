import React from 'react';
import { Message } from '../types.js';
import './MessageList.css';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import ContentWithFileLinks from './ContentWithFileLinks.js';

interface MessageListProps {
  messages: Message[];
}

const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  // Fonction pour rendre le contenu du message avec support des tableaux Markdown et liens de fichiers
  const renderMessageContent = (content: string) => {
    return (
      <div className="message-markdown">
        {content.includes("Chemin du fichier:") ? (
          <ContentWithFileLinks content={content} />
        ) : (
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {content}
          </ReactMarkdown>
        )}
      </div>
    );
  };

  return (
    <div className="message-list">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`message ${message.role === 'user' ? 'user-message' : 'assistant-message'}`}
        >
          <div className="message-role">
            {message.role === 'user' ? 'Vous' : 'Assistant'}
          </div>
          <div className="message-content">
            {renderMessageContent(message.content)}
          </div>
        </div>
      ))}
    </div>
  );
};

export default MessageList;
