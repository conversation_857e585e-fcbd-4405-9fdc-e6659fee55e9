import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExclamationTriangle, faCopy, faFileAlt, faDownload, faExternalLinkAlt } from '@fortawesome/free-solid-svg-icons';
import { getFileName } from '../utils/filePathUtils.js';
import { useNavigate } from 'react-router-dom';
import { checkFileExists, getFileViewUrl, getFileDownloadUrl } from '../services/fileService.js';

interface PdfViewerProps {
  filePath: string;
  width?: string | number;
  height?: string | number;
  className?: string;
}

/**
 * Composant pour afficher un PDF directement dans l'application
 */
const PdfViewer: React.FC<PdfViewerProps> = ({ 
  filePath, 
  width = '100%', 
  height = '100%',
  className = '' 
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [fileExists, setFileExists] = useState<boolean | null>(null);
  const [isCheckingFile, setIsCheckingFile] = useState(true);
  const navigate = useNavigate();
  
  // URL pour accéder au fichier via l'API
  const fileViewUrl = getFileViewUrl(filePath);
  const fileDownloadUrl = getFileDownloadUrl(filePath);

  // Vérifier si le fichier existe
  useEffect(() => {
    const verifyFileExists = async () => {
      try {
        setIsCheckingFile(true);
        const exists = await checkFileExists(filePath);
        setFileExists(exists);
        if (!exists) {
          setError('Le fichier n\'existe pas sur le serveur');
        }
      } catch (error) {
        console.error('Erreur lors de la vérification du fichier:', error);
        setFileExists(false);
        setError(`Erreur lors de la vérification du fichier: ${error}`);
      } finally {
        setIsCheckingFile(false);
      }
    };
    
    verifyFileExists();
  }, [filePath]);

  // Fonction pour copier le chemin dans le presse-papiers
  const copyToClipboard = () => {
    navigator.clipboard.writeText(filePath)
      .then(() => {
        alert('Chemin du fichier copié dans le presse-papiers');
      })
      .catch(err => {
        console.error('Erreur lors de la copie dans le presse-papiers:', err);
        alert(`Impossible de copier le chemin. Erreur: ${err.message}`);
      });
  };

  // Retourner à la page précédente
  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div className="pdf-viewer">
      {isCheckingFile ? (
        <div className="loading-state p-4 text-center">
          <p>Vérification du fichier en cours...</p>
        </div>
      ) : fileExists ? (
        <div className="pdf-container">
          <div className="pdf-toolbar bg-gray-100 p-2 flex justify-between items-center mb-4 rounded">
            <div className="file-info">
              <span className="font-medium">{getFileName(filePath)}</span>
            </div>
            <div className="actions flex space-x-2">
              <a
                href={fileDownloadUrl}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                title="Télécharger le fichier"
              >
                <FontAwesomeIcon icon={faDownload} className="mr-2 h-4 w-4" />
                Télécharger
              </a>
              <button
                onClick={copyToClipboard}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                title="Copier le chemin du fichier"
              >
                <FontAwesomeIcon icon={faCopy} className="mr-2 h-4 w-4" />
                Copier le chemin
              </button>
              <a
                href={fileViewUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                title="Ouvrir dans un nouvel onglet"
              >
                <FontAwesomeIcon icon={faExternalLinkAlt} className="mr-2 h-4 w-4" />
                Nouvel onglet
              </a>
            </div>
          </div>
          
          <iframe
            src={fileViewUrl}
            width={width}
            height={height}
            style={{ border: 'none' }}
            title={`PDF: ${filePath}`}
            className="w-full h-full border rounded flex-1"
          />
        </div>
      ) : (
        <div className="error-state bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0 pt-0.5">
              <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-400 h-5 w-5" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Fichier non trouvé
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>
                  Le fichier demandé n'a pas pu être trouvé sur le serveur.
                </p>
                <p className="mt-2">
                  <strong>Fichier :</strong> {getFileName(filePath)}
                </p>
                <p className="mt-2">
                  <strong>Chemin complet :</strong> {filePath}
                </p>
                {error && (
                  <p className="mt-2">
                    <strong>Erreur :</strong> {error}
                  </p>
                )}
              </div>
              <div className="mt-4 flex space-x-3">
                <button
                  onClick={handleBack}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Retour
                </button>
                <button
                  onClick={copyToClipboard}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <FontAwesomeIcon icon={faCopy} className="mr-2 h-4 w-4" />
                  Copier le chemin
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PdfViewer;
