.file-path-link-container {
  position: relative;
  display: inline-block;
  max-width: 100%;
}

.file-path-link {
  display: inline-block;
  color: #0066cc;
  text-decoration: none;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(0, 102, 204, 0.1);
  transition: all 0.2s ease;
  word-break: break-all;
  border: 1px solid rgba(0, 102, 204, 0.2);
  margin: 2px 0;
  cursor: pointer;
}

.file-path-link:hover {
  background-color: rgba(0, 102, 204, 0.2);
  text-decoration: underline;
  color: #004c99;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-path-link:active {
  background-color: rgba(0, 102, 204, 0.3);
  transform: translateY(1px);
}

/* Add a small icon to indicate it's a file */
.file-path-link::before {
  content: "📄 ";
  margin-right: 6px;
}

/* Tooltip styles */
.file-path-tooltip {
  position: absolute;
  bottom: 100%;
  left: 0;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  white-space: nowrap;
  margin-bottom: 5px;
  pointer-events: none;
  opacity: 0;
  transform: translateY(5px);
  transition: opacity 0.3s, transform 0.3s;
}

.file-path-link-container:hover .file-path-tooltip {
  opacity: 1;
  transform: translateY(0);
}

/* Style for PDF files specifically */
.file-path-link[title*=".pdf"]::before {
  content: "📑 ";
  color: #e74c3c;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .file-path-link {
    background-color: rgba(0, 102, 204, 0.2);
    color: #4da6ff;
    border-color: rgba(77, 166, 255, 0.3);
  }
  
  .file-path-link:hover {
    background-color: rgba(0, 102, 204, 0.3);
    color: #66b3ff;
  }
}
