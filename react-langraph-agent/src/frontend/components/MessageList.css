/* Styles existants */

/* Styles pour les tableaux Markdown */
.message-markdown table {
  border-collapse: collapse;
  margin: 15px 0;
  width: 100%;
  font-size: 0.6875em;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  display: table; /* Assure que la table est rendue comme un élément table */
  table-layout: fixed; /* Améliore le rendu des colonnes */
}

.message-markdown th,
.message-markdown td {
  padding: 12px 15px;
  text-align: left;
  word-wrap: break-word; /* Permet aux longs mots de se couper */
}

.message-markdown th {
  background-color: #4a6fa5;
  color: white;
  font-weight: bold;
}

.message-markdown td {
  border-bottom: 1px solid #dddddd;
}

/* Assurez-vous que les cellules du tableau ont une largeur minimale */
.message-markdown th:first-child,
.message-markdown td:first-child {
  min-width: 100px;
}

/* Styles pour les lignes alternées */
.message-markdown tr:nth-of-type(even) {
  background-color: #f3f3f3;
}

/* Effet de survol */
.message-markdown tr:hover {
  background-color: #f1f7ff;
}

/* Styles spécifiques pour les tableaux dans les messages utilisateur (fond sombre) */
.user-message .message-markdown table {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.user-message .message-markdown th {
  background-color: #2a4d7d; /* Plus foncé pour contraster avec le fond sombre */
  color: white;
}

.user-message .message-markdown td {
  border-bottom: 1px solid #5a7ba8;
  color: white; /* Assure que le texte est visible sur fond sombre */
}

.user-message .message-markdown tr:nth-of-type(even) {
  background-color: rgba(255, 255, 255, 0.1); /* Légère transparence pour les lignes alternées */
}

.user-message .message-markdown tr:hover {
  background-color: rgba(255, 255, 255, 0.2); /* Effet de survol plus visible */
}
