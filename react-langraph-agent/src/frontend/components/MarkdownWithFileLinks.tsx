import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import FilePathLink from './FilePathLink.js';

interface MarkdownWithFileLinksProps {
  content: string;
}

const MarkdownWithFileLinks: React.FC<MarkdownWithFileLinksProps> = ({ content }) => {
  // Regex to match file paths like Y:\HEMADIALYSE\HMD_PJ\000001870\mail\134041\PJ_Ordonnance 22-02-2022.pdf
  // This regex handles paths with spaces and special characters in filenames
  const filePathRegex = /(Chemin du fichier: )([A-Z]:.+?\.pdf)/g;
  

  // For debugging
  const logMatches = (content: string) => {
    console.log("Analyzing content for file paths:", content);
    let match;
    filePathRegex.lastIndex = 0;
    while ((match = filePathRegex.exec(content)) !== null) {
      console.log("Found match:", match[0]);
      console.log("Prefix:", match[1]);
      console.log("File path:", match[2]);
    }
  };

  // Process the content directly without using markers
  const processContent = (content: string): React.ReactNode[] => {
    // Log matches for debugging
    logMatches(content);
    const elements: React.ReactNode[] = [];
    let lastIndex = 0;
    let match;
    
    // Reset the regex for each execution
    filePathRegex.lastIndex = 0;
    
    while ((match = filePathRegex.exec(content)) !== null) {
      const [fullMatch, prefix, filePath] = match;
      const matchIndex = match.index;
      
      // Add text before the match
      if (matchIndex > lastIndex) {
        elements.push(content.substring(lastIndex, matchIndex));
      }
      
      // Add the prefix
      elements.push(prefix);
      
      // Add the file path as a link
      elements.push(<FilePathLink key={`file-${matchIndex}`} filePath={filePath} />);
      
      lastIndex = matchIndex + fullMatch.length;
    }
    
    // Add any remaining text
    if (lastIndex < content.length) {
      elements.push(content.substring(lastIndex));
    }
    
    return elements;
  };

  // Custom renderer for paragraphs to handle file paths
  const renderers = {
    p: (props: { children?: React.ReactNode }) => {
      const { children } = props;
      
      // If children is a string, process it for file paths
      if (typeof children === 'string') {
        const processedContent = processContent(children);
        return <p>{processedContent}</p>;
      }
      
      // Otherwise, return as is
      return <p>{children}</p>;
    }
  };

  return (
    <div className="message-markdown">
      <ReactMarkdown 
        remarkPlugins={[remarkGfm]}
        components={renderers}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownWithFileLinks;
