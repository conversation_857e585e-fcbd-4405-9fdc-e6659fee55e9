import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExternalLinkAlt, faEye, faCopy, faDownload } from '@fortawesome/free-solid-svg-icons';
import './FilePathLink.css';
import './FilePathMenu.css';
import { normalizePath, getFileName, formatPathForDisplay } from '../utils/filePathUtils.js';
import { openFileInNewTab, downloadFile } from '../services/fileService.js';

interface FilePathLinkProps {
  filePath: string;
}

const FilePathLink: React.FC<FilePathLinkProps> = ({ filePath }) => {
  const [showTooltip, setShowTooltip] = useState(false);
  
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // Gérer le clic sur le lien
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setShowMenu(true);
  };

  // Gérer le clic en dehors du menu
  const handleClickOutside = (e: MouseEvent) => {
    if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
      setShowMenu(false);
    }
  };

  // Ajouter/supprimer l'écouteur d'événements pour les clics en dehors du menu
  React.useEffect(() => {
    if (showMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMenu]);

  // Ouvrir le fichier directement via l'API
  const handleOpenDirect = () => {
    setShowMenu(false);
    
    // Demander confirmation avant d'ouvrir
    if (!window.confirm(`Voulez-vous ouvrir ce fichier?\n\n${filePath}`)) {
      return;
    }
    
    // Utiliser le service de fichiers pour ouvrir le fichier
    console.log('OpenDirect - Utilisation du service de fichiers:', filePath);
    
    try {
      openFileInNewTab(filePath);
    } catch (error) {
      console.error('Error opening file:', error);
      alert(`Impossible d'ouvrir le fichier: ${filePath}\n\nVeuillez vérifier que le fichier existe et que vous avez les permissions nécessaires.`);
    }
  };

  // Ouvrir le fichier dans la visionneuse PDF intégrée
  const handleOpenInViewer = () => {
    setShowMenu(false);
    
    // Utiliser directement le chemin du fichier
    console.log('OpenInViewer - Utilisation directe du chemin:', filePath);
    
    navigate(`/view-pdf?path=${encodeURIComponent(filePath)}`);
  };
  
  // Télécharger le fichier
  const handleDownload = () => {
    setShowMenu(false);
    
    // Utiliser le service de fichiers pour télécharger le fichier
    console.log('Download - Utilisation du service de fichiers:', filePath);
    
    try {
      downloadFile(filePath);
    } catch (error) {
      console.error('Error downloading file:', error);
      alert(`Impossible de télécharger le fichier: ${filePath}\n\nVeuillez vérifier que le fichier existe et que vous avez les permissions nécessaires.`);
    }
  };

  // Copier le chemin du fichier dans le presse-papiers
  const handleCopyPath = () => {
    setShowMenu(false);
    navigator.clipboard.writeText(filePath)
      .then(() => {
        alert('Chemin du fichier copié dans le presse-papiers');
      })
      .catch(err => {
        console.error('Erreur lors de la copie dans le presse-papiers:', err);
        alert(`Impossible de copier le chemin. Erreur: ${err.message}`);
      });
  };
  
  const handleMouseEnter = () => {
    setShowTooltip(true);
  };
  
  const handleMouseLeave = () => {
    setShowTooltip(false);
  };

  return (
    <div className="file-path-link-container">
      <a 
        href="#" 
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="file-path-link"
        title={`Options pour ${filePath}`}
      >
        {formatPathForDisplay(filePath)}
      </a>
      {showTooltip && !showMenu && (
        <div className="file-path-tooltip">
          Cliquez pour les options d'ouverture: {getFileName(filePath)}
        </div>
      )}
      {showMenu && (
        <div ref={menuRef} className="file-path-menu">
          <div className="menu-item open-direct" onClick={handleOpenDirect}>
            <span className="menu-item-icon">
              <FontAwesomeIcon icon={faExternalLinkAlt} />
            </span>
            <span className="menu-item-text">Ouvrir dans un nouvel onglet</span>
          </div>
          <div className="menu-item open-viewer" onClick={handleOpenInViewer}>
            <span className="menu-item-icon">
              <FontAwesomeIcon icon={faEye} />
            </span>
            <span className="menu-item-text">Ouvrir dans la visionneuse</span>
          </div>
          <div className="menu-item download" onClick={handleDownload}>
            <span className="menu-item-icon">
              <FontAwesomeIcon icon={faDownload} />
            </span>
            <span className="menu-item-text">Télécharger</span>
          </div>
          <div className="menu-separator"></div>
          <div className="menu-item copy-path" onClick={handleCopyPath}>
            <span className="menu-item-icon">
              <FontAwesomeIcon icon={faCopy} />
            </span>
            <span className="menu-item-text">Copier le chemin</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default FilePathLink;
