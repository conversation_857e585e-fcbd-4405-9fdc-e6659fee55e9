@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply text-xs;
  }
  
  h1 {
    @apply text-2xl;
  }
  
  h2 {
    @apply text-xl;
  }
  
  h3 {
    @apply text-lg;
  }
}

.pathway-extreme {
  font-family: "Pathway Extreme", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

:root {
  font-family: "Pathway Extreme", Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  background-color: theme('colors.nextchat-bg');
}

body {
  font-family: "Pathway Extreme", sans-serif;
  font-optical-sizing: auto;
  font-style: normal;
  font-variation-settings: "wdth" 100;
  margin: 0;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  height: 100vh;
}

.chat-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.chat-input {
  resize: none;
  min-height: 56px;
  max-height: 200px;
  overflow-y: auto;
  display: flex;
  align-items: center;
  border-radius: 0.75rem;
  padding-right: 3rem;
}

@layer components {
  .sidebar-item {
    @apply flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-xl transition-colors;
  }

  .sidebar-item.active {
    @apply bg-selected-chat font-medium;
  }
  
  .chat-message {
    @apply p-4 rounded-xl;
  }
  
  .user-message {
    @apply bg-white text-gray-800 border border-chat-border;
  }
  
  .assistant-message {
    @apply bg-white text-gray-800 border border-chat-border;
  }
  
  .nextchat-button {
    @apply rounded-xl border border-chat-border bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:border-primary transition-colors;
  }
  
  .nextchat-icon-button {
    @apply rounded-full p-2 text-gray-500 hover:bg-gray-100 transition-colors;
  }
  
  .pdf-viewer {
    @apply w-full h-full flex flex-col;
  }
  
  .pdf-viewer .pdf-container {
    @apply flex-1 flex flex-col;
  }
  
  .pdf-viewer iframe {
    @apply flex-1 min-h-[500px] h-full;
  }
}
