import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import PdfViewer from '../components/PdfViewer.js';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft, faExternalLinkAlt, faDownload } from '@fortawesome/free-solid-svg-icons';
import { getFileName } from '../utils/filePathUtils.js';
import { openFileInNewTab } from '../services/fileService.js';

/**
 * Page d'affichage de PDF
 */
const PdfViewerPage: React.FC = () => {
  const [filePath, setFilePath] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    // Extraire le chemin du fichier de l'URL
    const params = new URLSearchParams(location.search);
    const path = params.get('path');

    if (!path) {
      setError('Aucun chemin de fichier spécifié');
      return;
    }

    // Décoder le chemin du fichier
    try {
      const decodedPath = decodeURIComponent(path);
      setFilePath(decodedPath);
    } catch (error) {
      console.error('Erreur lors du décodage du chemin du fichier:', error);
      setError(`Impossible de décoder le chemin du fichier: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [location]);

  // Fonction pour revenir à la page précédente
  const handleBack = () => {
    navigate(-1);
  };

  // Fonction pour ouvrir le fichier dans une nouvelle fenêtre
  const handleOpenExternal = () => {
    if (filePath) {
      openFileInNewTab(filePath);
    }
  };

  return (
    <div className="pdf-viewer-page">
      <div className="header bg-white shadow-sm p-4 flex items-center justify-between">
        <div className="flex items-center">
          <button 
            onClick={handleBack}
            className="mr-4 p-2 rounded-full hover:bg-gray-100"
            title="Retour"
          >
            <FontAwesomeIcon icon={faArrowLeft} />
          </button>
          <h1 className="text-xl font-medium">
            {filePath ? getFileName(filePath) : 'Visionneuse PDF'}
          </h1>
        </div>
        
        {filePath && (
          <div className="actions">
            <button 
              onClick={handleOpenExternal}
              className="ml-2 p-2 rounded-full hover:bg-gray-100"
              title="Ouvrir dans une nouvelle fenêtre"
            >
              <FontAwesomeIcon icon={faExternalLinkAlt} />
            </button>
          </div>
        )}
      </div>
      
      <div className="content p-4">
        {error ? (
          <div className="error-message bg-red-100 text-red-700 p-4 rounded">
            {error}
          </div>
        ) : filePath ? (
          <div className="pdf-container bg-white rounded shadow-sm p-4 h-[calc(100vh-180px)] flex flex-col">
            <PdfViewer 
              filePath={filePath}
            />
          </div>
        ) : (
          <div className="loading">Chargement en cours...</div>
        )}
      </div>
    </div>
  );
};

export default PdfViewerPage;
