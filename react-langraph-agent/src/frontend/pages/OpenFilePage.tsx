import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { openFileInNewTab } from '../services/fileService.js';

/**
 * Page d'ouverture de fichier
 * Cette page est utilisée par le gestionnaire de protocole personnalisé
 */
const OpenFilePage: React.FC = () => {
  const [filePath, setFilePath] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const location = useLocation();

  useEffect(() => {
    // Extraire le chemin du fichier de l'URL
    const params = new URLSearchParams(location.search);
    const path = params.get('path');

    if (!path) {
      setError('Aucun chemin de fichier spécifié');
      setStatus('error');
      return;
    }

    // Décoder le chemin du fichier
    try {
      // Le chemin peut être encodé dans l'URL avec le format web+fileopen://...
      let decodedPath = path;
      if (path.startsWith('web+fileopen://')) {
        decodedPath = decodeURIComponent(path.substring('web+fileopen://'.length));
      }
      
      setFilePath(decodedPath);
      
      // Tenter d'ouvrir le fichier
      openFileInNewTab(decodedPath);
      
      setStatus('success');
    } catch (error) {
      console.error('Erreur lors du décodage ou de l\'ouverture du fichier:', error);
      setError(`Impossible d'ouvrir le fichier: ${error instanceof Error ? error.message : String(error)}`);
      setStatus('error');
    }
  }, [location]);


  return (
    <div className="open-file-page">
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Ouverture de fichier</h1>
        
        {status === 'loading' && (
          <div className="loading">Chargement en cours...</div>
        )}
        
        {status === 'success' && (
          <div className="success">
            <p className="mb-2">Tentative d'ouverture du fichier :</p>
            <div className="file-path p-2 bg-gray-100 rounded">{filePath}</div>
            <p className="mt-4">
              Si le fichier ne s'ouvre pas automatiquement, cliquez sur le bouton ci-dessous :
            </p>
            <button 
              className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              onClick={() => filePath && openFileInNewTab(filePath)}
            >
              Ouvrir le fichier
            </button>
          </div>
        )}
        
        {status === 'error' && (
          <div className="error">
            <p className="text-red-500">{error}</p>
            <p className="mt-4">
              Pour des raisons de sécurité, les navigateurs peuvent bloquer l'ouverture directe de fichiers.
              Vous pouvez essayer d'accéder au fichier manuellement via l'Explorateur de fichiers.
            </p>
            {filePath && (
              <div className="mt-2">
                <p>Chemin du fichier :</p>
                <div className="file-path p-2 bg-gray-100 rounded">{filePath}</div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default OpenFilePage;
