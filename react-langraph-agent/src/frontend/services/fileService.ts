/**
 * Service pour gérer les opérations sur les fichiers
 */

// URL de l'API backend
const API_URL = 'http://10.1.16.55:3005';

// Interface pour les métadonnées de fichier
interface FileMetadata {
  name: string;
  size: number;
  lastModified: Date;
  extension: string;
  path: string;
  contentType: string;
}

/**
 * Vérifie si un fichier existe
 * @param filePath Chemin du fichier
 * @returns true si le fichier existe, false sinon
 */
export async function checkFileExists(filePath: string): Promise<boolean> {
  try {
    const response = await fetch(`${API_URL}/api/files/exists?path=${encodeURIComponent(filePath)}`);
    
    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }
    
    const data = await response.json();
    return data.exists;
  } catch (error) {
    console.error('Erreur lors de la vérification du fichier:', error);
    return false;
  }
}

/**
 * Récupère les métadonnées d'un fichier
 * @param filePath Chemin du fichier
 * @returns Métadonn<PERSON> du fichier ou null si le fichier n'existe pas
 */
export async function getFileMetadata(filePath: string): Promise<FileMetadata | null> {
  try {
    const response = await fetch(`${API_URL}/api/files/metadata?path=${encodeURIComponent(filePath)}`);
    
    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Erreur HTTP: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Erreur lors de la récupération des métadonnées:', error);
    return null;
  }
}

/**
 * Génère une URL pour visualiser un fichier
 * @param filePath Chemin du fichier
 * @returns URL pour visualiser le fichier
 */
export function getFileViewUrl(filePath: string): string {
  return `${API_URL}/api/files/view?path=${encodeURIComponent(filePath)}`;
}

/**
 * Génère une URL pour télécharger un fichier
 * @param filePath Chemin du fichier
 * @returns URL pour télécharger le fichier
 */
export function getFileDownloadUrl(filePath: string): string {
  return `${API_URL}/api/files/download?path=${encodeURIComponent(filePath)}`;
}

/**
 * Ouvre un fichier dans un nouvel onglet
 * @param filePath Chemin du fichier
 */
export function openFileInNewTab(filePath: string): void {
  const url = getFileViewUrl(filePath);
  window.open(url, '_blank');
}

/**
 * Télécharge un fichier
 * @param filePath Chemin du fichier
 */
export function downloadFile(filePath: string): void {
  const url = getFileDownloadUrl(filePath);
  window.open(url, '_blank');
}

/**
 * Vérifie le statut du cache de fichiers
 * @returns Statut du cache
 */
export async function checkCacheStatus(): Promise<any> {
  try {
    const response = await fetch(`${API_URL}/api/files/cache-status`);
    
    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Erreur lors de la vérification du statut du cache:', error);
    return { status: 'Erreur', message: 'Impossible de vérifier le statut du cache' };
  }
}
