// API service for communicating with the backend

interface SendMessageRequest {
  message: string;
  conversationId?: string;
}

interface SendMessageResponse {
  id: string;
  role: 'assistant';
  content: string;
}

import { Message, Conversation } from '../types.js';
import { 
  getStoredConversations, 
  getCurrentConversation,
  saveConversation, 
  saveCurrentConversation,
  createNewConversation,
  setConversationAsCurrent
} from './localStorage.js';

// Backend API URL
const API_URL = 'http://10.1.16.55:3005';

export const sendMessage = async (
  request: SendMessageRequest
): Promise<SendMessageResponse> => {
  try {
    // Send the message to the backend API
    const response = await fetch(`${API_URL}/api/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message: request.message }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get response from the server');
    }
    
    // Parse the response
    const data = await response.json();
    
    // Si un ID de conversation est fourni, mettons à jour la conversation
    if (request.conversationId) {
      // Vérifier si c'est la conversation courante
      const currentConversation = getCurrentConversation();
      
      if (currentConversation && currentConversation.id === request.conversationId) {
        // Ajouter le message utilisateur à la conversation
        currentConversation.messages.push({
          id: Date.now().toString(),
          role: 'user',
          content: request.message
        });
        
        // Ajouter la réponse de l'assistant à la conversation
        currentConversation.messages.push(data);
        
        // Mettre à jour le titre si c'est la première interaction et que le titre n'a pas été modifié
        if (currentConversation.messages.length === 2 && currentConversation.title === 'Nouvelle conversation') {
          currentConversation.title = request.message.substring(0, 30) + (request.message.length > 30 ? '...' : '');
        }
        
        saveCurrentConversation(currentConversation);
        
        // Sauvegarder également dans les conversations récentes
        saveConversation(currentConversation);
      } else {
        // Chercher dans les récents
        const conversations = getStoredConversations();
        const conversation = conversations.find(c => c.id === request.conversationId);
        
        if (conversation) {
          // Définir cette conversation comme courante
          setConversationAsCurrent(conversation.id);
          
          // Ajouter le message utilisateur et la réponse
          conversation.messages.push({
            id: Date.now().toString(),
            role: 'user',
            content: request.message
          });
          
          conversation.messages.push(data);
          
          // Mettre à jour le titre si c'est la première interaction et que le titre n'a pas été modifié
          if (conversation.messages.length === 2 && conversation.title === 'Nouvelle conversation') {
            conversation.title = request.message.substring(0, 30) + (request.message.length > 30 ? '...' : '');
          }
          
          saveCurrentConversation(conversation);
          
          // Sauvegarder également dans les conversations récentes
          saveConversation(conversation);
        }
      }
    } else {
      // Si aucun ID de conversation n'est fourni, créer une nouvelle conversation
      const newConversation = createNewConversation();
      
      // Ajouter le message utilisateur à la conversation
      newConversation.messages.push({
        id: Date.now().toString(),
        role: 'user',
        content: request.message
      });
      
      // Ajouter la réponse de l'assistant à la conversation
      newConversation.messages.push(data);
      
      // Définir le titre comme la première question de l'utilisateur
      newConversation.title = request.message.substring(0, 30) + (request.message.length > 30 ? '...' : '');
      
      saveCurrentConversation(newConversation);
      
      // Sauvegarder également dans les conversations récentes
      saveConversation(newConversation);
    }
    
    return data;
  } catch (error: any) {
    console.error('Error sending message:', error);
    return {
      id: Date.now().toString(),
      role: 'assistant',
      content: `Désolé, une erreur s'est produite: ${error.message || 'Erreur inconnue'}`,
    };
  }
};

export const getConversations = async (): Promise<Conversation[]> => {
  // Récupérer les conversations depuis le stockage local
  return getStoredConversations();
};

export const getCurrentConversationData = async (): Promise<Conversation | null> => {
  // Récupérer la conversation courante
  const current = getCurrentConversation();
  
  // Si aucune conversation courante n'existe, en créer une nouvelle
  if (!current) {
    return createNewConversation();
  }
  
  return current;
};

export const getConversation = async (id: string): Promise<Conversation> => {
  // Vérifier si c'est la conversation courante
  const currentConversation = getCurrentConversation();
  if (currentConversation && currentConversation.id === id) {
    return currentConversation;
  }
  
  // Sinon, chercher dans les récents
  const conversations = getStoredConversations();
  const conversation = conversations.find(c => c.id === id);
  
  if (!conversation) {
    throw new Error(`Conversation with ID ${id} not found`);
  }
  
  // Définir cette conversation comme courante
  return setConversationAsCurrent(id) || conversation;
};

export const createConversation = async (): Promise<Conversation> => {
  // Toujours créer une nouvelle conversation, même si la conversation courante n'a pas de messages
  return createNewConversation();
};
