import { Conversation } from '../types.js';

const CURRENT_CONVERSATION_KEY = 'current_conversation';
const CONVERSATIONS_KEY = 'conversations';
const MAX_RECENT_CONVERSATIONS = 10;

// Récupérer la conversation courante
export const getCurrentConversation = (): Conversation | null => {
  const storedData = localStorage.getItem(CURRENT_CONVERSATION_KEY);
  return storedData ? JSON.parse(storedData) : null;
};

// Sauvegarder la conversation courante
export const saveCurrentConversation = (conversation: Conversation): void => {
  localStorage.setItem(CURRENT_CONVERSATION_KEY, JSON.stringify(conversation));
};

// Récupérer les conversations récentes
export const getStoredConversations = (): Conversation[] => {
  const storedData = localStorage.getItem(CONVERSATIONS_KEY);
  return storedData ? JSON.parse(storedData) : [];
};

// Sauvegarder une conversation dans les récentes
export const saveConversation = (conversation: Conversation): void => {
  const conversations = getStoredConversations();
  
  // Vérifier si la conversation existe déjà
  const existingIndex = conversations.findIndex(c => c.id === conversation.id);
  
  if (existingIndex !== -1) {
    // Mettre à jour la conversation existante
    conversations[existingIndex] = conversation;
  } else {
    // Ajouter la nouvelle conversation au début
    conversations.unshift(conversation);
    
    // Limiter le nombre de conversations récentes
    if (conversations.length > MAX_RECENT_CONVERSATIONS) {
      conversations.pop();
    }
  }
  
  localStorage.setItem(CONVERSATIONS_KEY, JSON.stringify(conversations));
};

// Supprimer une conversation
export const deleteConversation = (id: string): void => {
  const conversations = getStoredConversations();
  const filteredConversations = conversations.filter(c => c.id !== id);
  localStorage.setItem(CONVERSATIONS_KEY, JSON.stringify(filteredConversations));
  
  // Si c'est la conversation courante, la supprimer aussi
  const current = getCurrentConversation();
  if (current && current.id === id) {
    localStorage.removeItem(CURRENT_CONVERSATION_KEY);
  }
};

// Renommer une conversation
export const renameConversation = (id: string, newTitle: string): void => {
  // Mettre à jour dans les récentes
  const conversations = getStoredConversations();
  const conversationIndex = conversations.findIndex(c => c.id === id);
  
  if (conversationIndex !== -1) {
    conversations[conversationIndex].title = newTitle;
    localStorage.setItem(CONVERSATIONS_KEY, JSON.stringify(conversations));
  }
  
  // Mettre à jour la conversation courante si nécessaire
  const current = getCurrentConversation();
  if (current && current.id === id) {
    current.title = newTitle;
    saveCurrentConversation(current);
  }
};

// Définir une conversation comme courante
export const setConversationAsCurrent = (id: string): Conversation | null => {
  const conversations = getStoredConversations();
  const conversation = conversations.find(c => c.id === id);
  
  if (conversation) {
    saveCurrentConversation(conversation);
    return conversation;
  }
  
  return null;
};

// Créer une nouvelle conversation courante et déplacer l'ancienne dans les récents
export const createNewConversation = (): Conversation => {
  // Récupérer la conversation courante actuelle
  const currentConversation = getCurrentConversation();
  
  // Si une conversation courante existe et contient des messages, la déplacer dans les récents
  if (currentConversation && currentConversation.messages && currentConversation.messages.length > 0) {
    // Sauvegarder la conversation courante dans les récentes
    saveConversation(currentConversation);
  }
  
  // Créer une nouvelle conversation courante
  const newId = Date.now().toString();
  const newConversation: Conversation = {
    id: newId,
    title: 'Nouvelle conversation',
    messages: [],
    createdAt: new Date().toISOString()
  };
  
  saveCurrentConversation(newConversation);
  return newConversation;
};
