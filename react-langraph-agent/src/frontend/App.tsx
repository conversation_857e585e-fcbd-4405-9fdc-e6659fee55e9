import React, { useState, useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import Sidebar from './components/Sidebar.js';
import ChatInterface from './components/ChatInterface.js';
import OpenFilePage from './pages/OpenFilePage.js';
import PdfViewerPage from './pages/PdfViewerPage.js';
import { registerProtocolHandler } from './utils/protocolHandler.js';

const App: React.FC = () => {
  const [sidebarVisible, setSidebarVisible] = useState(true);

  // Tenter d'enregistrer le gestionnaire de protocole au chargement de l'application
  useEffect(() => {
    try {
      registerProtocolHandler();
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du gestionnaire de protocole:', error);
    }
  }, []);

  const toggleSidebar = () => {
    setSidebarVisible(!sidebarVisible);
  };

  return (
    <div className="flex h-screen bg-nextchat-bg overflow-hidden p-2">
      <div className="flex w-full h-full rounded-2xl bg-white shadow-sm overflow-hidden">
        <div className={`${sidebarVisible ? 'w-64' : 'w-0'} transition-all duration-300 ease-in-out overflow-hidden bg-sidebar-bg border-r border-chat-border`}>
          <Sidebar />
        </div>
        <main className="flex-1 overflow-hidden">
          <Routes>
            <Route path="/" element={<ChatInterface toggleSidebar={toggleSidebar} />} />
            <Route path="/chat/:id" element={<ChatInterface toggleSidebar={toggleSidebar} />} />
            <Route path="/open-file" element={<OpenFilePage />} />
            <Route path="/view-pdf" element={<PdfViewerPage />} />
          </Routes>
        </main>
      </div>
    </div>
  );
};

export default App;
