import { ChatAnthropic } from '@langchain/anthropic';
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { MessagesAnnotation, StateGraph } from '@langchain/langgraph';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import dotenv from 'dotenv';
import { initializeDatabase } from './api-database.js';
import { createSQLDatabaseTools } from './tools.js';
import { createDocumentDatabaseTools } from './tools.js';
import { initializeDocumentDatabase } from './api-document.js';
// Import memory components
import { BufferMemory } from "langchain/memory";
import { ConversationSummaryBufferMemory } from "langchain/memory";

// Load environment variables
dotenv.config();

// SQL Agent system prompt
const SQL_AGENT_SYSTEM_PROMPT = `
You are an agent designed to interact with a SQL database and Search Documents.
Given an input question, create a syntactically correct SQL query to run, or check documents avalaible in documents.
then look at the results of the query and return the answer.

CRITICAL INSTRUCTION: ALWAYS check the business context FIRST before writing any SQL query. 
The context often contains pre-validated SQL queries for common questions.
If the context contains a SQL query that matches or is similar to the user's question, 
USE THAT QUERY DIRECTLY instead of creating a new one.

IMPORTANT WORKFLOW - ALWAYS FOLLOW THIS EXACT SEQUENCE:
1. FIRST retrieve the business context using the sql_db_context_query tool
2. Check if the context contains a relevant query for the question
3. If yes, use that query directly with the sql_db_query tool
4. If no, then:
   a. Use sql_db_list_tables tool to get all tables (NEVER use SQL queries like SHOW TABLES or SELECT from information_schema)
   b. Use sql_db_schema tool to get schema for relevant tables (NEVER use SQL queries for this)
   c. Write a new query based on the schema information
   d. Use sql_db_query tool to execute your query

NEVER try to use SQL queries like "SHOW TABLES", "SHOW DATABASES", or queries against "information_schema" - 
these will not work with this database. Instead, always use the dedicated tools:
- sql_db_list_tables - To list all tables
- sql_db_schema - To get schema for a specific table
- sql_db_query - To execute a SQL query
- sql_db_query_checker - To check a query for errors
- document_search - To search for documents related to the user's question.
 CRITICAL: When using document_search, you MUST make EXACTLY ONE search with the EXACT terms from the user's question. DO NOT make multiple searches with different terms or variations. This is to prevent token limit errors. FAILURE TO FOLLOW THIS INSTRUCTION WILL RESULT IN INCORRECT RESULTS AND SYSTEM ERRORS.
 When displaying document results, you MUST ALWAYS list the EXACT file_path from the metadata for EACH document found. 
 This is CRITICAL. For each document, display a line with "Chemin du fichier: " followed by the complete file_path. Example: "Chemin du fichier: Y:\\HEMADIALYSE\\HMD_PJ\\000001870\\mail\\125277\\PJ_Ordonnance.pdf". Do this for EVERY document, not just a summary.

When writing new queries:
- Only query relevant columns
- Double check your query before executing it
- Never use DML statements (INSERT, UPDATE, DELETE, DROP etc.)

IMPORTANT: After executing a SQL query, you MUST ALWAYS:
1. Include the EXACT and COMPLETE results of the query in your response, showing ALL returned rows and columns
2. Format the results in a readable way (table or list format)


If the query returns no data, clearly state "Aucun résultat trouvé" and suggest possible reasons.

ALWAYS respond in French, regardless of the language of the user's question.

CRITICAL INSTRUCTION FOR DOCUMENT SEARCH:
- You MUST make EXACTLY ONE call to document_search
- You MUST use the EXACT query from the user without modification
- You MUST NOT make multiple searches with different terms
- VIOLATION OF THIS INSTRUCTION WILL CAUSE SYSTEM FAILURE


INSTRUCTIONS POUR LE TRAITEMENT STATISTIQUE DES DONNÉES:

Pour les questions nécessitant des calculs statistiques (moyennes, écarts-types, discriminations, etc.):
1. Récupérez d'abord les données brutes via une requête SQL appropriée
2. Une fois les données obtenues, effectuez vous-même les calculs mathématiques nécessaires
3. N'essayez PAS de faire des calculs complexes directement en SQL si cela n'est pas nécessaire

Pour les calculs statistiques spécifiques:
- MOYENNES: Récupérez les valeurs individuelles et calculez la moyenne arithmétique 
- MÉDIANES: Récupérez l'ensemble des valeurs, triez-les et déterminez la médiane
- DISCRIMINATION: Pour les analyses discriminantes, récupérez les données brutes par catégorie puis:
  a. Calculez les statistiques descriptives par groupe (moyennes, écarts-types)
  b. Analysez les différences entre groupes
  c. Présentez clairement les résultats avec leur interprétation

`;

// Create a memory instance to store conversation history using BufferMemory
const memory = new BufferMemory();

// Example of using BufferMemory
async function bufferMemoryExample() {
  // Save a conversation exchange
  await memory.saveContext({ input: "Bonjour" }, { output: "Salut !" });
  
  // Load the memory variables
  const vars = await memory.loadMemoryVariables({});
  console.log(vars); // { history: ... }
  
  return vars;
}

// Example of using ConversationSummaryBufferMemory
async function conversationSummaryBufferMemoryExample() {
  const summaryMemory = new ConversationSummaryBufferMemory({ 
    llm: new ChatAnthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
      model: 'claude-3-7-sonnet-20250219',
      temperature: 0,
    }),
    maxTokenLimit: 500
  });
  
  await summaryMemory.saveContext({ input: "hi" }, { output: "hello" });
  const vars = await summaryMemory.loadMemoryVariables({});
  console.log(vars); // { history: ... }
  
  return vars;
}

/**
 * Creates a SQL agent using LangGraph.js with conversation memory
 */
export async function createSQLAgent(question: string) {
  try {
    console.log('Initializing SQL Agent...');
    
    // Initialize the database
    const db = await initializeDatabase();

    // Initialize the documents database
    const document_db = await initializeDocumentDatabase();
    
    // Create the database tools
    const sqlTools = createSQLDatabaseTools(db);

    // Create the document database tools
    const documentTools = createDocumentDatabaseTools(document_db);
    
    // Combine all tools into a single array
    const allTools = [...sqlTools, ...documentTools];
    
    // Initialize the LLM
    const llm = new ChatAnthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
      model: 'claude-3-7-sonnet-20250219',
      temperature: 0,
    });
    
    // Load previous messages from memory
    const previousMessages = await memory.loadMemoryVariables({});
    const chatHistory = previousMessages.history ? [{ role: 'user', content: previousMessages.history }] : [];
    
    // Define the function that calls the model
    async function callModel(state: typeof MessagesAnnotation.State) {
      // Bind the tools to the LLM
      const llmWithTools = llm.bindTools(allTools);
      
      // Invoke the LLM with chat history included
      const response = await llmWithTools.invoke([
        { role: 'system', content: SQL_AGENT_SYSTEM_PROMPT },
        ...chatHistory, // Include previous conversation history
        ...state.messages, // Include current messages
      ]);
      
      // Return the response
      return { messages: [response] };
    }
    
    // Define the function that determines whether to continue or not
    function routeModelOutput(state: typeof MessagesAnnotation.State): string {
      const messages = state.messages;
      const lastMessage = messages[messages.length - 1];
      
      // If the LLM is invoking tools, route there
      if ((lastMessage as AIMessage)?.tool_calls?.length || 0 > 0) {
        return 'tools';
      }
      // Otherwise end the graph
      else {
        return '__end__';
      }
    }
    
    // Define a new graph
    const workflow = new StateGraph(MessagesAnnotation)
      // Define the two nodes we will cycle between
      .addNode('callModel', callModel)
      .addNode('tools', new ToolNode(allTools))
      // Set the entrypoint as `callModel`
      .addEdge('__start__', 'callModel')
      .addConditionalEdges(
        // First, we define the edges' source node
        'callModel',
        // Next, we pass in the function that will determine the sink node(s)
        routeModelOutput,
      )
      // This means that after `tools` is called, `callModel` node is called next
      .addEdge('tools', 'callModel');
    
    // Compile the graph
    const graph = workflow.compile();
    
    // Initialize the state with the user's question
    const initialState = {
      messages: [new HumanMessage(question)],
    };
    
    console.log(`Question: ${question}`);
    console.log('Processing...');
    
    // Execute the graph
    const result = await graph.invoke(initialState);
    
    // Get the final answer
    const finalMessage = result.messages[result.messages.length - 1];
    console.log('\nFinal Answer:');
    console.log(finalMessage.content);
    
    // Save the conversation to memory for future reference
    await memory.saveContext(
      { input: question },
      { output: finalMessage.content }
    );
    
    return finalMessage.content;
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
}

/**
 * Main function to run the SQL agent
 */
async function main() {
  try {
    // Example usage
    const question = "Quels sont les patients présents aujourd'hui?";
    await createSQLAgent(question);
    
    // You can ask follow-up questions and the agent will remember the context
    const followUp = "Parmi eux, lesquels ont une séance aujourd'hui?";
    await createSQLAgent(followUp);
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the main function if this file is executed directly
// In ES modules, we can use import.meta.url to check if this is the main module
const isMainModule = import.meta.url.endsWith(process.argv[1].replace(/^file:\/\//, ''));
if (isMainModule) {
  main().catch(console.error);
}
