import { ChatAnthropic } from '@langchain/anthropic';
import { HumanMessage, BaseMessage, AIMessage } from '@langchain/core/messages';
import { StateGraph, Annotation } from '@langchain/langgraph';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import dotenv from 'dotenv';
import { initializeDatabase } from './api-database.js';
import { createSQLDatabaseTools } from './tools.js';
import { createDocumentDatabaseTools } from './tools.js';
import { initializeDocumentDatabase } from './api-document.js';
// Import memory components
import { BufferMemory } from "langchain/memory";
import { ConversationSummaryBufferMemory } from "langchain/memory";

// Load environment variables
dotenv.config();

// Define enhanced state annotation for multi-agent workflow
const MultiAgentAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  context_retrieved: Annotation<boolean>({
    reducer: (x, y) => y ?? x,
    default: () => false,
  }),
  sql_exploration_done: Annotation<boolean>({
    reducer: (x, y) => y ?? x,
    default: () => false,
  }),
  document_search_done: Annotation<boolean>({
    reducer: (x, y) => y ?? x,
    default: () => false,
  }),
  agent_flow: Annotation<string[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  current_agent: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => '',
  }),
  sql_results: Annotation<any>({
    reducer: (x, y) => y ?? x,
    default: () => null,
  }),
  document_results: Annotation<any>({
    reducer: (x, y) => y ?? x,
    default: () => null,
  }),
});

// Specialized agent prompts
const SQL_CONTEXT_AGENT_PROMPT = `
Vous êtes l'agent spécialisé dans la récupération du contexte métier et des requêtes pré-validées.

VOTRE MISSION UNIQUE:
- Récupérer le contexte métier via l'outil sql_db_context_query
- Analyser si le contexte contient une requête pré-validée pour la question de l'utilisateur
- Déterminer si la question nécessite une recherche SQL ou documentaire

INSTRUCTIONS:
1. Utilisez TOUJOURS l'outil sql_db_context_query en premier
2. Analysez le contexte récupéré pour identifier les requêtes pertinentes
3. Si une requête pré-validée correspond à la question, préparez-la pour l'exécution
4. Répondez TOUJOURS en français

IMPORTANT: Vous ne devez PAS exécuter de requêtes SQL - votre rôle est uniquement de récupérer et analyser le contexte.
`;

const SQL_EXPLORATION_AGENT_PROMPT = `
Vous êtes l'agent spécialisé dans l'exploration de la structure de la base de données.

VOTRE MISSION UNIQUE:
- Explorer les tables et schémas de la base de données
- Identifier les tables pertinentes pour la question de l'utilisateur
- Préparer les informations nécessaires pour la génération de requêtes

INSTRUCTIONS:
1. Utilisez sql_db_list_tables pour lister toutes les tables
2. Analysez la question pour identifier les tables pertinentes
3. Utilisez sql_db_schema pour récupérer les schémas des tables pertinentes
4. Préparez un plan de requête basé sur les schémas

INTERDICTIONS:
- JAMAIS de requêtes SQL directes comme SHOW TABLES ou SELECT from information_schema
- Utilisez UNIQUEMENT les outils dédiés

IMPORTANT: Vous ne devez PAS exécuter de requêtes SQL - votre rôle est uniquement d'explorer la structure.
`;

const SQL_EXECUTION_AGENT_PROMPT = `
Vous êtes l'agent spécialisé dans l'exécution des requêtes SQL.

VOTRE MISSION UNIQUE:
- Générer et exécuter des requêtes SQL sécurisées
- Vérifier les requêtes avant exécution
- Formater les résultats de manière lisible

INSTRUCTIONS:
1. Générez une requête SQL basée sur les informations fournies
2. Utilisez sql_db_query_checker pour vérifier la requête
3. Exécutez la requête avec sql_db_query
4. Formatez les résultats de manière claire et complète

SÉCURITÉ:
- JAMAIS de requêtes DML (INSERT, UPDATE, DELETE, DROP)
- Limitez les résultats si nécessaire
- Vérifiez toujours avant d'exécuter

IMPORTANT: Incluez TOUS les résultats de la requête dans votre réponse.
`;

const DOCUMENT_SEARCH_AGENT_PROMPT = `
Vous êtes l'agent spécialisé dans la recherche documentaire.

VOTRE MISSION UNIQUE:
- Effectuer UNE SEULE recherche documentaire avec les termes exacts de l'utilisateur
- Présenter les résultats avec les chemins complets des fichiers

INSTRUCTIONS CRITIQUES:
1. Utilisez document_search avec les termes EXACTS de la question
2. NE FAITES JAMAIS plusieurs recherches avec des termes différents
3. Pour chaque document trouvé, affichez: "Chemin du fichier: [chemin_complet]"

EXEMPLE de format requis:
"Chemin du fichier: Y:\\HEMADIALYSE\\HMD_PJ\\000001870\\mail\\125277\\PJ_Ordonnance.pdf"

VIOLATION DE CES INSTRUCTIONS = ÉCHEC SYSTÈME
`;

const SYNTHESIS_AGENT_PROMPT = `
Vous êtes l'agent de synthèse finale qui combine tous les résultats.

VOTRE MISSION UNIQUE:
- Analyser tous les résultats des agents précédents
- Synthétiser une réponse complète et cohérente
- Effectuer des calculs statistiques si nécessaire

INSTRUCTIONS:
1. Combinez les résultats SQL et documentaires
2. Effectuez les calculs statistiques manuellement si requis
3. Présentez une réponse structurée et complète
4. Répondez TOUJOURS en français

CALCULS STATISTIQUES:
- MOYENNES: Calculez arithmétiquement à partir des données brutes
- MÉDIANES: Triez et déterminez la médiane
- ANALYSES DISCRIMINANTES: Calculez par groupe et analysez les différences

IMPORTANT: Votre réponse doit être la réponse finale à l'utilisateur.
`;

// Create a memory instance to store conversation history using BufferMemory
const memory = new BufferMemory();

// Example of using BufferMemory
async function bufferMemoryExample() {
  // Save a conversation exchange
  await memory.saveContext({ input: "Bonjour" }, { output: "Salut !" });

  // Load the memory variables
  const vars = await memory.loadMemoryVariables({});
  console.log(vars); // { history: ... }

  return vars;
}

// Example of using ConversationSummaryBufferMemory
async function conversationSummaryBufferMemoryExample() {
  const summaryMemory = new ConversationSummaryBufferMemory({
    llm: new ChatAnthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
      model: 'claude-3-7-sonnet-20250219',
      temperature: 0,
    }),
    maxTokenLimit: 500
  });

  await summaryMemory.saveContext({ input: "hi" }, { output: "hello" });
  const vars = await summaryMemory.loadMemoryVariables({});
  console.log(vars); // { history: ... }

  return vars;
}

// Specialized agent creation functions
function createSQLContextAgent(llm: ChatAnthropic, tools: any[]) {
  return async (state: typeof MultiAgentAnnotation.State) => {
    const llmWithTools = llm.bindTools(tools);
    const response = await llmWithTools.invoke([
      { role: 'system', content: SQL_CONTEXT_AGENT_PROMPT },
      ...state.messages,
    ]);

    // Update state to track agent flow
    const updatedState = {
      ...state,
      messages: [...state.messages, response],
      context_retrieved: true,
      current_agent: 'sql_context',
      agent_flow: [...(state.agent_flow || []), 'sql_context']
    };

    return updatedState;
  };
}

function createSQLExplorationAgent(llm: ChatAnthropic, tools: any[]) {
  return async (state: typeof MultiAgentAnnotation.State) => {
    const llmWithTools = llm.bindTools(tools);
    const response = await llmWithTools.invoke([
      { role: 'system', content: SQL_EXPLORATION_AGENT_PROMPT },
      ...state.messages,
    ]);

    const updatedState = {
      ...state,
      messages: [...state.messages, response],
      sql_exploration_done: true,
      current_agent: 'sql_exploration',
      agent_flow: [...(state.agent_flow || []), 'sql_exploration']
    };

    return updatedState;
  };
}

function createSQLExecutionAgent(llm: ChatAnthropic, tools: any[]) {
  return async (state: typeof MultiAgentAnnotation.State) => {
    const llmWithTools = llm.bindTools(tools);
    const response = await llmWithTools.invoke([
      { role: 'system', content: SQL_EXECUTION_AGENT_PROMPT },
      ...state.messages,
    ]);

    const updatedState = {
      ...state,
      messages: [...state.messages, response],
      current_agent: 'sql_execution',
      agent_flow: [...(state.agent_flow || []), 'sql_execution']
    };

    return updatedState;
  };
}

function createDocumentSearchAgent(llm: ChatAnthropic, tools: any[]) {
  return async (state: typeof MultiAgentAnnotation.State) => {
    const llmWithTools = llm.bindTools(tools);
    const response = await llmWithTools.invoke([
      { role: 'system', content: DOCUMENT_SEARCH_AGENT_PROMPT },
      ...state.messages,
    ]);

    const updatedState = {
      ...state,
      messages: [...state.messages, response],
      document_search_done: true,
      current_agent: 'document_search',
      agent_flow: [...(state.agent_flow || []), 'document_search']
    };

    return updatedState;
  };
}

function createSynthesisAgent(llm: ChatAnthropic) {
  return async (state: typeof MultiAgentAnnotation.State) => {
    const response = await llm.invoke([
      { role: 'system', content: SYNTHESIS_AGENT_PROMPT },
      ...state.messages,
    ]);

    const updatedState = {
      ...state,
      messages: [...state.messages, response],
      current_agent: 'synthesis',
      agent_flow: [...(state.agent_flow || []), 'synthesis']
    };

    return updatedState;
  };
}

// Function to check if a message has tool calls
function shouldCallTools(state: typeof MultiAgentAnnotation.State): string {
  const lastMessage = state.messages[state.messages.length - 1];
  if ((lastMessage as AIMessage)?.tool_calls?.length || 0 > 0) {
    return 'tools';
  }
  return 'continue';
}

/**
 * Creates a simplified multi-agent workflow that works with tools
 */
async function createMultiAgentWorkflow() {
  // Initialize the database
  const db = await initializeDatabase();
  const document_db = await initializeDocumentDatabase();

  // Create all tools
  const sqlTools = createSQLDatabaseTools(db);
  const documentTools = createDocumentDatabaseTools(document_db);
  const allTools = [...sqlTools, ...documentTools];

  // Initialize the LLM
  const llm = new ChatAnthropic({
    apiKey: process.env.ANTHROPIC_API_KEY,
    model: 'claude-3-7-sonnet-20250219',
    temperature: 0,
  });

  // Create a unified agent that uses different prompts based on state
  async function multiAgent(state: typeof MultiAgentAnnotation.State) {
    const currentAgent = state.current_agent || 'sql_context';
    let systemPrompt = SQL_CONTEXT_AGENT_PROMPT;

    // Select prompt based on current agent
    switch (currentAgent) {
      case 'sql_context':
        systemPrompt = SQL_CONTEXT_AGENT_PROMPT;
        break;
      case 'sql_exploration':
        systemPrompt = SQL_EXPLORATION_AGENT_PROMPT;
        break;
      case 'sql_execution':
        systemPrompt = SQL_EXECUTION_AGENT_PROMPT;
        break;
      case 'document_search':
        systemPrompt = DOCUMENT_SEARCH_AGENT_PROMPT;
        break;
      case 'synthesis':
        systemPrompt = SYNTHESIS_AGENT_PROMPT;
        break;
    }

    const llmWithTools = llm.bindTools(allTools);
    const response = await llmWithTools.invoke([
      { role: 'system', content: systemPrompt },
      ...state.messages,
    ]);

    return {
      messages: [response],
      agent_flow: [currentAgent],
      current_agent: currentAgent
    };
  }

  // Define routing logic
  function routeAgent(state: typeof MultiAgentAnnotation.State): string {
    const lastMessage = state.messages[state.messages.length - 1];

    // Check if we need to call tools
    if ((lastMessage as AIMessage)?.tool_calls?.length || 0 > 0) {
      return 'tools';
    }

    const currentAgent = state.current_agent || 'sql_context';
    const content = typeof lastMessage.content === 'string'
      ? lastMessage.content.toLowerCase()
      : JSON.stringify(lastMessage.content).toLowerCase();

    // Route based on current agent and content
    switch (currentAgent) {
      case 'sql_context':
        if (content.includes('document') || content.includes('fichier') || content.includes('pdf')) {
          return 'document_search';
        }
        if (content.includes('requête trouvée') || content.includes('query found')) {
          return 'sql_execution';
        }
        return 'sql_exploration';

      case 'sql_exploration':
        return 'sql_execution';

      case 'sql_execution':
      case 'document_search':
        return 'synthesis';

      case 'synthesis':
        return '__end__';

      default:
        return '__end__';
    }
  }

  // Build the workflow
  const workflow = new StateGraph(MultiAgentAnnotation)
    .addNode('agent', multiAgent)
    .addNode('tools', new ToolNode(allTools))
    .addNode('sql_exploration', (state: typeof MultiAgentAnnotation.State) => ({ ...state, current_agent: 'sql_exploration' }))
    .addNode('sql_execution', (state: typeof MultiAgentAnnotation.State) => ({ ...state, current_agent: 'sql_execution' }))
    .addNode('document_search', (state: typeof MultiAgentAnnotation.State) => ({ ...state, current_agent: 'document_search' }))
    .addNode('synthesis', (state: typeof MultiAgentAnnotation.State) => ({ ...state, current_agent: 'synthesis' }))

    .addEdge('__start__', 'agent')
    .addConditionalEdges('agent', routeAgent)
    .addEdge('tools', 'agent')
    .addEdge('sql_exploration', 'agent')
    .addEdge('sql_execution', 'agent')
    .addEdge('document_search', 'agent')
    .addEdge('synthesis', 'agent');

  return workflow.compile();
}

/**
 * Creates a SQL agent using multi-agent architecture with conversation memory
 */
export async function createSQLAgent(question: string) {
  try {
    console.log('Initializing Multi-Agent SQL System...');

    // Create the multi-agent workflow
    const workflow = await createMultiAgentWorkflow();

    // Load previous messages from memory
    const previousMessages = await memory.loadMemoryVariables({});
    const chatHistory = previousMessages.history ? [{ role: 'user', content: previousMessages.history }] : [];

    // Initialize the state with the user's question and chat history
    const initialState = {
      messages: [
        ...chatHistory.map(msg => new HumanMessage(msg.content)),
        new HumanMessage(question)
      ],
      context_retrieved: false,
      sql_exploration_done: false,
      document_search_done: false,
      agent_flow: [],
      current_agent: '',
      sql_results: null,
      document_results: null,
    };

    console.log(`Question: ${question}`);
    console.log('Processing through multi-agent workflow...');

    // Execute the multi-agent workflow
    const result = await workflow.invoke(initialState);

    // Get the final answer
    const finalMessage = result.messages[result.messages.length - 1];
    console.log('\nFinal Answer:');
    console.log(finalMessage.content);
    console.log('\nAgent Flow:', result.agent_flow);

    // Save the conversation to memory with enhanced context
    await memory.saveContext(
      { input: question },
      {
        output: finalMessage.content,
        agent_flow: result.agent_flow,
        current_agent: result.current_agent
      }
    );

    return finalMessage.content;
  } catch (error) {
    console.error('Error in multi-agent workflow:', error);
    throw error;
  }
}

/**
 * Main function to run the SQL agent
 */
async function main() {
  try {
    // Example usage
    const question = "Quels sont les patients présents aujourd'hui?";
    await createSQLAgent(question);
    
    // You can ask follow-up questions and the agent will remember the context
    const followUp = "Parmi eux, lesquels ont une séance aujourd'hui?";
    await createSQLAgent(followUp);
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the main function if this file is executed directly
// In ES modules, we can use import.meta.url to check if this is the main module
const isMainModule = import.meta.url.endsWith(process.argv[1].replace(/^file:\/\//, ''));
if (isMainModule) {
  main().catch(console.error);
}
