#!/usr/bin/env node

// This script runs the TypeScript server using ts-node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Path to the ts-node executable
const tsNodePath = resolve(__dirname, '../node_modules/.bin/ts-node');

// Path to the server.ts file
const serverPath = resolve(__dirname, 'server.ts');

// Options for ts-node
const options = [
  '--esm',  // Use ESM modules
  serverPath
];

// Spawn the ts-node process
const serverProcess = spawn(tsNodePath, options, {
  stdio: 'inherit',  // Inherit stdio from parent process
  shell: true        // Use shell to run the command
});

// Handle process events
serverProcess.on('error', (error) => {
  console.error('Failed to start server:', error);
});

serverProcess.on('close', (code) => {
  console.log(`Server process exited with code ${code}`);
});
