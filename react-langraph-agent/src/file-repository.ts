import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

// Promisify fs functions
const readFileAsync = promisify(fs.readFile);
const statAsync = promisify(fs.stat);

// Interface for file metadata
interface FileMetadata {
  name: string;
  size: number;
  lastModified: Date;
  extension: string;
  path: string;
  contentType: string;
}

// Interface for cached file
interface CachedFile {
  metadata: FileMetadata;
  content: Buffer;
  timestamp: number; // When the file was cached
}

/**
 * File Repository Service
 * 
 * This service manages file access and caching to allow files to be accessed
 * from different machines without direct file system access.
 */
class FileRepository {
  private fileCache: Map<string, CachedFile> = new Map();
  private cacheTTL: number = 3600000; // 1 hour in milliseconds
  private maxCacheSize: number = 500 * 1024 * 1024; // 500MB
  private currentCacheSize: number = 0;

  constructor() {
    // Start cache cleanup interval
    setInterval(() => this.cleanupCache(), 300000); // Clean every 5 minutes
  }

  /**
   * Get file metadata
   * @param filePath Path to the file
   * @returns File metadata
   */
  async getFileMetadata(filePath: string): Promise<FileMetadata | null> {
    try {
      // Check if file exists in cache
      const cachedFile = this.fileCache.get(filePath);
      if (cachedFile) {
        return cachedFile.metadata;
      }

      // Check if file exists on disk
      if (!await this.fileExists(filePath)) {
        return null;
      }

      // Get file stats
      const stats = await statAsync(filePath);
      const fileName = path.basename(filePath);
      const fileExt = path.extname(filePath).toLowerCase();

      // Determine content type
      const contentType = this.getContentType(fileExt);

      // Create metadata
      const metadata: FileMetadata = {
        name: fileName,
        size: stats.size,
        lastModified: stats.mtime,
        extension: fileExt,
        path: filePath,
        contentType
      };

      return metadata;
    } catch (error) {
      console.error('Error getting file metadata:', error);
      return null;
    }
  }

  /**
   * Get file content
   * @param filePath Path to the file
   * @returns File content as Buffer
   */
  async getFileContent(filePath: string): Promise<Buffer | null> {
    try {
      // Check if file exists in cache
      const cachedFile = this.fileCache.get(filePath);
      if (cachedFile) {
        // Update timestamp to indicate recent use
        cachedFile.timestamp = Date.now();
        return cachedFile.content;
      }

      // Check if file exists on disk
      if (!await this.fileExists(filePath)) {
        return null;
      }

      // Get file metadata
      const metadata = await this.getFileMetadata(filePath);
      if (!metadata) {
        return null;
      }

      // Read file content
      const content = await readFileAsync(filePath);

      // Cache the file if it's not too large
      if (content.length <= 50 * 1024 * 1024) { // Don't cache files larger than 50MB
        this.cacheFile(filePath, metadata, content);
      }

      return content;
    } catch (error) {
      console.error('Error getting file content:', error);
      return null;
    }
  }

  /**
   * Check if a file exists
   * @param filePath Path to the file
   * @returns true if file exists, false otherwise
   */
  async fileExists(filePath: string): Promise<boolean> {
    try {
      // Check if file exists in cache
      if (this.fileCache.has(filePath)) {
        return true;
      }

      // Check if file exists on disk
      await statAsync(filePath);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Cache a file
   * @param filePath Path to the file
   * @param metadata File metadata
   * @param content File content
   */
  private cacheFile(filePath: string, metadata: FileMetadata, content: Buffer): void {
    // Check if adding this file would exceed the cache size limit
    if (this.currentCacheSize + content.length > this.maxCacheSize) {
      this.makeRoomInCache(content.length);
    }

    // Add file to cache
    this.fileCache.set(filePath, {
      metadata,
      content,
      timestamp: Date.now()
    });

    // Update current cache size
    this.currentCacheSize += content.length;
  }

  /**
   * Make room in the cache by removing least recently used files
   * @param neededSpace Space needed in bytes
   */
  private makeRoomInCache(neededSpace: number): void {
    // If needed space is larger than max cache size, we can't cache it
    if (neededSpace > this.maxCacheSize) {
      return;
    }

    // Sort cache entries by timestamp (oldest first)
    const entries = Array.from(this.fileCache.entries())
      .sort((a, b) => a[1].timestamp - b[1].timestamp);

    // Remove entries until we have enough space
    let freedSpace = 0;
    for (const [key, value] of entries) {
      if (this.currentCacheSize - freedSpace + neededSpace <= this.maxCacheSize) {
        break;
      }

      this.fileCache.delete(key);
      freedSpace += value.content.length;
    }

    // Update current cache size
    this.currentCacheSize -= freedSpace;
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    let freedSpace = 0;

    // Remove expired entries
    for (const [key, value] of this.fileCache.entries()) {
      if (now - value.timestamp > this.cacheTTL) {
        this.fileCache.delete(key);
        freedSpace += value.content.length;
      }
    }

    // Update current cache size
    this.currentCacheSize -= freedSpace;
  }

  /**
   * Get content type based on file extension
   * @param fileExt File extension
   * @returns Content type
   */
  private getContentType(fileExt: string): string {
    switch (fileExt) {
      case '.pdf':
        return 'application/pdf';
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.txt':
        return 'text/plain';
      case '.html':
      case '.htm':
        return 'text/html';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.xls':
        return 'application/vnd.ms-excel';
      case '.xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case '.ppt':
        return 'application/vnd.ms-powerpoint';
      case '.pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      default:
        return 'application/octet-stream';
    }
  }
}

// Create a singleton instance
const fileRepository = new FileRepository();

export default fileRepository;
