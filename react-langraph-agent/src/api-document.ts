import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get API URL from environment variable or use default
const DOCUMENT_API_BASE_URL = process.env.DOCUMENT_API_BASE_URL || 'http://10.1.16.55:8087';

/**
 * API-based database wrapper that connects to the FastAPI service
 */
export class DocumentDatabase {   
  private baseUrl: string;
  public dialect: string = 'mssql'; // Set to match your actual database type

  constructor(baseUrl: string = DOCUMENT_API_BASE_URL) {
    this.baseUrl = baseUrl;
    console.log(`Initialized API database connection to: ${this.baseUrl}`);
  }

  /**
   * Create a SQLDatabase from a URI
   */
  static fromURI(uri: string): DocumentDatabase {
    // For compatibility with the original code, we'll ignore the URI
    // and use the DOCUMENT_API_BASE_URL environment variable instead
    return new DocumentDatabase();
  }
  
/*
curl -X POST "http://10.1.16.55:8087/search" \
     -H "Content-Type: application/json" \
     -d '{
           "query": "Je cherche des bilans bio",
           "limit": 5
         }'
*/
  async getDocuments(request: string): Promise<string[]> {
    try {
      console.log(`Fetching documents from ${this.baseUrl}/search with query: ${request}`);
      const response = await axios.post(`${this.baseUrl}/search`, {
        query: request,
        limit: 15
      });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching documents:', error.message);
      if (error.response) {
        console.error('API response:', error.response.data);
      }
      throw new Error(`Error fetching documents: ${error.message}`);
    }
  }
}

export async function initializeDocumentDatabase(): Promise<DocumentDatabase> {
  try {
    const apiUrl = process.env.DOCUMENT_API_BASE_URL || 'http://10.1.16.55:8087';
    console.log(`Initializing API document database connection to: ${apiUrl}`);
    
    const db = new DocumentDatabase(apiUrl);
    
    return db;
  } catch (error: any) {
    console.error('Error initializing API document database connection:', error.message);
    throw error;
  }
}
