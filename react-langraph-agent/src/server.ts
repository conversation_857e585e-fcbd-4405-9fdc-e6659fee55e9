import express from 'express';
import { Request, Response } from 'express';
import cors from 'cors';
import { createSQLAgent } from './index.js';
import fileServerRouter from './api-file-server.js';

const app = express();
const port = 3005; // Choose a port that doesn't conflict with the frontend

// Middleware
app.use(cors());
app.use(express.json());

// Create a router
const router = express.Router();

// API endpoint to process messages using the SQL agent
router.post('/api/message', async (req: Request, res: Response) => {
  
  try {
    const { message } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }
    
    console.log('Received message:', message);
    
    // Process the message using the SQL agent
    const response = await createSQLAgent(message);
    
    console.log('SQL agent response:', response);
    
    // Return the response
    return res.json({
      id: Date.now().toString(),
      role: 'assistant',
      content: response,
    });
  } catch (error: any) {
    console.error('Error processing message:', error);
    return res.status(500).json({
      error: error.message || 'An error occurred while processing your message',
    });
  }
});

// Use the routers
app.use(router);
app.use('/api/files', fileServerRouter);

// Start the server
app.listen(port, '0.0.0.0', () => {
  console.log(`Server is running on http://localhost:${port}`);
});
