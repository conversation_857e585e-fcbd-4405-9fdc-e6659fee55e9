# EMA Companion AI

Ce projet est une application complète comprenant un frontend React et un backend LangGraph pour créer un agent conversationnel capable d'interagir avec des bases de données SQL.

## Fonctionnalités

- Interface de chat réactive pour interagir avec l'agent AI
- Historique des conversations
- Agent AI capable d'interroger des bases de données SQL
- Architecture basée sur LangGraph pour un workflow d'agent flexible

## Démarrage rapide

### Prérequis

- Node.js (v16 ou supérieur)
- npm (v7 ou supérieur)
- Une clé API Anthropic (pour Claude)

### Installation

1. Clonez le dépôt
2. Installez les dépendances:

```bash
npm install
```

3. Créez un fichier `.env` basé sur `.env.example` et ajoutez votre clé API Anthropic:

```
ANTHROPIC_API_KEY=votre_clé_api_ici
API_BASE_URL=http://**********:8086
```

### Lancement de l'application

Pour lancer à la fois le frontend et le backend:

```bash
npm run dev
```

Cette commande lance:
- Le serveur frontend Vite sur http://localhost:3000
- Le serveur backend LangGraph sur http://localhost:3005

## Architecture du projet

```mermaid
graph TD
    subgraph "Frontend (Port 3000)"
        A[App.tsx] --> B[ChatInterface]
        B --> C[API Service]
    end
    
    subgraph "Backend (Port 3005)"
        D[server.ts] --> E[createSQLAgent]
        E --> F[LangGraph Workflow]
        F --> G[callModel Node]
        F --> H[tools Node]
        G <--> H
    end
    
    subgraph "Database API Service (Port 8086)"
        I[FastAPI Service] --> J[SQL Database]
    end
    
    C -- HTTP Request --> D
    H -- API Calls --> I
```

## Structure du projet

- `src/frontend/` - Code React du frontend
  - `components/` - Composants React
  - `services/` - Services API pour communiquer avec le backend
  - `main.tsx` - Point d'entrée de l'application React
  - `App.tsx` - Composant principal de l'application
  - `index.css` - Styles globaux

- `src/` - Code du backend
  - `server.ts` - Serveur Express pour le backend
  - `index.ts` - Définition du workflow LangGraph
  - `tools.ts` - Outils SQL pour l'agent
  - `api-database.ts` - Interface avec le service API de base de données

- `db-api-service/` - Service API pour les opérations de base de données

## Flux de travail de l'agent

L'agent utilise LangGraph pour définir un workflow qui alterne entre:

1. **callModel**: Appelle le LLM (Claude) avec les messages de l'utilisateur
2. **tools**: Exécute les outils SQL si le LLM a besoin d'interroger la base de données

Le workflow est défini comme un graphe d'état qui commence par appeler le modèle, puis:
- Si le modèle génère des appels d'outils, il passe au nœud des outils
- Après l'exécution des outils, il revient au modèle
- Si le modèle ne génère pas d'appels d'outils, le workflow se termine

## Technologies utilisées

- **Frontend**: React, TypeScript, Tailwind CSS, Vite
- **Backend**: Node.js, Express, LangGraph, Anthropic Claude
- **Base de données**: Service API SQL personnalisé

## Commandes npm

- `npm run frontend:dev` - Lance uniquement le serveur de développement frontend
- `npm run frontend:build` - Construit le frontend pour la production
- `npm run server` - Lance uniquement le serveur backend
- `npm run dev` - Lance à la fois le frontend et le backend
