{"name": "react-langraph-agent", "version": "1.0.0", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "./start-all.sh", "dev:frontend-backend": "./start-frontend-backend.sh", "sql-agent": "ts-node-esm src/index.ts", "server": "ts-node-esm src/server.ts", "server:js": "node src/server.js", "server:express": "node src/server-express.js", "server:run": "node src/run-server.js", "server:sh": "./run-server.sh", "test-api": "ts-node-esm src/test-api-connection.ts", "test": "echo \"Error: no test specified\" && exit 1", "frontend:dev": "vite", "frontend:build": "tsc -p tsconfig.frontend.json && vite build", "frontend:preview": "vite preview"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@langchain/anthropic": "^0.3.20", "@langchain/community": "^0.3.42", "@langchain/core": "^0.3.55", "@langchain/langgraph": "^0.2.68", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@vitejs/plugin-react": "^4.4.1", "axios": "^1.9.0", "better-sqlite3": "^11.9.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-lottie": "^1.2.10", "react-markdown": "^10.1.0", "react-router-dom": "^7.5.3", "remark-gfm": "^4.0.1", "sqlite3": "^5.1.7", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^6.3.5", "zod": "^3.24.4"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/react-lottie": "^1.2.10", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.17"}}