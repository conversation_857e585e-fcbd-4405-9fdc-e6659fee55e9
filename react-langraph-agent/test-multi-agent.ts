import { createSQLAgent } from './src/index.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Test script for the new multi-agent architecture
 */
async function testMultiAgentWorkflow() {
  console.log('🚀 Testing Multi-Agent Architecture...\n');
  
  const testQuestions = [
    {
      question: "Quels sont les patients présents aujourd'hui?",
      expectedAgents: ['sql_context', 'sql_exploration', 'sql_execution', 'synthesis'],
      description: "Test SQL workflow complet"
    },
    {
      question: "Recherche des documents PDF pour le patient 12345",
      expectedAgents: ['sql_context', 'document_search', 'synthesis'],
      description: "Test workflow de recherche documentaire"
    },
    {
      question: "Combien de séances de dialyse ont eu lieu cette semaine?",
      expectedAgents: ['sql_context', 'sql_exploration', 'sql_execution', 'synthesis'],
      description: "Test avec calculs statistiques"
    }
  ];

  for (let i = 0; i < testQuestions.length; i++) {
    const test = testQuestions[i];
    console.log(`\n📋 Test ${i + 1}: ${test.description}`);
    console.log(`❓ Question: ${test.question}`);
    console.log('⏳ Processing...\n');
    
    try {
      const startTime = Date.now();
      const response = await createSQLAgent(test.question);
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      
      console.log(`✅ Test ${i + 1} completed in ${duration}s`);
      console.log(`📝 Response: ${response.substring(0, 200)}...`);
      console.log('─'.repeat(80));
      
      // Wait a bit between tests to avoid overwhelming the system
      if (i < testQuestions.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
    } catch (error) {
      console.error(`❌ Test ${i + 1} failed:`, error);
      console.log('─'.repeat(80));
    }
  }
  
  console.log('\n🎉 Multi-Agent Architecture Testing Complete!');
}

/**
 * Test individual agent components
 */
async function testAgentComponents() {
  console.log('\n🔧 Testing Individual Agent Components...\n');
  
  // Test 1: Memory functionality
  console.log('📝 Testing conversation memory...');
  try {
    await createSQLAgent("Bonjour, je suis un nouveau utilisateur");
    await createSQLAgent("Peux-tu me rappeler ce que j'ai dit précédemment?");
    console.log('✅ Memory test passed');
  } catch (error) {
    console.error('❌ Memory test failed:', error);
  }
  
  // Test 2: Error handling
  console.log('\n🚨 Testing error handling...');
  try {
    await createSQLAgent(""); // Empty question
    console.log('✅ Error handling test passed');
  } catch (error) {
    console.log('✅ Error properly caught:', error.message);
  }
  
  console.log('\n🎯 Component Testing Complete!');
}

/**
 * Performance comparison test
 */
async function performanceTest() {
  console.log('\n⚡ Performance Testing...\n');
  
  const testQuestion = "Quels sont les patients présents aujourd'hui?";
  const iterations = 3;
  const times: number[] = [];
  
  for (let i = 0; i < iterations; i++) {
    console.log(`🔄 Iteration ${i + 1}/${iterations}`);
    const startTime = Date.now();
    
    try {
      await createSQLAgent(testQuestion);
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      times.push(duration);
      console.log(`⏱️  Duration: ${duration}s`);
    } catch (error) {
      console.error(`❌ Iteration ${i + 1} failed:`, error);
    }
    
    // Wait between iterations
    if (i < iterations - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  if (times.length > 0) {
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log('\n📊 Performance Results:');
    console.log(`   Average: ${avgTime.toFixed(2)}s`);
    console.log(`   Min: ${minTime.toFixed(2)}s`);
    console.log(`   Max: ${maxTime.toFixed(2)}s`);
  }
  
  console.log('\n⚡ Performance Testing Complete!');
}

/**
 * Main test runner
 */
async function runAllTests() {
  console.log('🧪 EMA Companion AI - Multi-Agent Architecture Tests');
  console.log('=' .repeat(80));
  
  try {
    // Test 1: Multi-agent workflow
    await testMultiAgentWorkflow();
    
    // Test 2: Individual components
    await testAgentComponents();
    
    // Test 3: Performance
    await performanceTest();
    
    console.log('\n🎊 All Tests Completed Successfully!');
    
  } catch (error) {
    console.error('\n💥 Test Suite Failed:', error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url.endsWith(process.argv[1].replace(/^file:\/\//, ''))) {
  runAllTests().catch(console.error);
}

export { testMultiAgentWorkflow, testAgentComponents, performanceTest };
