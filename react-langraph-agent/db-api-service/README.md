# Database API Service

This is a FastAPI service that provides API endpoints for database operations. It's designed to be deployed on a server that has access to your database, allowing you to interact with the database through HTTP requests instead of direct ODBC connections.

## Setup Instructions

### 1. Prerequisites

- Python 3.8 or higher
- Access to a SQL Server database
- 4D v19 ODBC Driver installed on the server

### 2. Installation

1. Clone this repository or copy the files to your server
2. Create a virtual environment:
   ```bash
   python -m venv venv
   ```
3. Activate the virtual environment:
   - On Windows:
     ```bash
     venv\Scripts\activate
     ```
   - On Linux/Mac:
     ```bash
     source venv/bin/activate
     ```
4. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

### 3. Configuration

1. Create a `.env` file based on the `.env.example` template:
   ```bash
   cp .env.example .env
   ```
2. Edit the `.env` file to set your database connection string and other settings

### 4. Running the Service

Start the service with:

```bash
uvicorn main:app --host 0.0.0.0 --port 8086 --reload
```

Or simply run:

```bash
python main.py
```

The API will be available at `http://your-server-ip:8086`

## API Endpoints

- `GET /tables` - List all tables in the database
- `POST /schema` - Get the schema of a specific table
- `POST /query` - Execute a SQL query
- `POST /query_checker` - Check a SQL query for common mistakes

## Example Usage

### List Tables

```bash
curl -X GET http://your-server-ip:8086/tables
```

### Get Table Schema

```bash
curl -X POST http://your-server-ip:8086/schema \
  -H "Content-Type: application/json" \
  -d '{"table_name": "your_table_name"}'
```

### Execute Query

```bash
curl -X POST http://your-server-ip:8086/query \
  -H "Content-Type: application/json" \
  -d '{"query": "SELECT * FROM your_table_name LIMIT 5"}'
```

### Check Query

```bash
curl -X POST http://your-server-ip:8086/query_checker \
  -H "Content-Type: application/json" \
  -d '{"query": "SELECT * FROM your_table_name WHERE id NOT IN (SELECT id FROM other_table)"}'
```

## Security Considerations

- This API does not include authentication. In a production environment, you should add authentication and authorization.
- The API blocks destructive operations (DROP, DELETE, UPDATE, INSERT, etc.) by default.
- Consider deploying behind a reverse proxy like Nginx with HTTPS enabled.
