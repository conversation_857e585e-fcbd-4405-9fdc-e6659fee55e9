CREATE TABLE SEANCE_DIALYSE (
    NUMPAT INT NOT NULL, -- Numéro unique du patient
    NOMPAT VARCHAR(80) NOT NULL, -- Nom du patient
    PRENOM VARCHAR(40) NOT NULL, -- Prénom du patient
    cDATE TIMESTAMP NOT NULL, -- Date de la séance de dialyse
    TYPE_DIALYSE VARCHAR(80) NOT NULL, -- Type de dialyse
    PERIODE VARCHAR(20) NOT NULL, -- Période de la séance (matin, après-midi, soir)
    POID_AV REAL NULL, -- Poids avant la dialyse
    POID_AP REAL NULL, -- Poids après la dialyse
    TA_DEB_AV_SYS REAL NULL, -- Tension artérielle systolique avant la dialyse (bras)
    TA_DEB_AV_DIA REAL NULL, -- Tension artérielle diastolique avant la dialyse (bras)
    TA_DEB_AP_SYS REAL NULL, -- Tension artérielle systolique après la dialyse (bras)
    TA_DEB_AP_DIA REAL NULL, -- Tension artérielle diastolique après la dialyse (bras)
    TA_COU_AV_SYS REAL NULL, -- Tension artérielle systolique avant la dialyse (cuisse)
    TA_COU_AV_DIA REAL NULL, -- Tension artérielle diastolique avant la dialyse (cuisse)
    TA_COU_AP_SYS REAL NULL, -- Tension artérielle systolique après la dialyse (cuisse)
    TA_COU_AP_DIA REAL NULL, -- Tension artérielle diastolique après la dialyse (cuisse)
    dialysat VARCHAR(80) NOT NULL, -- Type de dialysat utilisé
    POULS_deb REAL NOT NULL, -- Pouls au début de la dialyse
    TPS_COMPRESSION SMALLINT NULL, -- Temps de compression
    COMMENTAIRE VARCHAR(255) NOT NULL, -- Commentaire sur la séance
    NEPHROLOGUE VARCHAR(80) NOT NULL, -- Nom du néphrologue
    LOCALISATION VARCHAR(80) NOT NULL, -- Lieu ou localisation de la séance de dialyse
    TRANSFUSION_B BOOLEAN NOT NULL, -- Indicateur de transfusion sanguine
    NBR_POCHES REAL NULL, -- Nombre de poches de sang transfusées
    NUMERO_POCHE VARCHAR(255) NOT NULL, -- Numéro de la poche de sang
    TYPE_SANG VARCHAR(20) NOT NULL, -- Type de sang
    heure_deb TIME NOT NULL, -- Heure de début de la séance
    heure_fin TIME NOT NULL, -- Heure de fin de la séance
    duree INTERVAL NOT NULL -- Durée de la séance
    FOREIGN KEY (NUMPAT) REFERENCES PATIENT(NUMPAT)
);

CREATE TABLE EXAMENS_BIOLOGI (
    NUMPAT INT NOT NULL, -- Numéro unique du patient
    NOMPAT VARCHAR(80) NOT NULL, -- Nom du patient
    PRENOM VARCHAR(80) NOT NULL, -- Prénom du patient
    cDATE TIMESTAMP NOT NULL, -- Date de l'examen
    TYPE_EXAM VARCHAR(255) NOT NULL, -- Type d'examen
    RESULTAT_AV REAL NOT NULL, -- Résultat avant
    RESULTAT_AP REAL NOT NULL, -- Résultat après
    NORMALE VARCHAR(80) NOT NULL, -- Valeur normale
    LABO VARCHAR(80) NOT NULL, -- Laboratoire
    COMMENTAIRE VARCHAR NOT NULL, -- Commentaire de l'examen
    bilan VARCHAR(80) NOT NULL, -- Bilan de l'examen
    numero_bilan INT NOT NULL, -- Numéro du bilan
    COTATIONB REAL NOT NULL, -- Cotation de l'examen
    Unite VARCHAR(80) NOT NULL, -- Unité de mesure
    num_exam INT NOT NULL, -- Numéro d'examen
    VALEUR_alpha VARCHAR NOT NULL, -- Valeur alpha
    fam_exam VARCHAR(80) NOT NULL, -- Famille de l'examen
    fam_dosmed1 VARCHAR(80) NOT NULL, -- Famille dose médicamenteuse 1
    fam_dosmed2 VARCHAR(80) NOT NULL, -- Famille dose médicamenteuse 2
    fam_dosmed3 VARCHAR(80) NOT NULL, -- Famille dose médicamenteuse 3
    date_alpha VARCHAR(10) NOT NULL, -- Date alpha
    prescripteur VARCHAR(80) NOT NULL, -- Prescripteur
    type_result VARCHAR(4) NOT NULL, -- Type de résultat
    hrs_prelev VARCHAR(20) NOT NULL, -- Heure de prélèvement
    preleveur VARCHAR(80) NOT NULL, -- Personne ayant prélevé l'échantillon
    ide_demandeur VARCHAR(80) NOT NULL, -- IDE demandeur
    ide_executat VARCHAR(80) NOT NULL, -- IDE exécutant
    LOCALISATION VARCHAR(80) NOT NULL, -- Localisation
    position_inside SMALLINT NOT NULL, -- Position à l'intérieur
    NumMED INT NOT NULL, -- Numéro du médecin
    limite_inf REAL NOT NULL, -- Limite inférieure
    lim_sup REAL NOT NULL, -- Limite supérieure
    VALEUR_AV VARCHAR NOT NULL, -- Valeur avant
    VALEUR_AP VARCHAR NOT NULL, -- Valeur après
    MIR_DataOwner VARCHAR(80) NOT NULL, -- Propriétaire des données MIR
    MIR_TimeStamp VARCHAR(80) NOT NULL, -- Horodatage MIR
    MIR_ETAT VARCHAR(80) NOT NULL, -- État MIR
    lib_type_exam VARCHAR(80) NOT NULL, -- Libellé du type d'examen
    id_externe_01 VARCHAR(20) NOT NULL, -- Identifiant externe
    num_enreg_exb INT UNIQUE NOT NULL, -- Numéro d'enregistrement unique
    num_type_trait_pat SMALLINT NOT NULL, -- Numéro du type de traitement du patient
    LOINC VARCHAR(80) NOT NULL, -- Code LOINC
    FOREIGN KEY (NUMPAT) REFERENCES PATIENT(NUMPAT)
);

CREATE TABLE TRAITEMENTS_EN_SEANCE (
    num_traitement INT NOT NULL, -- Numéro du traitement
    NUMPAT INT NOT NULL, -- Numéro unique du patient
    NOMPAT VARCHAR(80) NOT NULL, -- Nom du patient
    PRENOM VARCHAR(80) NOT NULL, -- Prénom du patient
    NOM_TRAIT VARCHAR(255) NOT NULL, -- Nom du traitement
    DATE_DEB_TRAIT TIMESTAMP NOT NULL, -- Date de début du traitement
    DATE_FIN_TRAIT TIMESTAMP NOT NULL, -- Date de fin du traitement
    CODE_CIP VARCHAR(20) NOT NULL, -- Code CIP du médicament
    POSOLOGIE VARCHAR(255) NOT NULL, -- Posologie du traitement
    DOSE REAL NOT NULL, -- Dose du traitement
    DOSAGE VARCHAR(80) NOT NULL, -- Dosage du traitement
    NUM_TYPE INT NOT NULL, -- Numéro du type de traitement
    cTYPE VARCHAR(80) NOT NULL, -- Type de traitement
    NUM_MEDIC INT NOT NULL, -- Numéro du médicament
    LOCALISATION VARCHAR(80) NOT NULL, -- Localisation du traitement
    FORME VARCHAR(80) NOT NULL, -- Forme du médicament
    FAMILLE VARCHAR(80) NOT NULL, -- Famille du médicament
    NEPHROLOGUE VARCHAR(80) NOT NULL, -- Nom du néphrologue
    qte_principe REAL NOT NULL, -- Quantité de principe actif
    frequence_delivrance VARCHAR(80) NOT NULL, -- Fréquence de délivrance
    appellation_internationale VARCHAR(80) NOT NULL, -- Appellation internationale
    doses_variables_par_seance BOOLEAN NOT NULL, -- Indicateur de doses variables par séance
    multi_doses_en_seance BOOLEAN NOT NULL, -- Indicateur de multi-doses en séance
    dose_constante BOOLEAN NOT NULL, -- Indicateur de dose constante
    unite VARCHAR(80) NOT NULL, -- Unité de mesure
    mode_admission VARCHAR(80) NOT NULL, -- Mode d'administration
    nb_semaines INT NOT NULL, -- Nombre de semaines de traitement
    num_enreg_trait_en_seance INT UNIQUE NOT NULL, -- Numéro d'enregistrement du traitement en séance
    administre BOOLEAN NOT NULL, -- Indicateur d'administration
    traitement_ponctuel BOOLEAN NOT NULL, -- Indicateur de traitement ponctuel
    DATE_SEANCE TIMESTAMP NOT NULL, -- Date de la séance
    NUM_SEANCE INT NOT NULL, -- Numéro de la séance
    bool_produit_en_seance BOOLEAN NOT NULL, -- Indicateur de produit en séance
    bool_medicament_controle BOOLEAN NOT NULL, -- Indicateur de médicament contrôlé
    prix_unitaire REAL NOT NULL, -- Prix unitaire du médicament
    finess_nephro VARCHAR(20) NOT NULL, -- Code FINESS du néphrologue
    num_famille_medic_type SMALLINT NOT NULL, -- Numéro de la famille du médicament
    POSTE REAL NOT NULL, -- Poste de dialyse
    finess_localisation VARCHAR(20) NOT NULL, -- Code FINESS de la localisation
    Num_Periode INT NOT NULL, -- Numéro de la période
    Periode VARCHAR(20) NOT NULL, -- Période de la séance
    type_epo VARCHAR(80) NOT NULL, -- Type d'EPO
    code_article VARCHAR(80) NOT NULL, -- Code de l'article
    ssfamille VARCHAR(80) NOT NULL, -- Sous-famille du médicament
    NOM_PRENOM_PAT VARCHAR(80) NOT NULL, -- Nom et prénom du patient
    bool_non_administre BOOLEAN NOT NULL, -- Indicateur de non-administration
    motif_non_admin VARCHAR(255) NOT NULL, -- Motif de non-administration
    validation_medecin BOOLEAN NOT NULL, -- Validation par le médecin
    Soumis_Protocole BOOLEAN NOT NULL, -- Indicateur de soumission à un protocole
    unite_administration VARCHAR(80) NOT NULL, -- Unité d'administration
    prescription_unite_admin BOOLEAN NOT NULL, -- Indicateur de prescription en unité d'administration
    prescription_en_DC BOOLEAN NOT NULL, -- Indicateur de prescription en DC
    Medicament_virtuel BOOLEAN NOT NULL, -- Indicateur de médicament virtuel
    CIP13 VARCHAR(20) NOT NULL, -- Code CIP13 du médicament
    dopant BOOLEAN NOT NULL, -- Indicateur de produit dopant
    generique BOOLEAN NOT NULL, -- Indicateur de médicament générique
    posologie_reduite VARCHAR(255) NOT NULL, -- Posologie réduite
    num_type_preparation SMALLINT NOT NULL, -- Numéro du type de préparation
    nb_prises_jour SMALLINT NOT NULL, -- Nombre de prises par jour
    num_mode_prise_jour SMALLINT NOT NULL, -- Numéro du mode de prise par jour
    prescrit_d_urgence BOOLEAN NOT NULL, -- Indicateur de prescription d'urgence
    stupefiant BOOLEAN NOT NULL, -- Indicateur de stupéfiant
    medic_anticholinergique BOOLEAN NOT NULL, -- Indicateur de médicament anticholinergique
    medic_a_risque_ANSM BOOLEAN NOT NULL, -- Indicateur de médicament à risque selon l'ANSM
    multi_doses_en_prescription BOOLEAN NOT NULL, -- Indicateur de multi-doses en prescription
    num_trait_ponctuel INT NOT NULL, -- Numéro de traitement ponctuel
    type_medic_bdm VARCHAR(80) NOT NULL, -- Type de médicament BDM
    ACL VARCHAR(20) NOT NULL, -- Code ACL
    ACL13 VARCHAR(20) NOT NULL, -- Code ACL13
    nom_medic_court VARCHAR(255) NOT NULL, -- Nom court du médicament
    id_traitement_associe VARCHAR(255) NOT NULL, -- ID du traitement associé
    heure_administration TIME NOT NULL, -- Heure d'administration
    Classe_ATC VARCHAR(20) NOT NULL -- Classe ATC du médicament
);

-- TABLE DES ABORDS VASCULAIRE 
CREATE TABLE ABORDS (
    id_abord INT UNIQUE NULL, -- ID unique de l'abord
    user_creation VARCHAR(80) NOT NULL, -- Utilisateur ayant créé l'enregistrement
    date_creation TIMESTAMP NOT NULL, -- Date de création de l'enregistrement
    heure_creation TIME NOT NULL, -- Heure de création de l'enregistrement
    NUMPAT INT NOT NULL, -- Numéro unique du patient
    NOMPAT VARCHAR(80) NOT NULL, -- Nom du patient
    PRENOMPAT VARCHAR(80) NOT NULL, -- Prénom du patient
    MIR_DataOwner VARCHAR(80) NOT NULL, -- Propriétaire des données MIR
    MIR_TimeStamp VARCHAR(80) NOT NULL, -- Timestamp MIR
    MIR_ETAT VARCHAR(80) NOT NULL, -- État MIR
    f_dosmed1 VARCHAR(80) NOT NULL, -- Dosage médicamenteux 1
    f_dosmed2 VARCHAR(80) NOT NULL, -- Dosage médicamenteux 2
    f_dosmed3 VARCHAR(80) NOT NULL, -- Dosage médicamenteux 3
    nephrologue VARCHAR(80) NOT NULL, -- Nom du néphrologue
    numnephrologue INT NOT NULL, -- Numéro du néphrologue
    type_abord VARCHAR(80) NOT NULL, -- Type d'abord
    libelle_abord VARCHAR(255) NOT NULL, -- Libellé de l'abord
    site_abord VARCHAR(80) NOT NULL, -- Site de l'abord
    date_debut TIMESTAMP NOT NULL, -- Date de début de l'abord
    date_fin TIMESTAMP NOT NULL, -- Date de fin de l'abord
    motif VARCHAR(255) NOT NULL, -- Motif de l'abord
    commentaires VARCHAR(255) NOT NULL, -- Commentaires sur l'abord
    abord_arrete BOOLEAN NOT NULL, -- Indicateur d'arrêt de l'abord
    num_statut_abord SMALLINT NOT NULL, -- Numéro du statut de l'abord
    protocoles VARCHAR(255) NOT NULL, -- Protocoles associés à l'abord
    localisation VARCHAR(80) NOT NULL, -- Localisation de l'abord
    user_arret VARCHAR(80) NOT NULL, -- Utilisateur ayant arrêté l'abord
    date_arret TIMESTAMP NOT NULL, -- Date d'arrêt de l'abord
    heure_arret TIME NOT NULL, -- Heure d'arrêt de l'abord
    num_prescription INT NOT NULL, -- Numéro de la prescription
    commentaires_arret VARCHAR(255) NOT NULL, -- Commentaires sur l'arrêt de l'abord
    ponction VARCHAR(80) NOT NULL, -- Ponction
    debit_sanguin VARCHAR(80) NOT NULL, -- Débit sanguin
    debit_veine VARCHAR(80) NOT NULL, -- Débit veineux
    numero_lot VARCHAR(255) NOT NULL, -- Numéro de lot
    validation_medecin BOOLEAN NOT NULL, -- Validation par le médecin
    date_peremption TIMESTAMP NOT NULL, -- Date de péremption
    date_pose TIMESTAMP NOT NULL, -- Date de pose de l'abord
    date_premiere_utilisation TIMESTAMP NOT NULL, -- Date de première utilisation
    code_type_abord VARCHAR(20) NOT NULL, -- Code du type d'abord
    chirurgien VARCHAR(80) NOT NULL, -- Nom du chirurgien
    numchirurgien INT NOT NULL, -- Numéro du chirurgien
    anticoagulant_artere VARCHAR(80) NOT NULL, -- Anticoagulant pour l'artère
    anticoagulant_veine VARCHAR(80) NOT NULL, -- Anticoagulant pour la veine
    vol_anticoagulant_artere REAL NOT NULL, -- Volume d'anticoagulant pour l'artère
    vol_anticoagulant_veine REAL NOT NULL, -- Volume d'anticoagulant pour la veine
    date_retrait TIMESTAMP NOT NULL -- Date de retrait de l'abord
    FOREIGN KEY (NUMPAT) REFERENCES PATIENT(NUMPAT)
);

CREATE TABLE TRAITEMENTSv2 (
    num_traitement INT UNIQUE NULL, -- Numéro unique du traitement
    date_creation TIMESTAMP NOT NULL, -- Date de création du traitement
    heure_creation TIMESTAMP NOT NULL, -- Heure de création du traitement
    user_creation VARCHAR(255) NOT NULL, -- Utilisateur ayant créé le traitement
    utilisateur_arret VARCHAR(255) NOT NULL, -- Utilisateur ayant arrêté le traitement
    user_dern_validation_pharmacie VARCHAR(255) NOT NULL, -- Utilisateur ayant validé en dernier la pharmacie
    num_medic INT NOT NULL, -- Numéro du médicament
    Medicament_virtuel BOOLEAN NOT NULL, -- Indicateur de médicament virtuel
    VDL_Virtual_CommonID VARCHAR(255) NOT NULL, -- ID commun virtuel VDL
    VDL_Virtual_CommonName VARCHAR(255) NOT NULL, -- Nom commun virtuel VDL
    med_generique BOOLEAN NOT NULL, -- Indicateur de médicament générique
    fourni_par_patient BOOLEAN NOT NULL, -- Indicateur de médicament fourni par le patient
    Produit_dopan BOOLEAN NOT NULL, -- Indicateur de produit dopant
    produit_stupefiant BOOLEAN NOT NULL, -- Indicateur de produit stupéfiant
    non_substituable BOOLEAN NOT NULL, -- Indicateur de non-substituabilité
    mode_admission VARCHAR(255) NOT NULL, -- Mode d'administration
    code_non_substituable VARCHAR(255) NOT NULL, -- Code de non-substituabilité
    Num_Type INT NOT NULL, -- Numéro du type de traitement
    NOM VARCHAR(255) NOT NULL, -- Nom du traitement
    NumNephro INT NOT NULL, -- Numéro du néphrologue
    nephrologue VARCHAR(255) NOT NULL, -- Nom du néphrologue
    protocole_bool_complication BOOLEAN NOT NULL, -- Indicateur de protocole avec complication
    soumis_a_protocole BOOLEAN NOT NULL, -- Indicateur de soumission à un protocole
    TRAIT_ON BOOLEAN NOT NULL, -- Indicateur de traitement en cours
    TRAIT_OFF BOOLEAN NOT NULL, -- Indicateur de traitement arrêté
    a_arreter_en_date_de_fin BOOLEAN NOT NULL, -- Indicateur d'arrêt à la date de fin
    num_trait_origine INT NOT NULL, -- Numéro du traitement d'origine
    arrete_d_urgence BOOLEAN NOT NULL, -- Indicateur d'arrêt d'urgence
    commentaires VARCHAR(255) NOT NULL, -- Commentaires sur le traitement
    complement_poso VARCHAR(255) NOT NULL, -- Complément de posologie
    motif_prescription VARCHAR(255) NOT NULL, -- Motif de la prescription
    commentaires_arret VARCHAR(255) NOT NULL, -- Commentaires sur l'arrêt du traitement
    commentaires_pharmacie VARCHAR(255) NOT NULL, -- Commentaires de la pharmacie
    motif_arret VARCHAR(255) NOT NULL, -- Motif de l'arrêt du traitement
    date_arret TIMESTAMP NOT NULL, -- Date d'arrêt du traitement
    date_debut TIMESTAMP NOT NULL, -- Date de début du traitement
    date_dern_val_pharmacie TIMESTAMP NOT NULL, -- Date de dernière validation par la pharmacie
    date_derniere_administration TIMESTAMP NOT NULL, -- Date de dernière administration
    date_fin TIMESTAMP NOT NULL, -- Date de fin du traitement
    date_prochaine_delivrance TIMESTAMP NOT NULL, -- Date de prochaine délivrance
    NOMPAT VARCHAR(255) NOT NULL, -- Nom du patient
    NUMPAT INT NOT NULL, -- Numéro unique du patient
    PRENOM VARCHAR(255) NOT NULL, -- Prénom du patient
    heure_arret TIMESTAMP NOT NULL, -- Heure d'arrêt du traitement
    heure_debut TIMESTAMP NOT NULL, -- Heure de début du traitement
    heure_dern_val_pharmacie TIMESTAMP NOT NULL, -- Heure de dernière validation par la pharmacie
    id_traitement_associe VARCHAR(255) NOT NULL, -- ID du traitement associé
    num_phase_dialyse INT NOT NULL, -- Numéro de la phase de dialyse
    unite VARCHAR(255) NOT NULL, -- Unité de mesure
    prescription_json VARCHAR(255) NOT NULL, -- Prescription en format JSON
    num_famille_medic_type INT NOT NULL, -- Numéro de la famille du médicament (1 signifie EPO)
    prescrit_d_urgence BOOLEAN NOT NULL, -- Indicateur de prescription d'urgence
    multi_doses_en_seance BOOLEAN NOT NULL, -- Indicateur de multi-doses en séance
    num_enreg_infection INT NOT NULL, -- Numéro d'enregistrement de l'infection
    num_statut_prescription INT NOT NULL, -- Numéro du statut de la prescription
    num_etat_securisation INT NOT NULL, -- Numéro de l'état de sécurisation
    dose_egale BOOLEAN NOT NULL, -- Indicateur de dose égale
    id_traitV1 INT NOT NULL -- ID du traitement V1
    FOREIGN KEY (NUMPAT) REFERENCES PATIENT(NUMPAT)
);

CREATE TABLE PLANNING_DIAL (
    NUMPAT INT NOT NULL, -- Numéro unique du patient
    NOMPAT VARCHAR(80) NOT NULL, -- Nom du patient
    PRENOM VARCHAR(80) NOT NULL, -- Prénom du patient
    TYPE_SEANCE VARCHAR(80) NOT NULL, -- Type de séance
    POSTE REAL NOT NULL, -- Poste de dialyse
    TYPE_MALADE VARCHAR(80) NOT NULL, -- Type de malade
    cDATE TIMESTAMP NOT NULL, -- Date de la séance
    PERIODE VARCHAR(20) NOT NULL, -- Période de la séance
    LOCALISATION VARCHAR(80) NOT NULL, -- Localisation de la séance
    motif_absent VARCHAR(20) NOT NULL, -- Motif d'absence
    duree_dial INTERVAL NOT NULL, -- Durée de la dialyse
    hr_debut TIME NOT NULL, -- Heure de début de la séance
    Num_Periode INT NOT NULL, -- Numéro de la période
    hr_fin TIME NOT NULL, -- Heure de fin de la séance
    dern_utilisateur VARCHAR(80) NOT NULL, -- Dernier utilisateur ayant modifié l'enregistrement
    ancienne_date TIMESTAMP NOT NULL -- Ancienne date de la séance
    FOREIGN KEY (NUMPAT) REFERENCES PATIENT(NUMPAT)
);

CREATE TABLE TRANSMISSIONS_CIBLEES (
    num_enreg_tc INT AUTO_INCREMENT PRIMARY KEY, -- Numéro d'enregistrement de la transmission ciblée
    user_creation VARCHAR(80) NOT NULL, -- Utilisateur ayant créé l'enregistrement
    date_creation DATE NOT NULL, -- Date de création de l'enregistrement
    heure_creation TIME NOT NULL, -- Heure de création de l'enregistrement
    user_dern_modif VARCHAR(80) NOT NULL, -- Utilisateur ayant effectué la dernière modification
    date_dern_modif DATE NOT NULL, -- Date de la dernière modification
    heure_dern_modif TIME NOT NULL, -- Heure de la dernière modification
    NUMPAT INT NOT NULL, -- Numéro du patient
    NOMPAT VARCHAR(80) NOT NULL, -- Nom du patient
    PRENOMPAT VARCHAR(80) NOT NULL, -- Prénom du patient
    num_statut_tc TINYINT NOT NULL, -- Statut de la transmission ciblée
    localisation VARCHAR(80) NOT NULL, -- Localisation du patient ou de l'événement
    ids_elts TEXT NOT NULL, -- Identifiants des éléments associés
    libelles_elts TEXT NOT NULL, -- Libellés des éléments associés
    ids_actions TEXT NOT NULL, -- Identifiants des actions associées
    libelles_actions TEXT NOT NULL, -- Libellés des actions associées
    ids_resultats TEXT NOT NULL, -- Identifiants des résultats associés
    libelles_resultats TEXT NOT NULL, -- Libellés des résultats associés
    commentaires_tc TEXT NOT NULL, -- Commentaires sur la transmission ciblée
    commentaires_medecin TEXT NOT NULL, -- Commentaires du médecin
    num_statut_comment_med TINYINT NOT NULL, -- Statut des commentaires du médecin
    commentaires_patient TEXT NOT NULL, -- Commentaires du patient
    num_statut_comment_pat TINYINT NOT NULL, -- Statut des commentaires du patient
    ide VARCHAR(80) NOT NULL, -- Identifiant de l'infirmier(ère)
    num_ide INT NOT NULL, -- Numéro de l'infirmier(ère)
    medecin VARCHAR(80) NOT NULL, -- Nom du médecin
    num_medecin INT NOT NULL, -- Numéro du médecin
    num_seance INT NOT NULL, -- Numéro de la séance
    num_niveau_priorite TINYINT NOT NULL, -- Niveau de priorité de la transmission
    f_dosmed1 VARCHAR(80) NOT NULL, -- Champ supplémentaire pour le dossier médical 1
    f_dosmed2 VARCHAR(80) NOT NULL, -- Champ supplémentaire pour le dossier médical 2
    f_dosmed3 VARCHAR(80) NOT NULL, -- Champ supplémentaire pour le dossier médical 3
    date_action DATE NOT NULL, -- Date de l'action
    heure_action TIME NOT NULL, -- Heure de l'action
    id_cible_initiale INT NOT NULL, -- Identifiant de la cible initiale
    verrou BOOLEAN NOT NULL, -- Indicateur de verrouillage de l'enregistrement
    commentaires_actions TEXT NOT NULL, -- Commentaires sur les actions
    ids_elts2 TEXT NOT NULL, -- Identifiants des éléments secondaires associés
    ids_actions2 TEXT NOT NULL, -- Identifiants des actions secondaires associées
    ids_resultats2 TEXT NOT NULL, -- Identifiants des résultats secondaires associés
    num_enreg_table INT NOT NULL, -- Numéro d'enregistrement dans une autre table
    num_table INT NOT NULL, -- Numéro de la table associée
    id_abord INT NOT NULL -- Identifiant de l'abord
    FOREIGN KEY (NUMPAT) REFERENCES PATIENT(NUMPAT)
);

-- TABLE DES DIRECTIVES ANTICIPEES
CREATE TABLE virtuel_rec_data_arch (
    id_vrds_sea_arch VARCHAR(80) NOT NULL, -- Identifiant unique pour l'archive de séance virtuelle. Ce champ est la clé primaire de la table.
    NUMPAT INT NOT NULL, -- Numéro de patient. Ce champ représente l'identifiant du patient.
    NUM_SEANCE INT NOT NULL, -- Numéro de la séance. Ce champ représente l'identifiant de la séance associée au patient.
    date_seance DATE NOT NULL, -- Date de la séance. Ce champ stocke la date à laquelle la séance a eu lieu.
    NOM_PAT VARCHAR(80) NOT NULL, -- Nom du patient. Ce champ contient le nom de famille du patient.
    PRENOM_PAT VARCHAR(80) NOT NULL, -- Prénom du patient. Ce champ contient le prénom du patient.
    liste_infos_vrds_sea TEXT NOT NULL, -- Liste d'informations relatives à la séance virtuelle. Ce champ stocke des détails supplémentaires concernant la séance.
    liste_ids_vrds_sea_prevus TEXT NOT NULL, -- Liste des identifiants des séances virtuelles prévues. Ce champ contient les ID des séances programmées.
    liste_ids_vrds_sea_renseignes TEXT NOT NULL, -- Liste des identifiants des séances virtuelles renseignées. Ce champ contient les ID des séances renseignées.
    MIR_DataOwner VARCHAR(80) NOT NULL, -- Propriétaire des données MIR. Ce champ spécifie le propriétaire des données liées à MIR.
    MIR_TimeStamp VARCHAR(40) NOT NULL, -- Horodatage MIR. Ce champ enregistre la date et l'heure de l'interaction avec MIR.
    MIR_ETAT VARCHAR(80) NOT NULL, -- État MIR. Ce champ indique l'état ou le statut actuel dans le système MIR.
    PRIMARY KEY (id_vrds_sea_arch), -- Définition de la clé primaire sur le champ `id_vrds_sea_arch`.
    UNIQUE (id_vrds_sea_arch) -- Assure que le champ `id_vrds_sea_arch` est unique dans la table.
);

-- TABLE DES PROPRIETE DU PATIENT
CREATE TABLE PROPRIETEPAT (
    TYPE_RECORD INT NOT NULL, -- Type d'enregistrement
    NUMPAT INT NOT NULL, -- Numéro unique du patient
    ID_PP INT UNIQUE NOT NULL, -- Identifiant unique de la propriété
    ID_LINKED INT NOT NULL, -- Identifiant lié
    NOM VARCHAR(255) NOT NULL, -- Nom du patient
    PRENOM VARCHAR(255) NOT NULL, -- Prénom du patient
    AUTRE_PRENOMS VARCHAR(255) NOT NULL, -- Autres prénoms du patient
    NIVEAU_ETUDE VARCHAR(255) NOT NULL, -- Niveau d'étude du patient
    COM_EN_FRANCAIS VARCHAR(255) NOT NULL, -- Compétence en français
    LECTURE_DU_FRANCAIS VARCHAR(255) NOT NULL, -- Lecture du français
    AUTRES_NUMEROS VARCHAR(255) NOT NULL, -- Autres numéros associés
    QUALITE_CODE_CV INT NOT NULL, -- Code qualité de la carte vitale
    SEJOUR_ETRANGER VARCHAR(255) NOT NULL, -- Séjour à l'étranger
    NB_TRANSFUSION_TOTAL VARCHAR(255) NOT NULL, -- Nombre total de transfusions
    FUMEUR INT, -- Statut de fumeur
    FUMEUR_DEBUT DATE NOT NULL, -- Date de début du tabagisme
    FUMEUR_FIN DATE NOT NULL, -- Date de fin du tabagisme
    FUMEUR_NB_CIG_JOUR INT, -- Nombre de cigarettes par jour
    ALCOOL INT, -- Consommation d'alcool
    ALCOOL_FREQ VARCHAR(255) NOT NULL, -- Fréquence de consommation d'alcool
    ALCOOL_CONSO VARCHAR(255) NOT NULL, -- Consommation d'alcool
    DATE_CREATION DATE NOT NULL, -- Date de création de l'enregistrement
    USER_CREATION VARCHAR(255) NOT NULL, -- Utilisateur ayant créé l'enregistrement
    HEURE_CREATION TIME NOT NULL, -- Heure de création de l'enregistrement
    DATE_MODIF DATE NOT NULL, -- Date de modification de l'enregistrement
    USER_MODIF VARCHAR(255) NOT NULL, -- Utilisateur ayant modifié l'enregistrement
    HEURE_MODIF TIME NOT NULL, -- Heure de modification de l'enregistrement
    DOULEUR INT NOT NULL, -- Indicateur de douleur
    DATE_NAISSANCE DATE NOT NULL, -- Date de naissance du patient
    RANG_NAISSANCE INT NOT NULL, -- Rang de naissance
    NIR_INDIVIDU VARCHAR(255) NOT NULL, -- Numéro d'inscription au répertoire individuel
    NIR_OD VARCHAR(255) NOT NULL, -- Numéro d'inscription OD
    INS VARCHAR(255) NOT NULL, -- Identifiant INS
    STATUT_INS INT NOT NULL, -- Statut de l'INS
    OID VARCHAR(255) NOT NULL, -- Identifiant OID
    STATUT_DMP INT NOT NULL, -- Statut du dossier medical partagé DMP  : 1 = ouvert, 2 = fermé, 3 ou 0 = inexistant
    INFOS_FERMETURE_DMP VARCHAR(255) NOT NULL, -- Informations de fermeture du dossier medical partagé DMP
    NOM_USUEL_CV VARCHAR(255) NOT NULL, -- Nom usuel sur la carte vitale
    NOM_PATRONYMIQUE_CV VARCHAR(255) NOT NULL, -- Nom patronymique sur la carte vitale
    PRENOM_CV VARCHAR(255) NOT NULL, -- Prénom sur la carte vitale
    DATE_NAISSANCE_CV VARCHAR(255) NOT NULL, -- Date de naissance sur la carte vitale
    TYPE_CARTE_VITALE INT NOT NULL, -- Type de carte vitale
    DATE_REFUS_CONSENTEMENT_DMP DATE NOT NULL, -- Date de refus de consentement dossier medical partagé DMP
    QUALITE_CV VARCHAR(255) NOT NULL, -- Qualité sur la carte vitale
    NUM_SERIE_CV VARCHAR(30) NOT NULL, -- Numéro de série de la carte vitale
    INDEX_BENEF_SUR_CV VARCHAR(2) NOT NULL, -- Index bénéficiaire sur la carte vitale
    INS_ARCHIVE_INSI VARCHAR(255) NOT NULL, -- Archive INSI
    HEURE_FIN TIME NOT NULL, -- Heure de fin
    FIN_DIALYSE BOOLEAN NOT NULL, -- Fin de dialyse
    DPCA_DUREE_TOTALE TIME NOT NULL, -- Durée totale de DPCA
    DPCA_INFU_TOTALE INT NOT NULL, -- Infusion totale de DPCA
    MSS_REFUS_ENVOI_PAT BOOLEAN NOT NULL -- Refus d'envoi de données patient MSS,
    FOREIGN KEY (NUMPAT) REFERENCES PATIENT(NUMPAT) -- numéro du patient
);

-- TABLE DES EPHEMERIDES 
CREATE TABLE EPHER_PAT (
    NUMPAT INT NOT NULL, -- Numéro unique du patient
    cDATE TIMESTAMP NOT NULL, -- Date associée à l'enregistrement
    cTEXTE TEXT NOT NULL, -- Texte associé
    nom VARCHAR(80) NOT NULL, -- Nom du patient
    prenom VARCHAR(80) NOT NULL, -- Prénom du patient
    borne_inf TIMESTAMP NOT NULL, -- Borne inférieure
    borne_sup TIMESTAMP NOT NULL, -- Borne supérieure
    delivre BOOLEAN NOT NULL, -- Indicateur de délivrance
    titre VARCHAR(80) NOT NULL, -- Titre associé
    LOCALISATION VARCHAR(80) NOT NULL, -- Localisation
    nb_period SMALLINT NOT NULL, -- Nombre de périodes
    period SMALLINT NOT NULL, -- Période
    lib_per VARCHAR(80) NOT NULL, -- Libellé de la période
    num_serie INT NOT NULL, -- Numéro de série
    date_creation TIMESTAMP NOT NULL, -- Date de création
    createur VARCHAR(80) NOT NULL, -- Créateur de l'enregistrement
    modificateur VARCHAR(80) NOT NULL, -- Modificateur de l'enregistrement
    date_limite TIMESTAMP NOT NULL, -- Date limite
    nb_fois INT NOT NULL, -- Nombre de fois
    dern_utilisateur VARCHAR(80) NOT NULL, -- Dernier utilisateur
    date_dern_modif TIMESTAMP NOT NULL, -- Date de dernière modification
    heure_dern_modif TIME NOT NULL, -- Heure de dernière modification
    ParamSpec TEXT NOT NULL, -- Spécification des paramètres
    idEpher INT UNIQUE NOT NULL, -- Identifiant unique de l'enregistrement
    cHeure TIME NOT NULL, -- Heure associée
    id_externe_RDV TEXT NOT NULL, -- Identifiant externe du rendez-vous
    id_emetteur TEXT NOT NULL, -- Identifiant de l'émetteur
    cType TEXT NOT NULL, -- Type de l'enregistrement
    num_id_enreg_associe INT NOT NULL, -- Numéro d'enregistrement associé
    teleconsultation INT NOT NULL, -- Indicateur de téléconsultation
    roomConsultation TEXT NOT NULL, -- Salle de consultation
    PRIMARY KEY (idEpher) -- Clé primaire sur le champ idEpher
);

-- TABLE PATIENTS
CREATE TABLE PATIENT (
    NUMPAT INT UNIQUE NOT NULL, -- Numéro unique du patient
    NOM VARCHAR(100) NOT NULL, -- Nom du patient
    PRENOM VARCHAR(100) NOT NULL, -- Prénom du patient
    ADRESSE1 VARCHAR(80) NOT NULL, -- Adresse principale du patient
    ADRESSE2 VARCHAR(80) NOT NULL, -- Adresse secondaire du patient
    TEL VARCHAR(30) NOT NULL, -- Numéro de téléphone du patient
    DAT_NAIS TIMESTAMP NOT NULL, -- Date de naissance du patient
    NUM_SS VARCHAR(20) NOT NULL, -- Numéro de sécurité sociale du patient
    nephrologue VARCHAR(80) NOT NULL, -- Nom du néphrologue
    DPCA_prescription TEXT NOT NULL, -- Prescription DPCA
    AGE SMALLINT, -- Âge du patient
    CAISSE VARCHAR(80) NOT NULL, -- Caisse d'assurance du patient
    persaprev1 VARCHAR(80) NOT NULL, -- Personne à prévenir en cas d'urgence (1)
    genre VARCHAR(15) NOT NULL, -- Genre du patient
    DESSIN BYTEA NOT NULL, -- Dessin associé au patient
    LIEUX VARCHAR(25) NOT NULL, -- Lieu de résidence du patient
    persaprev2 VARCHAR(80) NOT NULL, -- Personne à prévenir en cas d'urgence (2)
    prescript_insuline TEXT NOT NULL, -- Prescription d'insuline
    HOSPITALISATION VARCHAR(20) NOT NULL, -- Statut d'hospitalisation
    LOCALISATION VARCHAR(80) NOT NULL, -- Localisation du patient
    Diag_princip VARCHAR(255) NOT NULL, -- Diagnostic principal
    diag_sec1 VARCHAR(80) NOT NULL, -- Diagnostic secondaire
    resident BOOLEAN NOT NULL, -- Statut de résidence
    nbr_jrs_dialyse SMALLINT, -- Nombre de jours de dialyse
    TRAITANT VARCHAR(80) NOT NULL, -- Médecin traitant
    NOM_NAISSANCE VARCHAR(100) NOT NULL, -- Nom de naissance du patient
    POIDS REAL, -- Poids SEC du patient
    pays VARCHAR(80) NOT NULL, -- Pays de résidence du patient
    SEXE VARCHAR(20) NOT NULL, -- Sexe du patient
    COMMENT_GEN_ BYTEA NOT NULL, -- Commentaire général
    PAT_EDIT VARCHAR(40) NOT NULL, -- Éditeur de patient
    MED2 VARCHAR(80) NOT NULL, -- Médecin 2
    MED3 VARCHAR(80) NOT NULL, -- Médecin 3
    MED4 VARCHAR(80) NOT NULL, -- Médecin 4
    MED5 VARCHAR(80) NOT NULL, -- Médecin 5
    TEMPO_ BYTEA NOT NULL, -- Temporaire
    EDIT_FORMULE VARCHAR(80) NOT NULL, -- Formule d'édition
    EXAM_CAPI TEXT NOT NULL, -- Examen capillaire
    soundex VARCHAR(21) NOT NULL, -- Soundex
    CODPOS VARCHAR(10) NOT NULL, -- Code postal
    VILLE VARCHAR(80) NOT NULL, -- Ville
    INDIC TEXT NOT NULL, -- Indicateur
    ADR_SEC1 VARCHAR(80) NOT NULL, -- Adresse secondaire (1)
    ADR_SEC2 VARCHAR(60) NOT NULL, -- Adresse secondaire (2)
    VILLE2 VARCHAR(80) NOT NULL, -- Ville secondaire
    CODPOS2 VARCHAR(10) NOT NULL, -- Code postal secondaire
    tel_persprev1 VARCHAR(30) NOT NULL, -- Téléphone de la personne à prévenir (1)
    IMAGE_TEMPO BYTEA NOT NULL, -- Image temporaire
    COMMENT_TXT TEXT NOT NULL, -- Commentaire texte
    shema_ BYTEA NOT NULL, -- Schéma
    LUNDI BOOLEAN NOT NULL, -- Indicateur pour lundi
    MARDI BOOLEAN NOT NULL, -- Indicateur pour mardi
    MERCREDI BOOLEAN NOT NULL, -- Indicateur pour mercredi
    JEUDI BOOLEAN NOT NULL, -- Indicateur pour jeudi
    VENDREDI BOOLEAN NOT NULL, -- Indicateur pour vendredi
    SAMEDI BOOLEAN NOT NULL, -- Indicateur pour samedi
    DIMANCHE BOOLEAN NOT NULL, -- Indicateur pour dimanche
    PERJ1 REAL, -- Période 1
    PERJ2 REAL, -- Période 2
    PERJ3 REAL, -- Période 3
    PERJ4 REAL, -- Période 4
    PERJ5 REAL, -- Période 5
    PERJ6 REAL, -- Période 6
    PERJ7 REAL, -- Période 7
    num_facturation VARCHAR(20) NOT NULL, -- Numéro de facturation
    DATE_PREM_DIAL TIMESTAMP NOT NULL, -- Date de la première dialyse
    DUREE_DIALYSE TIME NOT NULL, -- Durée de la dialyse
    TA_HAB_COUCHE VARCHAR(6) NOT NULL, -- Tension habituelle couchée
    TA_HAB_DEB VARCHAR(6) NOT NULL, -- Tension habituelle debout
    POID_IDEAL REAL, -- Poids idéal
    PRISE_POIDS_HAB REAL, -- Prise de poids habituelle
    NEPHRO_INITIALE VARCHAR(80) NOT NULL, -- Néphrologue initial
    GR_SANGUIN VARCHAR(2) NOT NULL, -- Groupe sanguin
    RHESUS VARCHAR(15) NOT NULL, -- Rhésus
    Hbs_ag VARCHAR(20) NOT NULL, -- Hbs antigène
    DATE_DERN_TRANS TIMESTAMP NOT NULL, -- Date de la dernière transfusion
    VOL_DIURESE REAL, -- Volume de diurèse
    PRISE_POID_HAB REAL, -- Prise de poids habituelle
    POID_A_PERDRE REAL, -- Poids à perdre
    DIALYSEUR VARCHAR(80) NOT NULL, -- Dialyseur
    bicar VARCHAR(80) NOT NULL, -- Bicarbonate
    HEPARINE_CHARGE VARCHAR(80) NOT NULL, -- Charge d'héparine
    HEPARINE_ENTRET VARCHAR(80) NOT NULL, -- Entretien d'héparine
    HEPARINE_ARRET VARCHAR(80) NOT NULL, -- Arrêt d'héparine
    sodium REAL, -- Sodium
    uf_const_tolere VARCHAR(20) NOT NULL, -- UF constante tolérée
    tel_ambu VARCHAR(30) NOT NULL, -- Téléphone ambulancier
    ambulancier VARCHAR(80) NOT NULL, -- Ambulancier
    type_patient VARCHAR(80) NOT NULL, -- Type de patient
    dern_date_plan TIMESTAMP NOT NULL, -- Dernière date de planification
    POSTE REAL, -- Poste
    TYPE_SEANCE VARCHAR(80) NOT NULL, -- Type de séance
    PHOTO BYTEA NOT NULL, -- Photo du patient
    prothese VARCHAR(40) NOT NULL, -- Prothèse
    cote VARCHAR(20) NOT NULL, -- Côté
    aiguille VARCHAR(80) NOT NULL, -- Aiguille
    prem_dial_centr TIMESTAMP NOT NULL, -- Première dialyse centrale
    date_greffe TIMESTAMP NOT NULL, -- Date de greffe
    attente_greffe_ TIMESTAMP NOT NULL, -- En attente de greffe
    derniere_transf TIMESTAMP NOT NULL, -- Dernière transfusion
    Hbs_ac VARCHAR(20) NOT NULL, -- Hbs anticorps
    VHC VARCHAR(10) NOT NULL, -- Virus de l'hépatite C
    HIV VARCHAR(10) NOT NULL, -- Virus de l'immunodéficience humaine
    dialyseur_dep TIMESTAMP NOT NULL, -- Dialyseur de départ
    ancien_dialys VARCHAR(20) NOT NULL, -- Ancien dialyseur
    dialysat_dep TIMESTAMP NOT NULL, -- Dialysat de départ
    anc_dialysat VARCHAR(20) NOT NULL, -- Ancien dialysat
    pds_base_dep TIMESTAMP NOT NULL, -- Poids de base de départ
    anc_pds_base REAL, -- Ancien poids de base
    pv_habitu REAL, -- Poids volumique habituel
    dure_dial_dep TIMESTAMP NOT NULL, -- Durée de dialyse de départ
    anc_dur_dial TIME NOT NULL, -- Ancienne durée de dialyse
    vit_pompe_sng REAL, -- Vitesse de la pompe sanguine
    fav TEXT NOT NULL, -- Fistule artério-veineuse
    site VARCHAR(20) NOT NULL, -- Site
    ponction VARCHAR(80) NOT NULL, -- Ponction
    k VARCHAR(20) NOT NULL, -- Potassium
    glucose VARCHAR(20) NOT NULL, -- Glucose
    ca REAL, -- Calcium
    vaccin VARCHAR(40) NOT NULL, -- Vaccin
    date_vaccin TIMESTAMP NOT NULL, -- Date de vaccination
    dose_vaccin SMALLINT, -- Dose de vaccin
    rincage VARCHAR(20) NOT NULL, -- Rinçage
    dern_prise_pds REAL, -- Dernière prise de poids
    dern_pds_av REAL, -- Dernier poids avant
    dern_pds_ap REAL, -- Dernier poids après
    pathologie_asso TEXT NOT NULL, -- Pathologie associée
    KT VARCHAR(80) NOT NULL, -- Cathéter
    double_pompe BOOLEAN NOT NULL, -- Double pompe
    air VARCHAR(20) NOT NULL, -- Air
    ALLERGIE TEXT NOT NULL, -- Allergie
    bicarb REAL, -- Bicarbonate
    NA_depuis TIMESTAMP NOT NULL, -- Sodium depuis
    Ca_depuis TIMESTAMP NOT NULL, -- Calcium depuis
    Hco3_depuis TIMESTAMP NOT NULL, -- Bicarbonate depuis
    anc_na REAL, -- Ancien sodium
    anc_ca REAL, -- Ancien calcium
    anc_hco3 REAL, -- Ancien bicarbonate
    uf_cons_tol_dep TIMESTAMP NOT NULL, -- UF constante tolérée depuis
    aiguille_depuis TIMESTAMP NOT NULL, -- Aiguille depuis
    aiguille_artere VARCHAR(80) NOT NULL, -- Aiguille artérielle
    kt_long_duree VARCHAR(80) NOT NULL, -- Cathéter de longue durée
    HBPM_charge VARCHAR(20) NOT NULL, -- Charge HBPM
    hbpm_entre VARCHAR(20) NOT NULL, -- Entre HBPM
    debit_hab VARCHAR(20) NOT NULL, -- Débit habituel
    Hep_vein_kt VARCHAR(80) NOT NULL, -- Héparine veine cathéter
    hep_art_kt VARCHAR(80) NOT NULL, -- Héparine artère cathéter
    centre_transpla VARCHAR(80) NOT NULL, -- Centre de transplantation
    Absent BOOLEAN NOT NULL, -- Absent
    date_deces TIMESTAMP NOT NULL, -- Date de décès
    epo_lundi REAL, -- Dose d'EPO le lundi
    epo_mardi REAL, -- Dose d'EPO le mardi
    epo_mercredi REAL, -- Dose d'EPO le mercredi
    epo_jeudi REAL, -- Dose d'EPO le jeudi
    epo_vendredi REAL, -- Dose d'EPO le vendredi
    epo_samedi REAL, -- Dose d'EPO le samedi
    epo_dimanche REAL, -- Dose d'EPO le dimanche
    nom_conjoint VARCHAR(80) NOT NULL, -- Nom du conjoint
    prenom_conjoint VARCHAR(80) NOT NULL, -- Prénom du conjoint
    adr1_conjoint VARCHAR(80) NOT NULL, -- Adresse 1 du conjoint
    adr2_conjoint VARCHAR(80) NOT NULL, -- Adresse 2 du conjoint
    cp_conjoint VARCHAR(10) NOT NULL, -- Code postal du conjoint
    ville_conjoint VARCHAR(80) NOT NULL, -- Ville du conjoint
    tel_conjoint VARCHAR(30) NOT NULL, -- Téléphone du conjoint
    tel_persprev2 VARCHAR(30) NOT NULL, -- Téléphone de la personne à prévenir (2)
    duree_dial_hebd TIME NOT NULL, -- Durée hebdomadaire de dialyse
    num_base_patient VARCHAR(20) NOT NULL, -- Numéro de base du patient
    DP BOOLEAN NOT NULL, -- Dialyse péritonéale
    DATE_DP VARCHAR(80) NOT NULL, -- Date de début de la dialyse péritonéale
    type_epo VARCHAR(80) NOT NULL, -- Type d'EPO
    DIABETE BOOLEAN NOT NULL, -- Indicateur de diabète
    type_dialysat VARCHAR(80) NOT NULL, -- Type de dialysat
    type_restit VARCHAR(80) NOT NULL, -- Type de restitution
    planif_jusquo TIMESTAMP NOT NULL, -- Planification jusqu'à
    genre_patient VARCHAR(80) NOT NULL, -- Genre du patient
    dextro_D BOOLEAN NOT NULL, -- Dextro le dimanche
    dextro_M BOOLEAN NOT NULL, -- Dextro le lundi
    dextro_f BOOLEAN NOT NULL, -- Dextro le vendredi
    profil_na VARCHAR(40) NOT NULL, -- Profil sodium
    profil_hco3 VARCHAR(40) NOT NULL, -- Profil bicarbonate
    profil_uf VARCHAR(40) NOT NULL, -- Profil ultrafiltration
    anticoagulant VARCHAR(25) NOT NULL, -- Anticoagulant
    TAILLE REAL, -- Taille du patient
    TEMPERATURE REAL, -- Température du patient
    debut_epo TIMESTAMP NOT NULL, -- Début de l'EPO
    modif_epo TIMESTAMP NOT NULL, -- Modification de l'EPO
    fin_epo TIMESTAMP NOT NULL, -- Fin de l'EPO
    dose_deb_epo SMALLINT, -- Dose de début d'EPO
    dos_mod_epo SMALLINT, -- Dose modifiée d'EPO
    dose_fin_epo SMALLINT, -- Dose de fin d'EPO
    ktv REAL, -- Kt/V
    motif_absence VARCHAR(80) NOT NULL, -- Motif d'absence
    type_regime TEXT NOT NULL, -- Type de régime
    type_repas VARCHAR(80) NOT NULL, -- Type de repas
    bool_repas BOOLEAN NOT NULL, -- Indicateur de repas
    interdiction TEXT NOT NULL, -- Interdictions
    num_statut_autonome_insuline SMALLINT NOT NULL, -- Statut d'autonomie pour l'insuline
    heure_deb_dialyse TIME NOT NULL, -- Heure de début de la dialyse
    numero_mirroir VARCHAR(80) NOT NULL, -- Numéro de miroir
    gsm VARCHAR(30) NOT NULL, -- Numéro de GSM
    ACTE1 VARCHAR(80) NOT NULL, -- Acte 1
    CODE_acte1 VARCHAR(10) NOT NULL, -- Code de l'acte 1
    acte2 VARCHAR(80) NOT NULL, -- Acte 2
    code_acte2 VARCHAR(20) NOT NULL, -- Code de l'acte 2
    Mode_Transport VARCHAR(80) NOT NULL, -- Mode de transport
    Nationalite VARCHAR(20) NOT NULL, -- Nationalité
    Date_Deter1 TIMESTAMP NOT NULL, -- Date de détermination 1
    Date_Deter2 TIMESTAMP NOT NULL, -- Date de détermination 2
    Date_AIR TIMESTAMP NOT NULL, -- Date de l'AIR
    Spec_Patient VARCHAR(20) NOT NULL, -- Spécificité du patient
    Patient_en_chg BOOLEAN NOT NULL, -- Patient en charge
    epo_heb_unit INT, -- Unité hebdomadaire d'EPO
    dose_epo_hebdo SMALLINT, -- Dose hebdomadaire d'EPO
    archive_epo TEXT NOT NULL, -- Archive EPO
    motif_deces TEXT NOT NULL, -- Motif de décès
    Profession TEXT NOT NULL, -- Profession
    type_ligne VARCHAR(80) NOT NULL, -- Type de ligne
    archive_abord_vasc TEXT NOT NULL, -- Archive abord vasculaire
    arch_greffe TEXT NOT NULL, -- Archive greffe
    dp_chg_poche VARCHAR(20) NOT NULL, -- Changement de poche DP
    dp_matos VARCHAR(80) NOT NULL, -- Matériel DP
    dp_dat_imp_cath TIMESTAMP NOT NULL, -- Date d'implantation du cathéter DP
    dp_dat_debut TIMESTAMP NOT NULL, -- Date de début DP
    dp_type_cath VARCHAR(80) NOT NULL, -- Type de cathéter DP
    dp_posit_asp VARCHAR(50) NOT NULL, -- Position d'aspiration DP
    dp_probleme VARCHAR(80) NOT NULL, -- Problème DP
    dp_type_insuline VARCHAR(20) NOT NULL, -- Type d'insuline DP
    dp_staphyloc BOOLEAN NOT NULL, -- Staphylocoque DP
    dp_PDC_date TIMESTAMP NOT NULL, -- Date PDC DP
    dp_PDC_val VARCHAR(80) NOT NULL, -- Valeur PDC DP
    dp_clairance_uree_date TIMESTAMP NOT NULL, -- Date de clairance urée DP
    dp_clairance_uree_val REAL, -- Valeur de clairance urée DP
    dp_peritoneale_date TIMESTAMP NOT NULL, -- Date péritonéale DP
    dp_peritoneale_val REAL, -- Valeur péritonéale DP
    dp_prescription VARCHAR(20) NOT NULL, -- Prescription DP
    dpa_hrs_duree TIME NOT NULL, -- Durée en heures DPA
    dpa_heure_jour TIME NOT NULL, -- Heure par jour DPA
    dpa_dose_dial REAL, -- Dose de dialyse DPA
    dpa2_vol_infusion REAL, -- Volume d'infusion DPA2
    dpa_nbr_cycle REAL, -- Nombre de cycles DPA
    dpa_glucose REAL, -- Glucose DPA
    dpa_tps_par_stase SMALLINT, -- Temps par stase DPA
    dpa_tps_drain SMALLINT, -- Temps de drainage DPA
    dpa_drain_fin SMALLINT, -- Drainage final DPA
    dpa_vol_drain REAL, -- Volume de drainage DPA
    dpa2_type_solution VARCHAR(20) NOT NULL, -- Type de solution DPA2
    dpa_vol_infusion REAL, -- Volume d'infusion DPA
    dpa_vol_fluct REAL, -- Volume fluctuant DPA
    dpa_nb_echange SMALLINT, -- Nombre d'échanges DPA
    dpa1_echange_jour BOOLEAN NOT NULL, -- Échange par jour DPA1
    dpca_vent_vid_nuit BOOLEAN NOT NULL, -- Ventilation vide nuit DPCA
    nb_km_dom REAL, -- Nombre de kilomètres domicile
    dept_naiss SMALLINT, -- Département de naissance
    commentaire_general TEXT NOT NULL, -- Commentaire général
    centre_rattachement VARCHAR(80) NOT NULL, -- Centre de rattachement
    Volume_infusion REAL, -- Volume d'infusion
    tx_infusion VARCHAR(4) NOT NULL, -- Taux d'infusion
    uf_ptm BOOLEAN NOT NULL, -- UF PTM
    bilan_complement VARCHAR(80) NOT NULL, -- Bilan complémentaire
    Feuill_specif VARCHAR(80) NOT NULL, -- Feuille spécifique
    Uniponction BOOLEAN NOT NULL, -- Uniponction
    ville_naissance VARCHAR(80) NOT NULL, -- Ville de naissance
    pays_naissance VARCHAR(80) NOT NULL, -- Pays de naissance
    Date_creation TIMESTAMP NOT NULL, -- Date de création
    code_absence VARCHAR(3) NOT NULL, -- Code d'absence
    cp_naissance VARCHAR(10) NOT NULL, -- Code postal de naissance
    dern_utilisateur VARCHAR(80) NOT NULL, -- Dernier utilisateur
    Langue_patient VARCHAR(80) NOT NULL, -- Langue du patient
    generateur VARCHAR(80) NOT NULL, -- Générateur
    Mutuelle VARCHAR(80) NOT NULL, -- Mutuelle
    taux_pec SMALLINT NOT NULL, -- Taux de prise en charge
    vol_distribution REAL, -- Volume de distribution
    vol_restitution REAL, -- Volume de restitution
    KT_cible REAL, -- KT cible
    volume_eau_totale REAL, -- Volume d'eau totale
    date_absence TIMESTAMP NOT NULL, -- Date d'absence
    def_1 VARCHAR(20) NOT NULL, -- Défini 1
    def_2 VARCHAR(20) NOT NULL, -- Défini 2
    def_3 VARCHAR(20) NOT NULL, -- Défini 3
    ICT REAL, -- ICT
    CLSC VARCHAR(80) NOT NULL, -- CLSC
    Pharmacie VARCHAR(80) NOT NULL, -- Pharmacie
    Autre VARCHAR(80) NOT NULL, -- Autre
    Date_ict TIMESTAMP NOT NULL, -- Date ICT
    dp_cycleur VARCHAR(80) NOT NULL, -- Cycleur DP
    dp_regime VARCHAR(80) NOT NULL, -- Régime DP
    dpa_vol_cycle REAL, -- Volume de cycle DPA
    dpa1_drainage BOOLEAN NOT NULL, -- Drainage DPA1
    dp_globale_date TIMESTAMP NOT NULL, -- Date globale DP
    dp_reab_date TIMESTAMP NOT NULL, -- Date de réabsorption DP
    dp_PET_date TIMESTAMP NOT NULL, -- Date PET DP
    dpa1_vol_infusion REAL, -- Volume d'infusion DPA1
    dp_globale_val REAL, -- Valeur globale DP
    dp_reab_val REAL, -- Valeur de réabsorption DP
    dp_PET_val VARCHAR(80) NOT NULL, -- Valeur PET DP
    dpa1_infusion BOOLEAN NOT NULL, -- Infusion DPA1
    dpa_freq_jour SMALLINT NOT NULL, -- Fréquence par jour DPA
    dpa_type_jour VARCHAR(20) NOT NULL, -- Type de jour DPA
    dpa_type_drain VARCHAR(80) NOT NULL, -- Type de drainage DPA
    dpa_uf_moy_jour REAL, -- UF moyen par jour DPA
    dpca_systeme VARCHAR(80) NOT NULL, -- Système DPCA
    dpca_uf_moy_jour REAL, -- UF moyen par jour DPCA
    dpca_jour_par_semaine SMALLINT, -- Jours par semaine DPCA
    dp_anesthesie VARCHAR(80) NOT NULL, -- Anesthésie DP
    dp_chir VARCHAR(80) NOT NULL, -- Chirurgie DP
    dp_date_retrait_catheter TIMESTAMP NOT NULL, -- Date de retrait du cathéter DP
    dp_raison_retrait VARCHAR(80) NOT NULL, -- Raison du retrait DP
    dp_frottis_nasal VARCHAR(80) NOT NULL, -- Frottis nasal DP
    dp_date_frotti_nasal TIMESTAMP NOT NULL, -- Date du frottis nasal DP
    Nesp_freq SMALLINT, -- Fréquence NESP
    dp_diabete_ins_dep BOOLEAN NOT NULL, -- Diabète insulino-dépendant DP
    dp_diabete_inject VARCHAR(80) NOT NULL, -- Injection de diabète DP
    Metier VARCHAR(80) NOT NULL, -- Métier
    date_pose_fistule TIMESTAMP NOT NULL, -- Date de pose de la fistule
    date_prems_util_fistule TIMESTAMP NOT NULL, -- Date de première utilisation de la fistule
    chir_implante_fistule VARCHAR(80) NOT NULL, -- Chirurgien implantant la fistule
    mode_anesthesie_fistule VARCHAR(80) NOT NULL, -- Mode d'anesthésie pour la fistule
    GR_SANGUIN_2 VARCHAR(2) NOT NULL, -- Groupe sanguin 2
    RHESUS_2 VARCHAR(15) NOT NULL, -- Rhésus 2
    dpa_type_solution VARCHAR(20) NOT NULL, -- Type de solution DPA
    dpa_vol_total REAL, -- Volume total DPA
     dpa_tps_total TIME NOT NULL, -- Temps total DPA
    dpa3_vol_fluctuant BOOLEAN NOT NULL, -- Volume fluctuant DPA3
    dpa3_vol_infusion REAL, -- Volume d'infusion DPA3
    dpa1_type_solution VARCHAR(20) NOT NULL, -- Type de solution DPA1
    dpa2_drainage BOOLEAN NOT NULL, -- Drainage DPA2
    dpa3_tps_stase SMALLINT, -- Temps de stase DPA3
    dpa3_uf_cycle SMALLINT, -- UF par cycle DPA3
    dpa2_infusion BOOLEAN NOT NULL, -- Infusion DPA2
    catheter_abdo_sans_prep BOOLEAN NOT NULL, -- Cathéter abdominal sans préparation
    dp_APEX_date TIMESTAMP NOT NULL, -- Date APEX DP
    dp_APEX_val VARCHAR(80) NOT NULL, -- Valeur APEX DP
    dp_PIP_date TIMESTAMP NOT NULL, -- Date PIP DP
    dp_PIP_val REAL, -- Valeur PIP DP
    dp_peritonite_germe VARCHAR(20) NOT NULL, -- Germe de péritonite DP
    dp_peritonite_nb_element SMALLINT, -- Nombre d'éléments de péritonite DP
    dp_peritonite_traitement TEXT NOT NULL, -- Traitement de la péritonite DP
    PCR VARCHAR(20) NOT NULL, -- PCR
    dp_prelev_liquide VARCHAR(80) NOT NULL, -- Prélèvement de liquide DP
    dp_prelev_orifice VARCHAR(80) NOT NULL, -- Prélèvement d'orifice DP
    dp_prelev_nez VARCHAR(80) NOT NULL, -- Prélèvement de nez DP
    dp_trait_orifice VARCHAR(20) NOT NULL, -- Traitement d'orifice DP
    dp_trait_nez VARCHAR(20) NOT NULL, -- Traitement de nez DP
    debit_dialysat REAL, -- Débit de dialysat
    comment_EPO TEXT NOT NULL, -- Commentaire EPO
    dpa_agent_osmotique VARCHAR(80) NOT NULL, -- Agent osmotique DPA
    archive_peritonite TEXT NOT NULL, -- Archive de péritonite
    archive_catheter TEXT NOT NULL, -- Archive du cathéter
    patient_autonome BOOLEAN NOT NULL, -- Patient autonome
    NumNephro INT NOT NULL, -- Numéro du néphrologue
    NumTraitant INT NOT NULL, -- Numéro du médecin traitant
    NumMED2 INT NOT NULL, -- Numéro du médecin 2
    NumMED3 INT NOT NULL, -- Numéro du médecin 3
    NumMED4 INT NOT NULL, -- Numéro du médecin 4
    NumMED5 INT NOT NULL, -- Numéro du médecin 5
    NumDP_Chir INT NOT NULL, -- Numéro du chirurgien DP
    NumChir_implante_fistule INT NOT NULL, -- Numéro du chirurgien implantant la fistule
    archive_insuline TEXT NOT NULL, -- Archive insuline
    pre_dialyse BOOLEAN NOT NULL, -- Pré-dialyse
    hemodialyse BOOLEAN NOT NULL, -- Hémodialyse
    Decede BOOLEAN NOT NULL, -- Décédé
    Greffe BOOLEAN NOT NULL, -- Greffe
    pers_confiance TEXT NOT NULL, -- Personne de confiance
    modele_generateur VARCHAR(80) NOT NULL, -- Modèle de générateur
    poste_lundi REAL, -- Poste lundi
    poste_mardi REAL, -- Poste mardi
    poste_mercredi REAL, -- Poste mercredi
    poste_jeudi REAL, -- Poste jeudi
    poste_vendredi REAL, -- Poste vendredi
    poste_samedi REAL, -- Poste samedi
    poste_dimanche REAL, -- Poste dimanche
    Flag_mirror BOOLEAN NOT NULL, -- Indicateur de miroir
    date_dern_modif TIMESTAMP NOT NULL, -- Date de dernière modification
    heure_dern_modif TIME NOT NULL, -- Heure de dernière modification
    patient_en_dpca BOOLEAN NOT NULL, -- Patient en DPCA
    dpa3_pct_fluctuante REAL, -- Pourcentage fluctuant DPA3
    dpa_jour_par_semaine SMALLINT, -- Jours par semaine DPA
    dp_peritonite_date TIMESTAMP NOT NULL, -- Date de péritonite DP
    dp_peritonite_cause VARCHAR(80) NOT NULL, -- Cause de péritonite DP
    dp_PNN    dp_PNN REAL, -- Polynucléaires neutrophiles DP
    dp_eosino REAL, -- Éosinophiles DP
    dp_complication_date TIMESTAMP NOT NULL, -- Date de complication DP
    dp_complication_type VARCHAR(80) NOT NULL, -- Type de complication DP
    dp_complication_traitement TEXT NOT NULL, -- Traitement de la complication DP
    archive_complication TEXT NOT NULL, -- Archive de complication
    enregistrement_RDPLF BOOLEAN NOT NULL, -- Enregistrement RDPLF
    DPA_Prescription TEXT NOT NULL, -- Prescription DPA
    archive_prescription_dpa TEXT NOT NULL, -- Archive de prescription DPA
    archive_prescription_dpca TEXT NOT NULL, -- Archive de prescription DPCA
    patient_en_nephrologie BOOLEAN NOT NULL, -- Patient en néphrologie
    en_attente_de_greffe BOOLEAN NOT NULL, -- En attente de greffe
    conductivite REAL, -- Conductivité
    conductivite_depuis TIMESTAMP NOT NULL, -- Conductivité depuis
    date_presence TIMESTAMP NOT NULL, -- Date de présence
    date_fin_presence TIMESTAMP NOT NULL, -- Date de fin de présence
    Mode_Transport_depart VARCHAR(80) NOT NULL, -- Mode de transport au départ
    date_fin_absence TIMESTAMP NOT NULL, -- Date de fin d'absence
    commentaire_presence TEXT NOT NULL, -- Commentaire de présence
    commentaire_absence TEXT NOT NULL, -- Commentaire d'absence
    code_presence VARCHAR(3) NOT NULL, -- Code de présence
    motif_presence VARCHAR(80) NOT NULL, -- Motif de présence
    centre_origine VARCHAR(80) NOT NULL, -- Centre d'origine
    centre_destination VARCHAR(80) NOT NULL, -- Centre de destination
    num_sejour VARCHAR(20) NOT NULL, -- Numéro de séjour
    pansements TEXT NOT NULL, -- Pansements
    dpa_dpco_vol_infusion REAL, -- Volume d'infusion DPCO DPA
    dpa_dpco_type_solution VARCHAR(20) NOT NULL, -- Type de solution DPCO DPA
    dpa_tps_stase SMALLINT, -- Temps de stase DPA
    dpa3_freq_drainage_total SMALLINT, -- Fréquence totale de drainage DPA3
    dpa2_concentre_different BOOLEAN NOT NULL, -- Concentré différent DPA2
    dp_volume_residuel SMALLINT, -- Volume résiduel DP
    date_validite_ss TIMESTAMP NOT NULL, -- Date de validité de la sécurité sociale
    comment_transport TEXT NOT NULL, -- Commentaire sur le transport
    present BOOLEAN NOT NULL, -- Présent
    heure_fin_dialyse TIME NOT NULL, -- Heure de fin de dialyse
    perimetre_obilical SMALLINT, -- Périmètre ombilical
    age_prise_en_charge SMALLINT, -- Âge à la prise en charge
    age_deces SMALLINT, -- Âge au décès
    age_premiere_dialyse SMALLINT, -- Âge à la première dialyse
    bilan_pre_transplantation BOOLEAN NOT NULL, -- Bilan pré-transplantation
    bilan_pre_transpl_depuis TIMESTAMP NOT NULL, -- Bilan pré-transplantation depuis
    protocoles TEXT NOT NULL, -- Protocoles
    Create_Reference REAL, -- Référence de création
    Med_referent_declare BOOLEAN NOT NULL, -- Médecin référent déclaré
    BMI REAL, -- Indice de masse corporelle
    Sem_Amenorrhee SMALLINT, -- Semaines d'aménorrhée
    allaitement BOOLEAN NOT NULL, -- Allaitement
    ChaineName_alergie_vidal TEXT NOT NULL, -- Nom de chaîne d'allergie Vidal
    ChaineId_Allergie_Vidal TEXT NOT NULL, -- ID de chaîne d'allergie Vidal
    DDR TIMESTAMP NOT NULL, -- Date des dernières règles
    Mirror_BloqUpload BOOLEAN NOT NULL, -- Blocage de l'upload miroir
    INSC VARCHAR(80) NOT NULL, -- INSC
    Dmp_DatePremierEnvoi TIMESTAMP NOT NULL, -- Date du premier envoi DMP
    Emails_patient TEXT NOT NULL, -- Emails du patient
    aucune_allergie_connue BOOLEAN NOT NULL, -- Aucune allergie connue
    num_statut_identite_patient SMALLINT NOT NULL, -- Statut de l'identité du patient
    num_statut_saisie_dossier SMALLINT NOT NULL, -- Statut de saisie du dossier
    num_statut_consentement SMALLINT NOT NULL, -- Statut du consentement
    num_statut_protec_jur SMALLINT NOT NULL, -- Statut de protection juridique
    num_statut_situ_famille SMALLINT NOT NULL, -- Statut de la situation familiale
    nb_enfants SMALLINT, -- Nombre d'enfants
    num_statut_anonymat SMALLINT NOT NULL, -- Statut de l'anonymat
    nom_reel_patient VARCHAR(100) NOT NULL, -- Nom réel du patient
    num_statut_diabetique SMALLINT NOT NULL, -- Statut de diabétique
    prenom_reel_patient VARCHAR(100) NOT NULL, -- Prénom réel du patient
    num_statut_anurique SMALLINT NOT NULL, -- Statut d'anurique
    infos_particulieres TEXT NOT NULL, -- Informations particulières
    user_creation VARCHAR(80) NOT NULL, -- Utilisateur créateur
    id_caisse VARCHAR(20) NOT NULL, -- ID de la caisse
    id_mutuelle VARCHAR(20) NOT NULL, -- ID de la mutuelle
    ambulancier_depart VARCHAR(80) NOT NULL, -- Ambulancier au départ
    tel_ambu_depart VARCHAR(30) NOT NULL, -- Téléphone de l'ambulancier au départ
    comment_transport_depart TEXT NOT NULL, -- Commentaire sur le transport au départ
    num_ambu INT NOT NULL, -- Numéro de l'ambulancier
    num_ambu_depart INT NOT NULL, -- Numéro de l'ambulancier au départ
    num_score_charlson REAL NOT NULL, -- Score de Charlson
    perimetre_cranien SMALLINT, -- Périmètre crânien
    perimetre_brachial SMALLINT, -- Périmètre brachial
    duree_trajet_dom TIME NOT NULL, -- Durée du trajet domicile
    date_def_3 TIMESTAMP NOT NULL, -- Date définie 3
    heure_def_3 TIME NOT NULL, -- Heure définie 3
    BMI2 REAL, -- Indice de masse corporelle 2
    num_statut_asepsie_transport SMALLINT NOT NULL, -- Statut d'asepsie du transport
    duree_dialyse_lu TIME NOT NULL, -- Durée de dialyse le lundi
    duree_dialyse_ma TIME NOT NULL, -- Durée de dialyse le mardi
    duree_dialyse_me TIME NOT NULL, -- Durée de dialyse le mercredi
    duree_dialyse_je TIME NOT NULL, -- Durée de dialyse le jeudi
    duree_dialyse_ve TIME NOT NULL, -- Durée de dialyse le vendredi
    duree_dialyse_sa TIME NOT NULL, -- Durée de dialyse le samedi
    duree_dialyse_di TIME NOT NULL, -- Durée de dialyse le dimanche
    afficher_nom_naissance BOOLEAN NOT NULL, -- Afficher le nom de naissance
    Tanner SMALLINT, -- Stade de Tanner
    pilosite VARCHAR(2) NOT NULL, -- Pilosité
    sein VARCHAR(2) NOT NULL, -- Sein
    gonades VARCHAR(2) NOT NULL, -- Gonades
    num_statut_eval_greffe SMALLINT NOT NULL, -- Statut d'évaluation de greffe
    date_eval_greffe TIMESTAMP NOT NULL, -- Date d'évaluation de greffe
    comment_eval_greffe TEXT NOT NULL, -- Commentaire sur l'évaluation de greffe
    date_dern_modif_presc TIMESTAMP NOT NULL, -- Date de dernière modification de prescription
    hbc_ac VARCHAR(20) NOT NULL, -- Hbc anticorps
    num_statut_pec SMALLINT NOT NULL, -- Statut de prise en charge
    Val_Apport_en_dialyse REAL, -- Valeur d'apport en dialyse
    localisation_prem_dial_centre VARCHAR(80) NOT NULL, -- Localisation de la première dialyse au centre
    dat_nais_lunaire VARCHAR(20) NOT NULL, -- Date de naissance lunaire
    PCR_HBV VARCHAR(20) NOT NULL, -- PCR HBV
    heure_dern_modif_presc TIME NOT NULL, -- Heure de dernière modification de prescription
    id_transfert TEXT NOT NULL, -- Identifiant de transfert
    DFG VARCHAR(20) NOT NULL, -- Débit de filtration glomérulaire
    stadeIRC VARCHAR(20) NOT NULL, -- Stade de l'insuffisance rénale chronique
    date_DFG TIMESTAMP NOT NULL, -- Date du DFG
    vecteur_caracteristique_visage TEXT NOT NULL, -- Vecteur de caractéristiques du visage
    debit_sang REAL, -- Débit sanguin
    inclus_forfait SMALLINT NOT NULL, -- Inclus dans le forfait  FPCMRC
    num_type_TLSV SMALLINT NOT NULL, -- Numéro de type télésurveillance
    infos_donnees_archivees TEXT NOT NULL, -- Informations sur les données archivées
    O_zone_CR_dossmed BYTEA NOT NULL, -- Zone CR dossier médical
    INS_insi VARCHAR(15) NOT NULL, -- INS INSI
    OID_insi VARCHAR(20) NOT NULL, -- OID INSI
    INSCle_insi VARCHAR(2) NOT NULL, -- Clé INSI
    INSEE_naissance VARCHAR(5) NOT NULL, -- INSEE de naissance
    prenom1_naissance_insi VARCHAR(100) NOT NULL, -- Premier prénom de naissance INSI
    listePrenoms_insi TEXT NOT NULL, -- Liste des prénoms INSI
    num_pct_var_pds_sec_6_mois REAL, -- Variation du poids sec sur 6 mois
    statutID_ins VARCHAR(80) NOT NULL, -- Statut ID INS
    nature_justif_id VARCHAR(80) NOT NULL, -- Nature du justificatif d'identité
    attributs_id VARCHAR(80) NOT NULL, -- Attributs d'identité
    image_pass_sanitaire BYTEA NOT NULL, -- Image du pass sanitaire
    vol_infusion_HDF_predil REAL, -- Volume d'infusion HDF prédilution
    vol_infusion_HDF_postdil REAL, -- Volume d'infusion HDF postdilution
    tx_infusion_HDF_predil REAL, -- Taux d'infusion HDF prédilution
    tx_infusion_HDF_postdil REAL, -- Taux d'infusion HDF postdilution
    num_option_recheck INT NOT NULL, -- Numéro d'option de recheck
    date_fin_pec_fpcmrc TIMESTAMP NOT NULL, -- Date de fin de prise en charge FPCMRC 
    MIR_DataOwner VARCHAR(80) NOT NULL, -- Propriétaire des données MIR
    MIR_Timestamp VARCHAR(80) NOT NULL, -- Horodatage MIR
    MIR_ETAT VARCHAR(80) NOT NULL, -- État MIR
    debit_amorcage REAL, -- Débit d'amorçage
    debit_restitution REAL, -- Débit de restitution
    branchement_rein_plein VARCHAR(10), -- Branchement rein plein
    refus_patient_RGPD BOOLEAN NOT NULL, -- Refus du patient RGPD
    codes_nephropathie_init TEXT NOT NULL, -- Codes de néphropathie initiale
    num_statut_score_denutrition SMALLINT, -- Statut du score de dénutrition
    date_statut_score_denutrition TIMESTAMP, -- Date du statut du score de dénutrition
    patient_a_domicile BOOLEAN, -- Patient à domicile
    HTLV1 VARCHAR(255), -- HTLV1
    HTLV2 VARCHAR(255), -- HTLV2
    num_statut_dir_ant INT, -- Statut des directives anticipées
    PRIMARY KEY (NUMPAT) -- Clé primaire sur le champ NUMPAT
);

-- TABLE ANOMALIE INCIDENTS
CREATE TABLE ANOMALIE (
    NUMPAT INT NOT NULL, -- Numéro unique du patient
    NOMPAT VARCHAR(80) NOT NULL, -- Nom du patient
    PRENOM VARCHAR(80) NOT NULL, -- Prénom du patient
    cDATE TIMESTAMP NOT NULL, -- Date de l'anomalie
    cTYPE VARCHAR(255) NOT NULL, -- Type d'anomalie
    COMMENTAIRE TEXT NOT NULL, -- Commentaire sur l'anomalie
    NUM_ANO INT UNIQUE NOT NULL, -- Numéro unique de l'anomalie
    famille VARCHAR(80) NOT NULL, -- Famille de l'anomalie
    f_dosmed1 VARCHAR(80) NOT NULL, -- Dosage médicamenteux 1
    f_dosmed2 VARCHAR(80) NOT NULL, -- Dosage médicamenteux 2
    f_dosmed3 VARCHAR(80) NOT NULL, -- Dosage médicamenteux 3
    LOCALISATION VARCHAR(80) NOT NULL, -- Localisation de l'anomalie
    num_seance INT NOT NULL, -- Numéro de la séance
    periode VARCHAR(80) NOT NULL, -- Période de l'anomalie
    num_periode INT NOT NULL, -- Numéro de la période
    cTimestamp TIME NOT NULL, -- Timestamp de l'anomalie
    heure_alarme VARCHAR(20) NOT NULL, -- Heure de l'alarme
    Nephrologue VARCHAR(80) NOT NULL, -- Néphrologue associé
    id_abord INT NOT NULL, -- Identifiant de l'abord
    heure_ano TIME NOT NULL, -- Heure de l'anomalie
    num_table INT NOT NULL, -- Numéro de la table
    num_record INT NOT NULL, -- Numéro de l'enregistrement
    PRIMARY KEY (NUM_ANO) -- Clé primaire sur le champ NUM_ANO
);

--TABLE INFECTION
CREATE TABLE INFECTIONS (
    numpat INT NOT NULL, -- Numéro unique du patient
    NOMPAT VARCHAR(80) NOT NULL, -- Nom du patient
    PRENOM VARCHAR(80) NOT NULL, -- Prénom du patient
    cDATE TIMESTAMP NOT NULL, -- Date de l'infection
    Laboratoire VARCHAR(80) NOT NULL, -- Laboratoire
    methode VARCHAR(80) NOT NULL, -- Méthode
    date_debut_traitement TIMESTAMP NOT NULL, -- Date de début du traitement
    duree_jour SMALLINT NOT NULL, -- Durée en jours
    traitement TEXT NOT NULL, -- Traitement
    commentaire TEXT NOT NULL, -- Commentaire
    type_infection VARCHAR(80) NOT NULL, -- Type d'infection
    medecin VARCHAR(80) NOT NULL, -- Médecin
    f_dossmed1 VARCHAR(80) NOT NULL, -- Dossier médical 1
    f_dossmed2 VARCHAR(80) NOT NULL, -- Dossier médical 2
    f_dossmed3 VARCHAR(80) NOT NULL, -- Dossier médical 3
    localisation VARCHAR(80) NOT NULL, -- Localisation
    type_traitement VARCHAR(80) NOT NULL, -- Type de traitement
    NumMED INT NOT NULL, -- Numéro du médecin
    dern_utilisateur VARCHAR(80) NOT NULL, -- Dernier utilisateur
    date_dern_modif TIMESTAMP NOT NULL, -- Date de dernière modification
    heure_dern_modif TIME NOT NULL, -- Heure de dernière modification
    centre_dialyse VARCHAR(80) NOT NULL, -- Centre de dialyse
    lieu_infection VARCHAR(80) NOT NULL, -- Lieu de l'infection
    num_enreg_infection INT UNIQUE NOT NULL, -- Numéro d'enregistrement de l'infection
    Date_fin_trait TIMESTAMP NOT NULL, -- Date de fin du traitement
    SARM BOOLEAN NOT NULL, -- Indicateur SARM
    BMR BOOLEAN NOT NULL, -- Indicateur BMR
    ERV BOOLEAN NOT NULL, -- Indicateur ERV
    piece_jointe_01 TEXT NOT NULL, -- Pièce jointe 01
    piece_jointe_02 TEXT NOT NULL, -- Pièce jointe 02
    piece_jointe_03 TEXT NOT NULL, -- Pièce jointe 03
    num_trait INT NOT NULL, -- Numéro du traitement
    isolement SMALLINT NOT NULL, -- Isolement
    signalement_externe SMALLINT NOT NULL, -- Signalement externe
    infection_importee SMALLINT NOT NULL, -- Infection importée
    date_fin_infection TIMESTAMP NOT NULL, -- Date de fin de l'infection
    precaution_compl_prescrite SMALLINT NOT NULL, -- Précaution complémentaire prescrite
    verif_precaution_compl SMALLINT NOT NULL, -- Vérification de la précaution complémentaire
    date_verif_precaution_compl TIMESTAMP NOT NULL, -- Date de vérification de la précaution complémentaire
    reevaluation_antibiotique SMALLINT NOT NULL, -- Réévaluation de l'antibiotique
    reevaluation_traitement SMALLINT NOT NULL, -- Réévaluation du traitement
    conforme_protocole_etabt SMALLINT NOT NULL, -- Conforme au protocole de l'établissement
    info_patient SMALLINT NOT NULL, -- Information au patient
    info_service SMALLINT NOT NULL, -- Information au service
    info_ambulanciers SMALLINT NOT NULL, -- Information aux ambulanciers
    num_pdt INT NOT NULL, -- Numéro PDT
    MIR_ETAT VARCHAR(80) NOT NULL, -- État MIR
    MIR_DataOwner VARCHAR(80) NOT NULL, -- Propriétaire des données MIR
    MIR_TimeStamp VARCHAR(40) NOT NULL, -- Horodatage MIR
    type_verif_precaution_compl VARCHAR(80) NOT NULL, -- Type de vérification de la précaution complémentaire
    num_trait_02 INT NOT NULL, -- Numéro du traitement 02
    num_trait_03 INT NOT NULL, -- Numéro du traitement 03
    delai_reevaluation_01 SMALLINT NOT NULL, -- Délai de réévaluation 01
    delai_reevaluation_02 SMALLINT NOT NULL, -- Délai de réévaluation 02
     delai_reevaluation_03 SMALLINT NOT NULL, -- Délai de réévaluation 03
    comment_reevaluation_01 TEXT NOT NULL, -- Commentaire de réévaluation 01
    comment_reevaluation_02 TEXT NOT NULL, -- Commentaire de réévaluation 02
    comment_reevaluation_03 TEXT NOT NULL, -- Commentaire de réévaluation 03
    user_reevaluation_01 VARCHAR(80) NOT NULL, -- Utilisateur de réévaluation 01
    user_reevaluation_02 VARCHAR(80) NOT NULL, -- Utilisateur de réévaluation 02
    user_reevaluation_03 VARCHAR(80) NOT NULL, -- Utilisateur de réévaluation 03
    date_reevaluation_01 TIMESTAMP NOT NULL, -- Date de réévaluation 01
    date_reevaluation_02 TIMESTAMP NOT NULL, -- Date de réévaluation 02
    date_reevaluation_03 TIMESTAMP NOT NULL, -- Date de réévaluation 03
    heure_reevaluation_01 TIME NOT NULL, -- Heure de réévaluation 01
    heure_reevaluation_02 TIME NOT NULL, -- Heure de réévaluation 02
    heure_reevaluation_03 TIME NOT NULL, -- Heure de réévaluation 03
    duree_infection_jour SMALLINT NOT NULL, -- Durée de l'infection en jours
    site_prelevement VARCHAR(80) NOT NULL, -- Site de prélèvement
    date_prelevement TIMESTAMP NOT NULL, -- Date de prélèvement
    heure_prelevement TIME NOT NULL, -- Heure de prélèvement
    verrou BOOLEAN NOT NULL, -- Verrou
    id_abord INT NOT NULL, -- Identifiant de l'abord
    BHRe BOOLEAN NOT NULL, -- Indicateur BHRe
    type_bacterie VARCHAR(80) NOT NULL, -- Type de bactérie
    COVID BOOLEAN NOT NULL, -- Indicateur COVID
    type_COVID VARCHAR(80) NOT NULL, -- Type de COVID
    peritonite BOOLEAN NOT NULL, -- Indicateur de péritonite
    peritonite_info TEXT NOT NULL, -- Informations sur la péritonite
    peritonite_nb SMALLINT NOT NULL, -- Nombre de péritonites
    PRIMARY KEY (num_enreg_infection) -- Clé primaire sur le champ num_enreg_infection
);