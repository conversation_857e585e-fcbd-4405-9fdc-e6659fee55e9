from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any
import os
import pyodbc
from dotenv import load_dotenv
import logging
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('api.log')
    ]
)
logger = logging.getLogger(__name__)

class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        start_time = time.time()
        try:
            response = await call_next(request)
            process_time = time.time() - start_time
            logger.info(
                f"Path: {request.url.path} | "
                f"Method: {request.method} | "
                f"Status: {response.status_code} | "
                f"Duration: {process_time:.2f}s"
            )
            return response
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                f"Path: {request.url.path} | "
                f"Method: {request.method} | "
                f"Error: {str(e)} | "
                f"Duration: {process_time:.2f}s"
            )
            raise

# Load environment variables
load_dotenv()

# Get individual database connection parameters
DB_DRIVER = os.getenv("DB_DRIVER")
DB_SERVER = os.getenv("DB_SERVER")
DB_NAME = os.getenv("DB_NAME")
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")

required_vars = ["DB_DRIVER", "DB_SERVER", "DB_NAME", "DB_USER", "DB_PASSWORD"]
missing_vars = [var for var in required_vars if not os.getenv(var)]
if missing_vars:
    raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

def get_pyodbc_connection():
    """
    Create a direct pyodbc connection using individual parameters
    """
    try:
        conn_str = (
            f"DRIVER={{{DB_DRIVER}}};"
            f"SERVER={DB_SERVER};"
            f"DATABASE={DB_NAME};"
            f"UID={DB_USER};"
            f"PWD={DB_PASSWORD};"
        )
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        logger.error(f"Error creating pyodbc connection: {e}")
        raise

app = FastAPI(
    title="Database API Service", 
    description="API service for database operations that can be called from external tools"
)

# Add middleware
app.add_middleware(LoggingMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    logger.error(f"HTTP error occurred: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "HTTP Exception",
            "detail": exc.detail,
            "path": request.url.path
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unexpected error occurred: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "detail": str(exc),
            "path": request.url.path
        }
    )

# Models
class QueryRequest(BaseModel):
    query: str

class SchemaRequest(BaseModel):
    table_name: str

@app.get("/")
async def root():
    return {"message": "Database API Service is running"}

@app.get("/context")
async def get_context():
    """
    Read and return the SQL context rules from context.toml file
    """
    try:
        with open("db/context.toml", "r", encoding='utf-8') as f:
            content = f.read()
        return {"content": content}
    except Exception as e:
        logger.error(f"Error reading context from context.toml: {e}")
        raise HTTPException(status_code=500, detail=f"Error reading context from context.toml: {str(e)}")

@app.get("/tables", response_model=List[str])
async def list_tables():
    """
    List all tables from tables.toml file
    """
    try:
        with open("db/tables.toml", "r") as f:
            content = f.read()
        
        # Extract table names using regex pattern that matches "CREATE TABLE" statements
        import re
        table_pattern = r"CREATE TABLE (\w+)"
        tables = re.findall(table_pattern, content)
        
        return tables
    except Exception as e:
        logger.error(f"Error reading tables from tables.toml: {e}")
        raise HTTPException(status_code=500, detail=f"Error reading tables from tables.toml: {str(e)}")

@app.post("/schema", response_model=str)
async def get_schema(request: SchemaRequest):
    """
    Get the schema of a specific table from tables.toml file
    """
    try:
        table_name = request.table_name
        with open("db/tables.toml", "r") as f:
            content = f.read()
        
        # Find the CREATE TABLE statement for the requested table
        import re
        pattern = f"CREATE TABLE {table_name} \\([^;]+\\);"
        match = re.search(pattern, content, re.DOTALL)
        
        if not match:
            raise HTTPException(status_code=404, detail=f"Table '{table_name}' not found")
            
        return match.group(0)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting schema for table {request.table_name}: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting schema for table {request.table_name}: {str(e)}")

@app.post("/query")
async def run_query(request: QueryRequest):
    """
    Execute a SQL query on the database (adapté pour 4D)
    """
    conn = None
    try:
        query = request.query.strip()
        logger.info(f"Executing query: {query}")
        
        # Basic security check - prevent destructive operations
        forbidden_keywords = ["DROP", "DELETE", "UPDATE", "INSERT", "ALTER", "CREATE"]
        found_keywords = [kw for kw in forbidden_keywords if kw in query.upper()]
        if found_keywords:
            msg = f"DML and DDL statements are not allowed. Found: {', '.join(found_keywords)}"
            logger.warning(f"Query rejected - {msg}")
            raise HTTPException(status_code=403, detail=msg)
        
        conn = get_pyodbc_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(query)
            if query.upper().startswith("SELECT"):
                columns = [column[0] for column in cursor.description]
                rows = [dict(zip(columns, row)) for row in cursor.fetchall()]
                logger.info(f"Query executed successfully. Returned {len(rows)} rows")
                return {
                    "success": True,
                    "rows": rows,
                    "rowCount": len(rows),
                    "columns": columns
                }
            else:
                rowcount = cursor.rowcount
                logger.info(f"Query executed successfully. Affected {rowcount} rows")
                return {
                    "success": True,
                    "rowcount": rowcount
                }
        except Exception as e:
            logger.error(f"Query execution failed: {str(e)}\nQuery: {query}")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Query execution failed",
                    "message": str(e),
                    "query": query,
                    "errorType": type(e).__name__
                }
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error executing query: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Unexpected error",
                "message": str(e),
                "errorType": type(e).__name__
            }
        )
    finally:
        if conn:
            conn.close()

@app.post("/query_checker")
async def check_query(request: QueryRequest):
    """
    Check a SQL query for common mistakes
    """
    query = request.query
    # Define common mistakes to check for
    common_mistakes = [
        # SQL Syntax Rules
        {"pattern": "JOIN", "context": "", "message": "Use explicit joins with WHERE clause instead of JOIN keyword (e.g., 'Table1, Table2 WHERE Table1.colonne = Table2.colonne')"},
        {"pattern": "NUMPAT", "context": "PATIENT", "message": "Joins with PATIENT table must use NUMPAT field"},
        {"pattern": "SELECT", "context": "NOM", "message": "Always include PRENOM when selecting NOM"},
        {"pattern": "LIKE", "context": "TYPE_EXAM", "message": "Use LIKE for TYPE_EXAM comparisons and remove accents"},
        {"pattern": "COUNT", "context": "", "message": "Use COUNT(*) for counting records"},
        {"pattern": "INNER JOIN", "context": "", "message": "Use explicit joins with WHERE clause instead of INNER JOIN"},
        {"pattern": "LEFT JOIN", "context": "", "message": "Use explicit joins with WHERE clause instead of LEFT JOIN"},
        {"pattern": "DATEADD", "context": "", "message": "Use direct date comparisons (date1 < date2) instead of DATEADD"},
        {"pattern": "DATE_TRUNC", "context": "", "message": "Use direct date comparisons instead of DATE_TRUNC"},
        {"pattern": "CURRENT_DATE", "context": "", "message": "Use explicit date value in YYYY-MM-DD format instead of CURRENT_DATE"},
        {"pattern": "IS NULL", "context": "cDate", "message": "For dates, use comparison with '00-00-0000' instead of NULL check"},
        
        # Date Functions
        {"pattern": "EXTRACT", "context": "", "message": "Don't use EXTRACT. Use SUBSTRING on date column instead, assuming date is stored as text"},
        {"pattern": "MONTH", "context": "", "message": "Don't use MONTH. Use SUBSTRING on date column instead, assuming date is stored as text"},
        {"pattern": "DATE_PART", "context": "", "message": "Don't use DATE_PART. Use SUBSTRING on date column instead, assuming date is stored as text"},
        {"pattern": "TO_CHAR", "context": "", "message": "Don't use TO_CHAR. Use SUBSTRING on date column instead, assuming date is stored as text"},
        
        # Boolean Fields
        {"pattern": "= TRUE", "context": "", "message": "Use CAST(1 AS BOOLEAN) instead of = TRUE"},
        {"pattern": "= FALSE", "context": "", "message": "Use CAST(0 AS BOOLEAN) instead of = FALSE"},
        
        # Text Comparisons
        {"pattern": "=", "context": "TYPE_EXAM", "message": "Use LIKE instead of = for TYPE_EXAM comparisons"},
        {"pattern": "!=", "context": "", "message": "Use <> for not equals comparison"},
        
        # Table-Specific Rules
        {"pattern": "SEANCE_DIALYSE", "context": "", "message": "Add condition 'fin_dialyse = CAST(1 AS BOOLEAN)' for dialysis sessions"},
        {"pattern": "PATIENT.NOMPAT", "context": "", "message": "Use PATIENT.NOM instead of PATIENT.NOMPAT"},
        {"pattern": "TRAITEMENTSv2", "context": "", "message": "Add condition 'en_cours = CAST(1 AS BOOLEAN)' for current treatments"},
        {"pattern": "PLANNING_DIAL", "context": "preparation", "message": "For material preparation queries, use PLANNING_DIAL table"},
        {"pattern": "EXAMENS_BIOLOGI", "context": "", "message": "For biological exams, use RESULTAT_AV and TYPE_EXAM fields, and include Unite if requested"},
        {"pattern": "ANOMALIE", "context": "incident", "message": "For incident queries, use the ANOMALIE table"},
        {"pattern": "PATIENT", "context": "present", "message": "For present patients, add condition 'Absent = CAST(0 AS BOOLEAN)'"},
        {"pattern": "GROUP BY", "context": "PATIENT", "message": "For patient groups, use LOCALISATION field from PATIENT table"},
        {"pattern": "PLANNING_DIAL", "context": "past", "message": "For past dialysis sessions, add condition 'cDATE < current_date'"},
        
        # Treatment Types
        {"pattern": "EPO", "context": "TRAITEMENTSv2", "message": "For EPO treatments, use condition 'num_famille_medic_type = 1'"},
        {"pattern": "FER", "context": "TRAITEMENTSv2", "message": "For Fer treatments, use condition 'num_famille_medic_type = 2'"},
        {"pattern": "HEPARINE", "context": "TRAITEMENTSv2", "message": "For Héparine treatments, use condition 'num_famille_medic_type = 3'"},
        {"pattern": "SOLUTION VERROU", "context": "TRAITEMENTSv2", "message": "For Solution verrou treatments, use condition 'num_famille_medic_type = 4'"},
        {"pattern": "AVK", "context": "TRAITEMENTSv2", "message": "For AVK treatments, use condition 'num_famille_medic_type = 5'"},
        
        # Status Fields
        {"pattern": "STATUT_DMP", "context": "", "message": "DMP status: 1=open, 2=closed, 3/0=nonexistent"},
        {"pattern": "num_statut_abord", "context": "", "message": "Abord status: 0=usable, 2=supplementary, 1=not yet usable, -1=no longer usable"},
        {"pattern": "num_statut_diabetique", "context": "", "message": "Diabetic status: 0=not specified, 1=YES, -1=NO"},
        {"pattern": "num_statut_anurique", "context": "", "message": "Anuric status: 0=not specified, 1=YES, -1=NO"},
        {"pattern": "Patient_algique_branchement", "context": "", "message": "Connection pain: 0=not specified, 1=YES, 2=NO"},
        {"pattern": "num_statut_anonymat", "context": "", "message": "Anonymity status: 0=not requested, 1=requested"},
        {"pattern": "num_statut_asepsie_transport", "context": "", "message": "Transport asepsis status: 0=not specified, 1=YES, -1=NO"},
        {"pattern": "num_statut_autonome_insuline", "context": "", "message": "Insulin autonomy status: 0=not specified, 1=YES, -1=NO, -99=not applicable"},
        {"pattern": "num_statut_consentement", "context": "", "message": "Consent status: 0=not requested, 2=requested, 1=obtained, -1=refused"},
        {"pattern": "num_statut_dir_ant", "context": "", "message": "Advance directives status: 0=not requested, -1=requested, 1=recorded, -2=unavailable, -9=refused"},
        {"pattern": "num_statut_identite_patient", "context": "", "message": "Patient identity status: 0=unverified, 1=to verify, 2=provisional, 10=verified"},
        {"pattern": "num_statut_pec", "context": "", "message": "Care status: 0=establishment, 1=liberal"},
        {"pattern": "num_statut_protec_jur", "context": "", "message": "Legal protection status: 0=not specified, 1=under guardianship, -1=NO, 11=under curatorship, 21=under legal protection, 31=family authorization"},
        {"pattern": "num_statut_saisie_dossier", "context": "", "message": "File entry status: 0=update in progress, 10=update completed"},
        {"pattern": "num_statut_score_denutrition", "context": "", "message": "Malnutrition score status: -1=not malnourished, 0=no known malnutrition, 1=malnourished, 5=moderately malnourished, 9=severely malnourished"},
        {"pattern": "num_statut_situ_famille", "context": "", "message": "Family situation status: 0=not specified, 1=single, 8=in couple, 9=cohabiting, 11=married, 12=civil partnership, 21=separated, 22=divorced, 29=widowed"},
        {"pattern": "num_type_TLSV", "context": "", "message": "Remote monitoring type: 1=hemodialysis, 4=UDM, 5=self-dialysis, 7=post-transplant"},
        
        # Security
        {"pattern": "DROP", "context": "", "message": "DML statements are not allowed"},
        {"pattern": "DELETE", "context": "", "message": "DML statements are not allowed"},
        {"pattern": "UPDATE", "context": "", "message": "DML statements are not allowed"},
        {"pattern": "INSERT", "context": "", "message": "DML statements are not allowed"},
        {"pattern": "ALTER", "context": "", "message": "DML statements are not allowed"},
        {"pattern": "CREATE", "context": "", "message": "DML statements are not allowed"}
    ]
    mistakes = []
    for mistake in common_mistakes:
        if mistake["pattern"] in query.upper():
            if not mistake["context"] or mistake["context"] in query.upper():
                mistakes.append(mistake["message"])
    return {"issues": mistakes} if mistakes else {"issues": []}

if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", "8086"))
    host = os.getenv("HOST", "0.0.0.0")
    uvicorn.run(app, host=host, port=port)
