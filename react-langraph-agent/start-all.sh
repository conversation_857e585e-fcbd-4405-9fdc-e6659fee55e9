#!/bin/bash

# Determine the Python command to use
PYTHON_CMD="python"
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
fi
 
# Store the root directory
ROOT_DIR=$(pwd)

# Install dependencies from the combined requirements.txt file
echo "Installing Python dependencies..."
$PYTHON_CMD -m pip install -r requirements.txt

# Start the db-api-service
echo "Starting db-api-service..."
cd "$ROOT_DIR/db-api-service" && $PYTHON_CMD main.py &
DB_API_PID=$!

# Start the db-vector-service
echo "Starting db-vector-service..."
cd "$ROOT_DIR/db-vector-service" && $PYTHON_CMD main.py &
DB_VECTOR_PID=$!

# Return to the root directory
cd "$ROOT_DIR"

# Wait for services to initialize
echo "Waiting for services to initialize..."
sleep 3

# Build TypeScript files
echo "Building TypeScript files..."
npm run build
echo "TypeScript build completed"

# Start the main application
echo "Starting main application..."
npx concurrently "npm run frontend:dev" "node dist/server.js"

# When the main application exits, kill the background services
echo "Shutting down services..."
kill $DB_API_PID
kill $DB_VECTOR_PID
