# Flux détaillés de l'EMA Companion AI

Ce document décrit en détail les flux de données entre les différentes couches de l'application EMA Companion AI.

## Vue d'ensemble

L'application est composée de trois couches principales:
1. **Frontend React** (port 3000) - Interface utilisateur
2. **Backend LangGraph** (port 3005) - Agent conversationnel
3. **Service API de base de données** (port 8086) - Interface avec la base de données SQL

## Diagramme des flux détaillés

```mermaid
flowchart TD
    %% Définition des styles
    classDef frontend fill:#d4f1f9,stroke:#05a,stroke-width:1px
    classDef backend fill:#e1d5e7,stroke:#735,stroke-width:1px
    classDef database fill:#d5e8d4,stroke:#376,stroke-width:1px
    classDef model fill:#fff2cc,stroke:#d6b656,stroke-width:1px
    
    %% Frontend (Port 3000)
    subgraph FE["Frontend React (Port 3000)"]
        A[index.html] --> B[main.tsx]
        B --> C[App.tsx]
        C --> D[ChatInterface.tsx]
        D --> E[MessageList.tsx]
        D --> F[MessageInput.tsx]
        D --> G[Sidebar.tsx]
        H[api.ts] --> D
    end
    
    %% Backend (Port 3005)
    subgraph BE["Backend LangGraph (Port 3005)"]
        I[server.ts] --> J[index.ts]
        J --> K[createSQLAgent]
        K --> L[StateGraph]
        
        subgraph LG["LangGraph Workflow"]
            L --> M["callModel Node"]
            L --> N["tools Node"]
            M <--> N
        end
        
        O[tools.ts] --> N
        P[api-database.ts] --> O
    end
    
    %% Database API Service (Port 8086)
    subgraph DB["Database API Service (Port 8086)"]
        Q[main.py] --> R[FastAPI]
        R --> S["/tables"]
        R --> T["/schema"]
        R --> U["/query"]
        R --> V["/query_checker"]
        R --> W["/context"]
        X[pyodbc] --> Y["SQL Database\n(4D/SQL Server)"]
        S & T & U & V & W --> X
    end
    
    %% External Services
    subgraph EX["Services Externes"]
        Z["Anthropic Claude API"]
    end
    
    %% Flux de données
    H -- "POST /api/message" --> I
    I -- "createSQLAgent" --> K
    K -- "Initialisation workflow" --> L
    M -- "Appel au modèle LLM" --> Z
    Z -- "Réponse du modèle" --> M
    M -- "Si tool_calls" --> N
    N -- "Exécution des outils" --> P
    P -- "Appels API" --> R
    X -- "Requêtes SQL" --> Y
    Y -- "Résultats SQL" --> X
    X -- "Résultats formatés" --> R
    R -- "Réponse JSON" --> P
    P -- "Résultats des outils" --> N
    N -- "Retour avec résultats" --> M
    M -- "Réponse finale" --> K
    K -- "Réponse complète" --> I
    I -- "Réponse JSON" --> H
    H -- "Mise à jour interface" --> D
    
    %% Application des styles
    class A,B,C,D,E,F,G,H frontend
    class I,J,K,L,M,N,O,P backend
    class Q,R,S,T,U,V,W,X,Y database
    class Z model
```

## Description détaillée des flux

### 1. Interaction utilisateur → Frontend

1. L'utilisateur saisit un message dans `MessageInput.tsx`
2. Le message est envoyé au service API via `api.ts`
3. L'interface affiche un indicateur de chargement

### 2. Frontend → Backend

4. Le service API envoie une requête POST à `/api/message` avec le message de l'utilisateur
5. `server.ts` reçoit la requête et appelle `createSQLAgent` avec le message
6. `createSQLAgent` initialise le workflow LangGraph avec le message comme état initial

### 3. Backend → LangGraph Workflow

7. Le workflow démarre au nœud `callModel`
8. `callModel` prépare les messages (système + utilisateur) et appelle le modèle LLM (Claude)
9. Le modèle génère une réponse qui peut contenir des appels d'outils (tool_calls)
10. `routeModelOutput` détermine si la réponse contient des appels d'outils
    - Si oui, le flux continue vers le nœud `tools`
    - Si non, le workflow se termine et renvoie la réponse

### 4. LangGraph → Outils SQL

11. Le nœud `tools` reçoit les appels d'outils du modèle
12. Il exécute les outils demandés via `tools.ts`:
    - `listTablesTool` - Liste toutes les tables disponibles
    - `getSchemaTool` - Récupère le schéma d'une table spécifique
    - `runQueryTool` - Exécute une requête SQL
    - `checkQueryTool` - Vérifie une requête SQL pour les erreurs courantes
    - `generateContextQueryTool` - Récupère le contexte métier

### 5. Outils SQL → Service API de base de données

13. Les outils SQL font des appels API au service de base de données via `api-database.ts`
14. Les requêtes sont envoyées aux endpoints correspondants:
    - GET `/tables` - Liste des tables
    - POST `/schema` - Schéma d'une table
    - POST `/query` - Exécution d'une requête
    - POST `/query_checker` - Vérification d'une requête
    - GET `/context` - Contexte métier

### 6. Service API → Base de données

15. Le service API reçoit les requêtes et les traite via FastAPI
16. Il utilise pyodbc pour se connecter à la base de données SQL
17. Les requêtes SQL sont exécutées sur la base de données
18. Les résultats sont formatés en JSON et renvoyés

### 7. Retour des résultats

19. Les résultats des outils sont renvoyés au nœud `tools`
20. Le workflow retourne au nœud `callModel` avec les résultats
21. Le modèle génère une réponse finale basée sur les résultats
22. La réponse finale est renvoyée au frontend

### 8. Affichage de la réponse

23. Le frontend reçoit la réponse et met à jour l'interface utilisateur
24. La réponse est affichée dans `MessageList.tsx`
25. L'indicateur de chargement est masqué

## Cycle de vie complet d'une requête utilisateur

### Phase 1: Initialisation de la requête
1. L'utilisateur saisit une question dans l'interface de chat
2. Le frontend capture la question et l'envoie au backend
3. Le backend initialise l'agent SQL avec la question

### Phase 2: Exploration de la base de données
4. L'agent demande la liste des tables disponibles
5. L'agent analyse la question pour déterminer les tables pertinentes
6. L'agent demande le schéma des tables identifiées comme pertinentes

### Phase 3: Génération et vérification de la requête
7. L'agent génère une requête SQL basée sur la question et les schémas
8. L'agent vérifie la requête pour détecter d'éventuelles erreurs
9. Si des erreurs sont détectées, l'agent reformule la requête

### Phase 4: Exécution de la requête
10. L'agent envoie la requête au service API de base de données
11. Le service API exécute la requête sur la base de données
12. La base de données renvoie les résultats au service API
13. Le service API formate les résultats en JSON et les renvoie à l'agent

### Phase 5: Formulation de la réponse
14. L'agent analyse les résultats de la requête
15. L'agent formule une réponse en langage naturel
16. L'agent inclut les résultats formatés dans sa réponse

### Phase 6: Présentation des résultats
17. La réponse est renvoyée au frontend
18. Le frontend affiche la réponse dans l'interface de chat
19. L'utilisateur peut poser une nouvelle question ou approfondir la conversation

## Considérations techniques

### Sécurité
- Les requêtes destructives (INSERT, UPDATE, DELETE) sont bloquées
- Le service API vérifie les requêtes pour détecter les injections SQL
- Les permissions de la base de données sont limitées en lecture seule

### Performance
- Les requêtes sont limitées à 5 résultats par défaut
- Les requêtes sont optimisées pour ne demander que les colonnes pertinentes
- Le cache est utilisé pour les schémas de tables fréquemment consultés

### Gestion des erreurs
- Les erreurs SQL sont capturées et renvoyées au modèle pour reformulation
- Les erreurs de connexion à la base de données sont gérées avec des messages clairs
- Les timeouts sont implémentés pour éviter les requêtes bloquantes