# Architecture détaillée de l'EMA Companion AI

Ce document décrit en détail l'architecture et les flux de données entre les différentes couches de l'application EMA Companion AI.

## Vue d'ensemble

L'application est composée de trois couches principales:
1. **Frontend React** (port 3000) - Interface utilisateur
2. **Backend LangGraph** (port 3005) - Agent conversationnel
3. **Service API de base de données** (port 8086) - Interface avec la base de données SQL

## Diagramme d'architecture détaillé

```mermaid
flowchart TD
    %% Définition des styles
    classDef frontend fill:#d4f1f9,stroke:#05a,stroke-width:1px
    classDef backend fill:#e1d5e7,stroke:#735,stroke-width:1px
    classDef database fill:#d5e8d4,stroke:#376,stroke-width:1px
    classDef model fill:#fff2cc,stroke:#d6b656,stroke-width:1px
    
    %% Frontend (Port 3000)
    subgraph FE["Frontend React (Port 3000)"]
        A[index.html] --> B[main.tsx]
        B --> C[App.tsx]
        C --> D[ChatInterface.tsx]
        D --> E[MessageList.tsx]
        D --> F[MessageInput.tsx]
        D --> G[Sidebar.tsx]
        H[api.ts] --> D
    end
    
    %% Backend (Port 3005)
    subgraph BE["Backend LangGraph (Port 3005)"]
        I[server.ts] --> J[index.ts]
        J --> K[createSQLAgent]
        K --> L[StateGraph]
        
        subgraph LG["LangGraph Workflow"]
            L --> M["callModel Node"]
            L --> N["tools Node"]
            M <--> N
        end
        
        O[tools.ts] --> N
        P[api-database.ts] --> O
    end
    
    %% Database API Service (Port 8086)
    subgraph DB["Database API Service (Port 8086)"]
        Q[main.py] --> R[FastAPI]
        R --> S["/tables"]
        R --> T["/schema"]
        R --> U["/query"]
        R --> V["/query_checker"]
        R --> W["/context"]
        X[pyodbc] --> Y["SQL Database\n(4D/SQL Server)"]
        S & T & U & V & W --> X
    end
    
    %% External Services
    subgraph EX["Services Externes"]
        Z["Anthropic Claude API"]
    end
    
    %% Flux de données
    H -- "1. POST /api/message\n{message: string}" --> I
    I -- "2. createSQLAgent(message)" --> K
    K -- "3. Initialisation du workflow" --> L
    M -- "4. Appel au modèle LLM" --> Z
    Z -- "5. Réponse du modèle" --> M
    M -- "6. Si tool_calls, route vers tools" --> N
    N -- "7. Exécution des outils SQL" --> P
    P -- "8. Appels API" --> R
    X -- "9. Requêtes SQL" --> Y
    Y -- "10. Résultats SQL" --> X
    X -- "11. Résultats formatés" --> R
    R -- "12. Réponse JSON" --> P
    P -- "13. Résultats des outils" --> N
    N -- "14. Retour au modèle avec résultats" --> M
    M -- "15. Génération de réponse finale" --> K
    K -- "16. Réponse complète" --> I
    I -- "17. Réponse JSON\n{id, role, content}" --> H
    H -- "18. Mise à jour de l'interface" --> D
    
    %% Application des styles
    class A,B,C,D,E,F,G,H frontend
    class I,J,K,L,M,N,O,P backend
    class Q,R,S,T,U,V,W,X,Y database
    class Z model
```

## Description détaillée des flux

### 1. Interaction utilisateur → Frontend

1. L'utilisateur saisit un message dans `MessageInput.tsx`
2. Le message est envoyé au service API via `api.ts`
3. L'interface affiche un indicateur de chargement

### 2. Frontend → Backend

4. Le service API envoie une requête POST à `/api/message` avec le message de l'utilisateur
5. `server.ts` reçoit la requête et appelle `createSQLAgent` avec le message
6. `createSQLAgent` initialise le workflow LangGraph avec le message comme état initial

### 3. Backend → LangGraph Workflow

7. Le workflow démarre au nœud `callModel`
8. `callModel` prépare les messages (système + utilisateur) et appelle le modèle LLM (Claude)
9. Le modèle génère une réponse qui peut contenir des appels d'outils (tool_calls)
10. `routeModelOutput` détermine si la réponse contient des appels d'outils
    - Si oui, le flux continue vers le nœud `tools`
    - Si non, le workflow se termine et renvoie la réponse

### 4. LangGraph → Outils SQL

11. Le nœud `tools` reçoit les appels d'outils du modèle
12. Il exécute les outils demandés via `tools.ts`:
    - `listTablesTool` - Liste toutes les tables disponibles
    - `getSchemaTool` - Récupère le schéma d'une table spécifique
    - `runQueryTool` - Exécute une requête SQL
    - `checkQueryTool` - Vérifie une requête SQL pour les erreurs courantes
    - `generateContextQueryTool` - Récupère le contexte métier

### 5. Outils SQL → Service API de base de données

13. Les outils SQL font des appels API au service de base de données via `api-database.ts`
14. Les requêtes sont envoyées aux endpoints correspondants:
    - GET `/tables` - Liste des tables
    - POST `/schema` - Schéma d'une table
    - POST `/query` - Exécution d'une requête
    - POST `/query_checker` - Vérification d'une requête
    - GET `/context` - Contexte métier

### 6. Service API → Base de données

15. Le service API reçoit les requêtes et les traite via FastAPI
16. Il utilise pyodbc pour se connecter à la base de données SQL
17. Les requêtes SQL sont exécutées sur la base de données
18. Les résultats sont formatés en JSON et renvoyés

### 7. Retour des résultats

19. Les résultats des outils sont renvoyés au nœud `tools`
20. Le workflow retourne au nœud `callModel` avec les résultats
21. Le modèle génère une réponse finale basée sur les résultats
22. La réponse finale est renvoyée au frontend

### 8. Affichage de la réponse

23. Le frontend reçoit la réponse et met à jour l'interface utilisateur
24. La réponse est affichée dans `MessageList.tsx`
25. L'indicateur de chargement est masqué

## Cycle de vie d'une requête utilisateur

1. **Initialisation**:
   - L'utilisateur pose une question sur la base de données
   - Le frontend envoie la question au backend

2. **Analyse et planification**:
   - Le modèle LLM analyse la question
   - Il décide quelles tables sont pertinentes
   - Il demande les schémas des tables pertinentes

3. **Génération et exécution de requête**:
   - Le modèle génère une requête SQL
   - La requête est vérifiée pour les erreurs
   - La requête est exécutée sur la base de données

4. **Formulation de réponse**:
   - Le modèle analyse les résultats de la requête
   - Il formule une réponse en langage naturel
   - Il inclut les résultats formatés dans sa réponse

5. **Présentation**:
   - La réponse est renvoyée au frontend
   - L'interface affiche la réponse à l'utilisateur

## Considérations techniques

- **Gestion des erreurs**: Les erreurs SQL sont capturées et renvoyées au modèle pour reformulation
- **Limitation des requêtes**: Les requêtes sont limitées à 5 résultats par défaut
- **Sécurité**: Les requêtes destructives (INSERT, UPDATE, DELETE) sont bloquées
- **Performance**: Les requêtes sont optimisées pour ne demander que les colonnes pertinentes