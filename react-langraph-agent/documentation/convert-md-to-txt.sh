#!/bin/bash

# Script pour convertir tous les fichiers .md en .txt
# Supprime le formatage Markdown pour créer des versions texte lisibles

echo "🔄 Conversion des fichiers Markdown en format texte..."

# Fonction pour convertir un fichier MD en TXT
convert_md_to_txt() {
    local md_file="$1"
    local txt_file="${md_file%.md}.txt"
    
    echo "📄 Conversion: $md_file → $txt_file"
    
    # Conversion avec suppression du formatage Markdown
    sed -E '
        # Supprimer les titres Markdown (# ## ###)
        s/^#{1,6} //g
        
        # Supprimer les liens [text](url)
        s/\[([^\]]*)\]\([^\)]*\)/\1/g
        
        # Supprimer le formatage gras **text**
        s/\*\*([^\*]*)\*\*/\1/g
        
        # Supprimer le formatage italique *text*
        s/\*([^\*]*)\*/\1/g
        
        # Supprimer les blocs de code ```
        s/```[a-zA-Z]*//g
        s/```//g
        
        # Supprimer les backticks simples `code`
        s/`([^`]*)`/\1/g
        
        # Supprimer les listes - item
        s/^- //g
        s/^  - //g
        s/^    - //g
        
        # Supprimer les listes numérotées
        s/^[0-9]+\. //g
        
        # Supprimer les citations >
        s/^> //g
        
        # Supprimer les lignes de séparation ---
        /^-{3,}$/d
        /^={3,}$/d
        
        # Supprimer les emojis et caractères spéciaux
        s/[🚀🎯📋🔧⚡📄🧠🔍📊🎉✅❌🔄📝🧪💥🎊]/*/g
        
    ' "$md_file" > "$txt_file"
    
    echo "✅ Conversion terminée: $txt_file"
}

# Convertir tous les fichiers .md du répertoire
for md_file in *.md; do
    if [ -f "$md_file" ]; then
        convert_md_to_txt "$md_file"
    fi
done

echo ""
echo "🎉 Conversion terminée ! Fichiers créés :"
ls -la *.txt

echo ""
echo "📁 Structure du répertoire documentation :"
ls -la
