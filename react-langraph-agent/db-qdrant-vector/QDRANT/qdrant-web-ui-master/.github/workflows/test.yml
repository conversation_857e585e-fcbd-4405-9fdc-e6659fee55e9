# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.
# ESLint is a tool for identifying and reporting on patterns
# found in ECMAScript/JavaScript code.
# More details at https://github.com/eslint/eslint
# and https://eslint.org

name: ESLint

on:
  push:
    branches: ["master"]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: ["master"]
  schedule:
    - cron: "40 23 * * 1"

jobs:
  test:
    name: Check the source code
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: Install packages
        run: npm ci
      - name: Audit
        run: npm audit
      - name: Lint
        run: npm run lint
      - name: Lint
        run: npm run format:check
      - name: Test
        run: npm run test
