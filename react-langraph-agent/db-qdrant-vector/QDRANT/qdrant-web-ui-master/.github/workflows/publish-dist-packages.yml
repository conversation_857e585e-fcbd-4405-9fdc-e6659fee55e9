# This workflow will run tests using node and then publish a package to GitHub Packages when a release is created
# For more information see: https://docs.github.com/en/actions/publishing-packages/publishing-nodejs-packages

name: Dist build

on:
  release:
    types: [created]
  workflow_dispatch:
  push:
    # Pattern matched against refs/tags
    tags:
      - '*'           # Push events to every tag not containing /

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - run: npm ci
      - run: npm test -- --run

  publish-dist:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - run: npm ci
      - name: Set npm package version
        if: startsWith(github.ref, 'refs/tags/')
        run: npm version --allow-same-version --no-git-tag-version ${GITHUB_REF#refs/*/}
      - run: npm run build-qdrant
      - run: npm sbom --sbom-format spdx > dist/qdrant-web-ui.spdx.json
      - name: Archive dist folder
        uses: montudor/action-zip@v1
        with:
          args: zip -r dist-qdrant.zip dist
      - name: Release
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: dist-qdrant.zip

