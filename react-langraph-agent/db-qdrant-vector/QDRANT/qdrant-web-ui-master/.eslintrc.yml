env:
  browser: true
  es2021: true
  node: true
settings:
  react:
    version: detect
root: true
extends:
  - eslint:recommended
  - google
  - prettier
  - plugin:react/recommended
overrides: []
parserOptions:
  ecmaVersion: latest
  sourceType: module
plugins:
  - react
rules:
  {
    'max-len': [
      'error',
      {
        code: 120,
        ignoreComments: true,
        ignoreTemplateLiterals: true,
        ignoreUrls: true,
      },
    ],
    'no-process-env': 'off',
    'require-jsdoc': 'off',
    'no-unused-vars': [
      'error',
      {
        varsIgnorePattern: '^React$',
      },
    ],
  }
ignorePatterns: ['node_modules/', 'build/', '*.test.js', '*.test.jsx']
