# Interactive Tutorials
## Set up your cluster, add data, and start searching!

**Setup Guide**
- [Quickstart](#/tutorial/quickstart) - Create a collection, upsert vectors, and run a search.
- [Load Data](#/tutorial/loadcontent) - Load a prepared dataset snapshot into your collection.

**Vector Search**
- [Filtering - Beginner](#/tutorial/filteringbeginner) - Filter search results using basic payload conditions.
- [Filtering - Advanced](#/tutorial/filteringadvanced) - Try advanced filtering based on nested payload conditions.
- [Filtering - Full Text](#/tutorial/filteringfulltext) - Search for substrings, tokens, or phrases within text fields.
- [Multivector Search](#/tutorial/multivectors) - Work with data represented by ColBERT multivectors.
- [Sparse Vector Search](#/tutorial/sparsevectors) - Use sparse vectors to get specific search results.
- [Hybrid Search](#/tutorial/hybridsearch) - Combine dense and sparse vectors for more accurate search results.

**Manage Data**
- [Multitenancy](#/tutorial/multitenancy) - Manage multiple users within a single collection.

