export const title = 'Quickstart';

# Quickstart: Vector Search for Beginners

Qdrant is designed to find the approximate nearest data points in your dataset. In this quickstart guide, you'll create a simple database to track space colonies and perform a search for the nearest colony based on its vector representation.

<Alert severity="info">Click **RUN** to send the API request. The response will appear on the right.<br/>You can also edit any code block and rerun the request to see different results.</Alert>

## Step 1: Create a collection

First, we’ll create a collection called `star_charts` to store the colony data. Each location will be represented by a vector of four dimensions, and we'll use the Dot product as the distance metric for similarity search.

Run this command to create the collection:

```json withRunButton=true
PUT collections/star_charts
{
  "vectors": {
    "size": 4,
    "distance": "Dot"
  }
}
```

## Step 2: Load data into the collection

Now that the collection is set up, let’s add some data. Each location will have a vector and additional information (payload), such as its name.

Run this request to add the data:

```json withRunButton=true
PUT collections/star_charts/points
{
  "points": [
    {
      "id": 1,
      "vector": [0.05, 0.61, 0.76, 0.74],
      "payload": {
        "colony": "Mars"
      }
    },
    {
      "id": 2,
      "vector": [0.19, 0.81, 0.75, 0.11],
      "payload": {
        "colony": "Jupiter"
      }
    },
    {
      "id": 3,
      "vector": [0.36, 0.55, 0.47, 0.94],
      "payload": {
        "colony": "Venus"
      }
    },
    {
      "id": 4,
      "vector": [0.18, 0.01, 0.85, 0.80],
      "payload": {
        "colony": "Moon"
      }
    },
    {
      "id": 5,
      "vector": [0.24, 0.18, 0.22, 0.44],
      "payload": {
        "colony": "Pluto"
      }
    }
  ]
}
```

## Step 3: Run a search query

Now, let’s search for the three nearest colonies to a specific vector representing a spatial location. This query will return the colonies along with their payload information.

Run the query below to find the nearest colonies:

```json withRunButton=true
POST collections/star_charts/points/search
{
  "vector": [0.2, 0.1, 0.9, 0.7],
  "limit": 3,
  "with_payload": true
}
```

## Conclusion

Congratulations! 🎉 You’ve just completed a vector search across galactic coordinates! You've successfully added spatial data into a collection and performed searches to find the nearest locations based on their vector representation.

## Next steps

In the next section, you’ll explore creating complex filter conditions to refine your searches further for interstellar exploration!

