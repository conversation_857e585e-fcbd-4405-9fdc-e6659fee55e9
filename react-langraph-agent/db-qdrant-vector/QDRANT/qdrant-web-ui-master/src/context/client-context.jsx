import React, { useContext, createContext, useState, useEffect } from 'react';
import { axiosInstance, setupAxios } from '../common/axios';
import qdrantClient from '../common/client';
import { bigIntJSON } from '../common/bigIntJSON';
import { isTokenRestricted } from '../config/restricted-routes';

const DEFAULT_SETTINGS = {
  apiKey: '',
};

// Write settings to local storage
const persistSettings = (settings) => {
  localStorage.setItem('settings', bigIntJSON.stringify(settings));
};

// Get existing Settings from Local Storage or set default values
const getPersistedSettings = () => {
  const settings = localStorage.getItem('settings');

  if (settings) return bigIntJSON.parse(settings);

  return DEFAULT_SETTINGS;
};

// React context to store the settings
const ClientContext = createContext();

// React hook to access and modify the settings
export const useClient = () => {
  const context = useContext(ClientContext);

  if (!context) {
    throw new Error('useClient must be used within ClientProvider');
  }

  return {
    ...context,
    isRestricted: isTokenRestricted(context.settings.apiKey),
  };
};

// Client Context Provider
export const ClientProvider = (props) => {
  // TODO: Switch to Reducer if we have more settings to track.
  const [settings, setSettings] = useState(getPersistedSettings());

  const client = qdrantClient(settings);

  setupAxios(axiosInstance, settings);

  useEffect(() => {
    setupAxios(axiosInstance, settings);
    persistSettings(settings);
  }, [settings]);

  return <ClientContext.Provider value={{ client, settings, setSettings }} {...props} />;
};
