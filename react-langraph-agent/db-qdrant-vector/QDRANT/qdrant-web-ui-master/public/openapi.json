{"paths": {"/collections/{collection_name}/shards": {"put": {"tags": ["Distributed"], "summary": "Create shard key", "operationId": "create_shard_key", "requestBody": {"description": "Shard key configuration", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateShardingKey"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to create shards for", "required": true, "schema": {"type": "string"}}, {"name": "timeout", "in": "query", "description": "Wait for operation commit timeout in seconds. \nIf timeout is reached - request will return with service error.\n", "schema": {"type": "integer"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}}}}, "/collections/{collection_name}/shards/delete": {"post": {"tags": ["Distributed"], "summary": "Delete shard key", "operationId": "delete_shard_key", "requestBody": {"description": "Select shard key to delete", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DropShardingKey"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to create shards for", "required": true, "schema": {"type": "string"}}, {"name": "timeout", "in": "query", "description": "Wait for operation commit timeout in seconds. \nIf timeout is reached - request will return with service error.\n", "schema": {"type": "integer"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}}}}, "/": {"get": {"summary": "Returns information about the running Qdrant instance", "description": "Returns information about the running Qdrant instance like version and commit id", "operationId": "root", "tags": ["Service"], "responses": {"200": {"description": "Qdrant server version information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionInfo"}}}}, "4XX": {"description": "error"}}}}, "/telemetry": {"get": {"summary": "Collect telemetry data", "description": "Collect telemetry data including app info, system info, collections info, cluster info, configs and statistics", "operationId": "telemetry", "tags": ["Service"], "parameters": [{"name": "anonymize", "in": "query", "description": "If true, anonymize result", "required": false, "schema": {"type": "boolean"}}, {"name": "details_level", "in": "query", "description": "Level of details in telemetry data. Minimal level is 0, maximal is infinity", "required": false, "schema": {"type": "integer", "minimum": 0}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/TelemetryData"}}}}}}}}}, "/metrics": {"get": {"summary": "Collect Prometheus metrics data", "description": "Collect metrics data including app info, collections info, cluster info and statistics", "operationId": "metrics", "tags": ["Service"], "parameters": [{"name": "anonymize", "in": "query", "description": "If true, anonymize result", "required": false, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Metrics data in Prometheus format", "content": {"text/plain": {"schema": {"type": "string", "example": "# HELP app_info information about qdrant server\n# TYPE app_info gauge\napp_info{name=\"qdrant\",version=\"0.11.1\"} 1\n# HELP cluster_enabled is cluster support enabled\n# TYPE cluster_enabled gauge\ncluster_enabled 0\n# HELP collections_total number of collections\n# TYPE collections_total gauge\ncollections_total 1\n"}}}}, "4XX": {"description": "error"}}}}, "/locks": {"post": {"summary": "Set lock options", "description": "Set lock options. If write is locked, all write operations and collection creation are forbidden. Returns previous lock options", "operationId": "post_locks", "tags": ["Service"], "requestBody": {"description": "Lock options and optional error message", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocksOption"}}}}, "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/LocksOption"}}}}}}}}, "get": {"summary": "Get lock options", "description": "Get lock options. If write is locked, all write operations and collection creation are forbidden", "operationId": "get_locks", "tags": ["Service"], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/LocksOption"}}}}}}}}}, "/healthz": {"get": {"summary": "Kubernetes healthz endpoint", "description": "An endpoint for health checking used in Kubernetes.", "operationId": "healthz", "tags": ["Service"], "responses": {"200": {"description": "Healthz response", "content": {"text/plain": {"schema": {"type": "string", "example": "healthz check passed"}}}}, "4XX": {"description": "error"}}}}, "/livez": {"get": {"summary": "Kubernetes livez endpoint", "description": "An endpoint for health checking used in Kubernetes.", "operationId": "livez", "tags": ["Service"], "responses": {"200": {"description": "Healthz response", "content": {"text/plain": {"schema": {"type": "string", "example": "healthz check passed"}}}}, "4XX": {"description": "error"}}}}, "/readyz": {"get": {"summary": "Kubernetes readyz endpoint", "description": "An endpoint for health checking used in Kubernetes.", "operationId": "readyz", "tags": ["Service"], "responses": {"200": {"description": "Healthz response", "content": {"text/plain": {"schema": {"type": "string", "example": "healthz check passed"}}}}, "4XX": {"description": "error"}}}}, "/issues": {"get": {"summary": "Get issues", "description": "Get a report of performance issues and configuration suggestions", "operationId": "get_issues", "tags": ["Beta"], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object"}}}}, "4XX": {"description": "error"}}}, "delete": {"summary": "Clear issues", "description": "Removes all issues reported so far", "operationId": "clear_issues", "tags": ["Beta"], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "4XX": {"description": "error"}}}}, "/cluster": {"get": {"tags": ["Distributed"], "summary": "Get cluster status info", "description": "Get information about the current state and composition of the cluster", "operationId": "cluster_status", "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/ClusterStatus"}}}}}}}}}, "/cluster/recover": {"post": {"tags": ["Distributed"], "summary": "Tries to recover current peer Raft state.", "operationId": "recover_current_peer", "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}}}}, "/cluster/peer/{peer_id}": {"delete": {"tags": ["Distributed"], "summary": "Remove peer from the cluster", "description": "Tries to remove peer from the cluster. Will return an error if peer has shards on it.", "operationId": "remove_peer", "parameters": [{"name": "peer_id", "in": "path", "description": "Id of the peer", "required": true, "schema": {"type": "integer"}}, {"name": "force", "in": "query", "description": "If true - removes peer even if it has shards/replicas on it.", "schema": {"type": "boolean", "default": false}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}}}}, "/collections": {"get": {"tags": ["Collections"], "summary": "List collections", "description": "Get list name of all existing collections", "operationId": "get_collections", "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/CollectionsResponse"}}}}}}}}}, "/collections/{collection_name}": {"get": {"tags": ["Collections"], "summary": "Collection info", "description": "Get detailed information about specified existing collection", "operationId": "get_collection", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to retrieve", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/CollectionInfo"}}}}}}}}, "put": {"tags": ["Collections"], "summary": "Create collection", "description": "Create new collection with given parameters", "operationId": "create_collection", "requestBody": {"description": "Parameters of a new collection", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCollection"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the new collection", "required": true, "schema": {"type": "string"}}, {"name": "timeout", "in": "query", "description": "Wait for operation commit timeout in seconds. \nIf timeout is reached - request will return with service error.\n", "schema": {"type": "integer"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}}}, "patch": {"tags": ["Collections"], "summary": "Update collection parameters", "description": "Update parameters of the existing collection", "operationId": "update_collection", "requestBody": {"description": "New parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCollection"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to update", "required": true, "schema": {"type": "string"}}, {"name": "timeout", "in": "query", "description": "Wait for operation commit timeout in seconds. \nIf timeout is reached - request will return with service error.\n", "schema": {"type": "integer"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}}}, "delete": {"tags": ["Collections"], "summary": "Delete collection", "description": "Drop collection and all associated data", "operationId": "delete_collection", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to delete", "required": true, "schema": {"type": "string"}}, {"name": "timeout", "in": "query", "description": "Wait for operation commit timeout in seconds. \nIf timeout is reached - request will return with service error.\n", "schema": {"type": "integer"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}}}}, "/collections/aliases": {"post": {"tags": ["Aliases"], "summary": "Update aliases of the collections", "operationId": "update_aliases", "requestBody": {"description": "Alias update operations", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeAliasesOperation"}}}}, "parameters": [{"name": "timeout", "in": "query", "description": "Wait for operation commit timeout in seconds. \nIf timeout is reached - request will return with service error.\n", "schema": {"type": "integer"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}}}}, "/collections/{collection_name}/index": {"put": {"tags": ["Indexes"], "summary": "Create index for field in collection", "description": "Create index for field in collection", "operationId": "create_field_index", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen", "required": false, "schema": {"type": "boolean"}}, {"name": "ordering", "in": "query", "description": "define ordering guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/WriteOrdering"}}], "requestBody": {"description": "Field name", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFieldIndex"}}}}, "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/UpdateResult"}}}}}}}}}, "/collections/{collection_name}/exists": {"get": {"tags": ["Collections"], "summary": "Check the existence of a collection", "description": "Returns \"true\" if the given collection name exists, and \"false\" otherwise", "operationId": "collection_exists", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/CollectionExistence"}}}}}}}}}, "/collections/{collection_name}/index/{field_name}": {"delete": {"tags": ["Indexes"], "summary": "Delete index for field in collection", "description": "Delete field index for collection", "operationId": "delete_field_index", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection", "required": true, "schema": {"type": "string"}}, {"name": "field_name", "in": "path", "description": "Name of the field where to delete the index", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen", "required": false, "schema": {"type": "boolean"}}, {"name": "ordering", "in": "query", "description": "define ordering guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/WriteOrdering"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/UpdateResult"}}}}}}}}}, "/collections/{collection_name}/cluster": {"get": {"tags": ["Distributed"], "summary": "Collection cluster info", "description": "Get cluster information for a collection", "operationId": "collection_cluster_info", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to retrieve the cluster info for", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/CollectionClusterInfo"}}}}}}}}, "post": {"tags": ["Distributed"], "summary": "Update collection cluster setup", "operationId": "update_collection_cluster", "requestBody": {"description": "Collection cluster update operations", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClusterOperations"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection on which to to apply the cluster update operation", "required": true, "schema": {"type": "string"}}, {"name": "timeout", "in": "query", "description": "Wait for operation commit timeout in seconds. \nIf timeout is reached - request will return with service error.\n", "schema": {"type": "integer"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}}}}, "/collections/{collection_name}/aliases": {"get": {"tags": ["Aliases"], "summary": "List aliases for collection", "description": "Get list of all aliases for a collection", "operationId": "get_collection_aliases", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/CollectionsAliasesResponse"}}}}}}}}}, "/aliases": {"get": {"tags": ["Aliases"], "summary": "List collections aliases", "description": "Get list of all existing collections aliases", "operationId": "get_collections_aliases", "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/CollectionsAliasesResponse"}}}}}}}}}, "/collections/{collection_name}/snapshots/upload": {"post": {"tags": ["Snapshots"], "summary": "Recover from an uploaded snapshot", "description": "Recover local collection data from an uploaded snapshot. This will overwrite any data, stored on this node, for the collection. If collection does not exist - it will be created.", "operationId": "recover_from_uploaded_snapshot", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen. If false - let changes happen in background. Default is true.", "required": false, "schema": {"type": "boolean"}}, {"name": "priority", "in": "query", "description": "Defines source of truth for snapshot recovery", "required": false, "schema": {"$ref": "#/components/schemas/SnapshotPriority"}}, {"name": "checksum", "in": "query", "description": "Optional SHA256 checksum to verify snapshot integrity before recovery.", "required": false, "schema": {"type": "string"}}], "requestBody": {"description": "Snap<PERSON> to recover from", "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"snapshot": {"type": "string", "format": "binary"}}}}}}, "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}, "202": {"description": "operation is accepted", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request"}, "status": {"type": "string"}}}}}}}}}, "/collections/{collection_name}/snapshots/recover": {"put": {"tags": ["Snapshots"], "summary": "Recover from a snapshot", "description": "Recover local collection data from a snapshot. This will overwrite any data, stored on this node, for the collection. If collection does not exist - it will be created.", "operationId": "recover_from_snapshot", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen. If false - let changes happen in background. Default is true.", "required": false, "schema": {"type": "boolean"}}], "requestBody": {"description": "Snap<PERSON> to recover from", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SnapshotRecover"}}}}, "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}, "202": {"description": "operation is accepted", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request"}, "status": {"type": "string"}}}}}}}}}, "/collections/{collection_name}/snapshots": {"get": {"tags": ["Snapshots"], "summary": "List collection snapshots", "description": "Get list of snapshots for a collection", "operationId": "list_snapshots", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/SnapshotDescription"}}}}}}}}}, "post": {"tags": ["Snapshots"], "summary": "Create collection snapshot", "description": "Create new snapshot for a collection", "operationId": "create_snapshot", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection for which to create a snapshot", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen. If false - let changes happen in background. Default is true.", "required": false, "schema": {"type": "boolean"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/SnapshotDescription"}}}}}}, "202": {"description": "operation is accepted", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request"}, "status": {"type": "string"}}}}}}}}}, "/collections/{collection_name}/snapshots/{snapshot_name}": {"delete": {"tags": ["Snapshots"], "summary": "Delete collection snapshot", "description": "Delete snapshot for a collection", "operationId": "delete_snapshot", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection for which to delete a snapshot", "required": true, "schema": {"type": "string"}}, {"name": "snapshot_name", "in": "path", "description": "Name of the snapshot to delete", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen. If false - let changes happen in background. Default is true.", "required": false, "schema": {"type": "boolean"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}, "202": {"description": "operation is accepted", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request"}, "status": {"type": "string"}}}}}}}}, "get": {"tags": ["Snapshots"], "summary": "Download collection snapshot", "description": "Download specified snapshot from a collection as a file", "operationId": "get_snapshot", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection", "required": true, "schema": {"type": "string"}}, {"name": "snapshot_name", "in": "path", "description": "Name of the snapshot to download", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Snapshot file", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/snapshots": {"get": {"tags": ["Snapshots"], "summary": "List of storage snapshots", "description": "Get list of snapshots of the whole storage", "operationId": "list_full_snapshots", "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/SnapshotDescription"}}}}}}}}}, "post": {"tags": ["Snapshots"], "summary": "Create storage snapshot", "description": "Create new snapshot of the whole storage", "operationId": "create_full_snapshot", "parameters": [{"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen. If false - let changes happen in background. Default is true.", "required": false, "schema": {"type": "boolean"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/SnapshotDescription"}}}}}}, "202": {"description": "operation is accepted", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request"}, "status": {"type": "string"}}}}}}}}}, "/snapshots/{snapshot_name}": {"delete": {"tags": ["Snapshots"], "summary": "Delete storage snapshot", "description": "Delete snapshot of the whole storage", "operationId": "delete_full_snapshot", "parameters": [{"name": "snapshot_name", "in": "path", "description": "Name of the full snapshot to delete", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen. If false - let changes happen in background. Default is true.", "required": false, "schema": {"type": "boolean"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}, "202": {"description": "operation is accepted", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request"}, "status": {"type": "string"}}}}}}}}, "get": {"tags": ["Snapshots"], "summary": "Download storage snapshot", "description": "Download specified snapshot of the whole storage as a file", "operationId": "get_full_snapshot", "parameters": [{"name": "snapshot_name", "in": "path", "description": "Name of the snapshot to download", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Snapshot file", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/collections/{collection_name}/shards/{shard_id}/snapshots/upload": {"post": {"tags": ["Snapshots"], "summary": "Recover shard from an uploaded snapshot", "description": "Recover shard of a local collection from an uploaded snapshot. This will overwrite any data, stored on this node, for the collection shard.", "operationId": "recover_shard_from_uploaded_snapshot", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection", "required": true, "schema": {"type": "string"}}, {"name": "shard_id", "in": "path", "description": "Id of the shard to recover", "required": true, "schema": {"type": "integer"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen. If false - let changes happen in background. Default is true.", "required": false, "schema": {"type": "boolean"}}, {"name": "priority", "in": "query", "description": "Defines source of truth for snapshot recovery", "required": false, "schema": {"$ref": "#/components/schemas/SnapshotPriority"}}, {"name": "checksum", "in": "query", "description": "Optional SHA256 checksum to verify snapshot integrity before recovery.", "required": false, "schema": {"type": "string"}}], "requestBody": {"description": "Snap<PERSON> to recover from", "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"snapshot": {"type": "string", "format": "binary"}}}}}}, "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}, "202": {"description": "operation is accepted", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request"}, "status": {"type": "string"}}}}}}}}}, "/collections/{collection_name}/shards/{shard_id}/snapshots/recover": {"put": {"tags": ["Snapshots"], "summary": "Recover from a snapshot", "description": "Recover shard of a local collection data from a snapshot. This will overwrite any data, stored in this shard, for the collection.", "operationId": "recover_shard_from_snapshot", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection", "required": true, "schema": {"type": "string"}}, {"name": "shard_id", "in": "path", "description": "Id of the shard to recover", "required": true, "schema": {"type": "integer"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen. If false - let changes happen in background. Default is true.", "required": false, "schema": {"type": "boolean"}}], "requestBody": {"description": "Snap<PERSON> to recover from", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShardSnapshotRecover"}}}}, "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}, "202": {"description": "operation is accepted", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request"}, "status": {"type": "string"}}}}}}}}}, "/collections/{collection_name}/shards/{shard_id}/snapshots": {"get": {"tags": ["Snapshots"], "summary": "List shards snapshots for a collection", "description": "Get list of snapshots for a shard of a collection", "operationId": "list_shard_snapshots", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection", "required": true, "schema": {"type": "string"}}, {"name": "shard_id", "in": "path", "description": "Id of the shard", "required": true, "schema": {"type": "integer"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/SnapshotDescription"}}}}}}}}}, "post": {"tags": ["Snapshots"], "summary": "Create shard snapshot", "description": "Create new snapshot of a shard for a collection", "operationId": "create_shard_snapshot", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection for which to create a snapshot", "required": true, "schema": {"type": "string"}}, {"name": "shard_id", "in": "path", "description": "Id of the shard", "required": true, "schema": {"type": "integer"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen. If false - let changes happen in background. Default is true.", "required": false, "schema": {"type": "boolean"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/SnapshotDescription"}}}}}}, "202": {"description": "operation is accepted", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request"}, "status": {"type": "string"}}}}}}}}}, "/collections/{collection_name}/shards/{shard_id}/snapshots/{snapshot_name}": {"delete": {"tags": ["Snapshots"], "summary": "Delete shard snapshot", "description": "Delete snapshot of a shard for a collection", "operationId": "delete_shard_snapshot", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection for which to delete a snapshot", "required": true, "schema": {"type": "string"}}, {"name": "shard_id", "in": "path", "description": "Id of the shard", "required": true, "schema": {"type": "integer"}}, {"name": "snapshot_name", "in": "path", "description": "Name of the snapshot to delete", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen. If false - let changes happen in background. Default is true.", "required": false, "schema": {"type": "boolean"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "boolean"}}}}}}, "202": {"description": "operation is accepted", "content": {"application/json": {"schema": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request"}, "status": {"type": "string"}}}}}}}}, "get": {"tags": ["Snapshots"], "summary": "Download collection snapshot", "description": "Download specified snapshot of a shard from a collection as a file", "operationId": "get_shard_snapshot", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection", "required": true, "schema": {"type": "string"}}, {"name": "shard_id", "in": "path", "description": "Id of the shard", "required": true, "schema": {"type": "integer"}}, {"name": "snapshot_name", "in": "path", "description": "Name of the snapshot to download", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Snapshot file", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/collections/{collection_name}/points/{id}": {"get": {"tags": ["Points"], "summary": "Get point", "description": "Retrieve full information of single point by id", "operationId": "get_point", "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to retrieve from", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "Id of the point", "required": true, "schema": {"$ref": "#/components/schemas/ExtendedPointId"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/Record"}}}}}}}}}, "/collections/{collection_name}/points": {"post": {"tags": ["Points"], "summary": "Get points", "description": "Retrieve multiple points by specified IDs", "operationId": "get_points", "requestBody": {"description": "List of points to retrieve", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PointRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to retrieve from", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/Record"}}}}}}}}}, "put": {"tags": ["Points"], "summary": "Upsert points", "description": "Perform insert + updates on points. If point with given ID already exists - it will be overwritten.", "operationId": "upsert_points", "requestBody": {"description": "Operation to perform on points", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PointInsertOperations"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to update from", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen", "required": false, "schema": {"type": "boolean"}}, {"name": "ordering", "in": "query", "description": "define ordering guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/WriteOrdering"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/UpdateResult"}}}}}}}}}, "/collections/{collection_name}/points/delete": {"post": {"tags": ["Points"], "summary": "Delete points", "description": "Delete points", "operationId": "delete_points", "requestBody": {"description": "Operation to perform on points", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PointsSelector"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to delete from", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen", "required": false, "schema": {"type": "boolean"}}, {"name": "ordering", "in": "query", "description": "define ordering guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/WriteOrdering"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/UpdateResult"}}}}}}}}}, "/collections/{collection_name}/points/vectors": {"put": {"tags": ["Points"], "summary": "Update vectors", "description": "Update specified named vectors on points, keep unspecified vectors intact.", "operationId": "update_vectors", "requestBody": {"description": "Update named vectors on points", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateVectors"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to update from", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen", "required": false, "schema": {"type": "boolean"}}, {"name": "ordering", "in": "query", "description": "define ordering guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/WriteOrdering"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/UpdateResult"}}}}}}}}}, "/collections/{collection_name}/points/vectors/delete": {"post": {"tags": ["Points"], "summary": "Delete vectors", "description": "Delete named vectors from the given points.", "operationId": "delete_vectors", "requestBody": {"description": "Delete named vectors from points", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteVectors"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to delete from", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen", "required": false, "schema": {"type": "boolean"}}, {"name": "ordering", "in": "query", "description": "define ordering guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/WriteOrdering"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/UpdateResult"}}}}}}}}}, "/collections/{collection_name}/points/payload": {"post": {"tags": ["Points"], "summary": "Set payload", "description": "Set payload values for points", "operationId": "set_payload", "requestBody": {"description": "Set payload on points", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetPayload"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to set from", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen", "required": false, "schema": {"type": "boolean"}}, {"name": "ordering", "in": "query", "description": "define ordering guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/WriteOrdering"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/UpdateResult"}}}}}}}}, "put": {"tags": ["Points"], "summary": "Overwrite payload", "description": "Replace full payload of points with new one", "operationId": "overwrite_payload", "requestBody": {"description": "Payload and points selector", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetPayload"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to set from", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen", "required": false, "schema": {"type": "boolean"}}, {"name": "ordering", "in": "query", "description": "define ordering guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/WriteOrdering"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/UpdateResult"}}}}}}}}}, "/collections/{collection_name}/points/payload/delete": {"post": {"tags": ["Points"], "summary": "Delete payload", "description": "Delete specified key payload for points", "operationId": "delete_payload", "requestBody": {"description": "delete payload on points", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeletePayload"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to delete from", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen", "required": false, "schema": {"type": "boolean"}}, {"name": "ordering", "in": "query", "description": "define ordering guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/WriteOrdering"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/UpdateResult"}}}}}}}}}, "/collections/{collection_name}/points/payload/clear": {"post": {"tags": ["Points"], "summary": "Clear payload", "description": "Remove all payload for specified points", "operationId": "clear_payload", "requestBody": {"description": "clear payload on points", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PointsSelector"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to clear payload from", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen", "required": false, "schema": {"type": "boolean"}}, {"name": "ordering", "in": "query", "description": "define ordering guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/WriteOrdering"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/UpdateResult"}}}}}}}}}, "/collections/{collection_name}/points/batch": {"post": {"tags": ["Points"], "summary": "Batch update points", "description": "Apply a series of update operations for points, vectors and payloads", "operationId": "batch_update", "requestBody": {"description": "update operations", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOperations"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to apply operations on", "required": true, "schema": {"type": "string"}}, {"name": "wait", "in": "query", "description": "If true, wait for changes to actually happen", "required": false, "schema": {"type": "boolean"}}, {"name": "ordering", "in": "query", "description": "define ordering guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/WriteOrdering"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateResult"}}}}}}}}}}, "/collections/{collection_name}/points/scroll": {"post": {"tags": ["Points"], "summary": "Scroll points", "description": "Scroll request - paginate over all points which matches given filtering condition", "operationId": "scroll_points", "requestBody": {"description": "Pagination and filter parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScrollRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to retrieve from", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/ScrollResult"}}}}}}}}}, "/collections/{collection_name}/points/search": {"post": {"tags": ["Search"], "summary": "Search points", "description": "Retrieve closest points based on vector similarity and given filtering conditions", "operationId": "search_points", "requestBody": {"description": "Search request with optional filtering", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to search in", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/ScoredPoint"}}}}}}}}}}, "/collections/{collection_name}/points/search/batch": {"post": {"tags": ["Search"], "summary": "Search batch points", "description": "Retrieve by batch the closest points based on vector similarity and given filtering conditions", "operationId": "search_batch_points", "requestBody": {"description": "Search batch request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchRequestBatch"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to search in", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "array", "items": {"type": "array", "items": {"$ref": "#/components/schemas/ScoredPoint"}}}}}}}}}}}, "/collections/{collection_name}/points/search/groups": {"post": {"tags": ["Search"], "summary": "Search point groups", "description": "Retrieve closest points based on vector similarity and given filtering conditions, grouped by a given payload field", "operationId": "search_point_groups", "requestBody": {"description": "Search request with optional filtering, grouped by a given payload field", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchGroupsRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to search in", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/GroupsResult"}}}}}}}}}, "/collections/{collection_name}/points/recommend": {"post": {"tags": ["Search"], "summary": "Recommend points", "description": "Look for the points which are closer to stored positive examples and at the same time further to negative examples.", "operationId": "recommend_points", "requestBody": {"description": "Request points based on positive and negative examples.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to search in", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/ScoredPoint"}}}}}}}}}}, "/collections/{collection_name}/points/recommend/batch": {"post": {"tags": ["Search"], "summary": "Recommend batch points", "description": "Look for the points which are closer to stored positive examples and at the same time further to negative examples.", "operationId": "recommend_batch_points", "requestBody": {"description": "Request points based on positive and negative examples.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendRequestBatch"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to search in", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "array", "items": {"type": "array", "items": {"$ref": "#/components/schemas/ScoredPoint"}}}}}}}}}}}, "/collections/{collection_name}/points/recommend/groups": {"post": {"tags": ["Search"], "summary": "Recommend point groups", "description": "Look for the points which are closer to stored positive examples and at the same time further to negative examples, grouped by a given payload field.", "operationId": "recommend_point_groups", "requestBody": {"description": "Request points based on positive and negative examples, grouped by a payload field.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendGroupsRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to search in", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/GroupsResult"}}}}}}}}}, "/collections/{collection_name}/points/discover": {"post": {"tags": ["Search"], "summary": "Discover points", "description": "Use context and a target to find the most similar points to the target, constrained by the context.\nWhen using only the context (without a target), a special search - called context search - is performed where pairs of points are used to generate a loss that guides the search towards the zone where most positive examples overlap. This means that the score minimizes the scenario of finding a point closer to a negative than to a positive part of a pair.\nSince the score of a context relates to loss, the maximum score a point can get is 0.0, and it becomes normal that many points can have a score of 0.0.\nWhen using target (with or without context), the score behaves a little different: The  integer part of the score represents the rank with respect to the context, while the decimal part of the score relates to the distance to the target. The context part of the score for  each pair is calculated +1 if the point is closer to a positive than to a negative part of a pair,  and -1 otherwise.\n", "operationId": "discover_points", "requestBody": {"description": "Request points based on {positive, negative} pairs of examples, and/or a target", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DiscoverRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to search in", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/ScoredPoint"}}}}}}}}}}, "/collections/{collection_name}/points/discover/batch": {"post": {"tags": ["Search"], "summary": "Discover batch points", "description": "Look for points based on target and/or positive and negative example pairs, in batch.", "operationId": "discover_batch_points", "requestBody": {"description": "<PERSON><PERSON> request points based on { positive, negative } pairs of examples, and/or a target.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DiscoverRequestBatch"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to search in", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "array", "items": {"type": "array", "items": {"$ref": "#/components/schemas/ScoredPoint"}}}}}}}}}}}, "/collections/{collection_name}/points/count": {"post": {"tags": ["Points"], "summary": "Count points", "description": "Count points which matches given filtering condition", "operationId": "count_points", "requestBody": {"description": "Request counts of points which matches given filtering condition", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CountRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to count in", "required": true, "schema": {"type": "string"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/CountResult"}}}}}}}}}, "/collections/{collection_name}/facet": {"post": {"tags": ["Points"], "summary": "Facet a payload key with a given filter.", "description": "Count points that satisfy the given filter for each unique value of a payload key.", "operationId": "facet", "requestBody": {"description": "Request counts of points for each unique value of a payload key", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FacetRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to facet in", "required": true, "schema": {"type": "string"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/FacetResponse"}}}}}}}}}, "/collections/{collection_name}/points/query": {"post": {"tags": ["Search"], "summary": "Query points", "description": "Universally query points. This endpoint covers all capabilities of search, recommend, discover, filters. But also enables hybrid and multi-stage queries.", "operationId": "query_points", "requestBody": {"description": "Describes the query to make to the collection", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to query", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/QueryResponse"}}}}}}}}}, "/collections/{collection_name}/points/query/batch": {"post": {"tags": ["Search"], "summary": "Query points in batch", "description": "Universally query points in batch. This endpoint covers all capabilities of search, recommend, discover, filters. But also enables hybrid and multi-stage queries.", "operationId": "query_batch_points", "requestBody": {"description": "Describes the queries to make to the collection", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryRequestBatch"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to query", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/QueryResponse"}}}}}}}}}}, "/collections/{collection_name}/points/query/groups": {"post": {"tags": ["Search"], "summary": "Query points, grouped by a given payload field", "description": "Universally query points, grouped by a given payload field", "operationId": "query_points_groups", "requestBody": {"description": "Describes the query to make to the collection", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryGroupsRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to query", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/GroupsResult"}}}}}}}}}, "/collections/{collection_name}/points/search/matrix/pairs": {"post": {"tags": ["Search"], "summary": "Search points matrix distance pairs", "description": "Compute distance matrix for sampled points with a pair based output format", "operationId": "search_matrix_pairs", "requestBody": {"description": "Search matrix request with optional filtering", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchMatrixRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to search in", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/SearchMatrixPairsResponse"}}}}}}}}}, "/collections/{collection_name}/points/search/matrix/offsets": {"post": {"tags": ["Search"], "summary": "Search points matrix distance offsets", "description": "Compute distance matrix for sampled points with an offset based output format", "operationId": "search_matrix_offsets", "requestBody": {"description": "Search matrix request with optional filtering", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchMatrixRequest"}}}}, "parameters": [{"name": "collection_name", "in": "path", "description": "Name of the collection to search in", "required": true, "schema": {"type": "string"}}, {"name": "consistency", "in": "query", "description": "Define read consistency guarantees for the operation", "required": false, "schema": {"$ref": "#/components/schemas/ReadConsistency"}}, {"name": "timeout", "in": "query", "description": "If set, overrides global timeout for this request. Unit is seconds.", "required": false, "schema": {"type": "integer", "minimum": 1}}], "responses": {"default": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "4XX": {"description": "error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"usage": {"default": null, "anyOf": [{"$ref": "#/components/schemas/HardwareUsage"}, {"nullable": true}]}, "time": {"type": "number", "format": "float", "description": "Time spent to process this request", "example": 0.002}, "status": {"type": "string", "example": "ok"}, "result": {"$ref": "#/components/schemas/SearchMatrixOffsetsResponse"}}}}}}}}}}, "openapi": "3.0.1", "security": [{"api-key": []}, {"bearerAuth": []}, {}], "info": {"title": "Qdrant API", "description": "API description for Qdrant vector search engine.\n\nThis document describes CRUD and search operations on collections of points (vectors with payload).\n\nQdrant supports any combinations of `should`, `min_should`, `must` and `must_not` conditions, which makes it possible to use in applications when object could not be described solely by vector. It could be location features, availability flags, and other custom properties businesses should take into account.\n## Examples\nThis examples cover the most basic use-cases - collection creation and basic vector search.\n### Create collection\nFirst - let's create a collection with dot-production metric.\n```\ncurl -X PUT 'http://localhost:6333/collections/test_collection' \\\n  -H 'Content-Type: application/json' \\\n  --data-raw '{\n    \"vectors\": {\n      \"size\": 4,\n      \"distance\": \"Dot\"\n    }\n  }'\n\n```\nExpected response:\n```\n{\n    \"result\": true,\n    \"status\": \"ok\",\n    \"time\": 0.*********\n}\n```\nWe can ensure that collection was created:\n```\ncurl 'http://localhost:6333/collections/test_collection'\n```\nExpected response:\n```\n{\n  \"result\": {\n    \"status\": \"green\",\n    \"vectors_count\": 0,\n    \"segments_count\": 5,\n    \"disk_data_size\": 0,\n    \"ram_data_size\": 0,\n    \"config\": {\n      \"params\": {\n        \"vectors\": {\n          \"size\": 4,\n          \"distance\": \"Dot\"\n        }\n      },\n      \"hnsw_config\": {\n        \"m\": 16,\n        \"ef_construct\": 100,\n        \"full_scan_threshold\": 10000\n      },\n      \"optimizer_config\": {\n        \"deleted_threshold\": 0.2,\n        \"vacuum_min_vector_number\": 1000,\n        \"default_segment_number\": 2,\n        \"max_segment_size\": null,\n        \"memmap_threshold\": null,\n        \"indexing_threshold\": 20000,\n        \"flush_interval_sec\": 5,\n        \"max_optimization_threads\": null\n      },\n      \"wal_config\": {\n        \"wal_capacity_mb\": 32,\n        \"wal_segments_ahead\": 0\n      }\n    }\n  },\n  \"status\": \"ok\",\n  \"time\": 2.1199e-05\n}\n```\n\n### Add points\nLet's now add vectors with some payload:\n```\ncurl -L -X PUT 'http://localhost:6333/collections/test_collection/points?wait=true' \\ -H 'Content-Type: application/json' \\ --data-raw '{\n  \"points\": [\n    {\"id\": 1, \"vector\": [0.05, 0.61, 0.76, 0.74], \"payload\": {\"city\": \"Berlin\"}},\n    {\"id\": 2, \"vector\": [0.19, 0.81, 0.75, 0.11], \"payload\": {\"city\": [\"Berlin\", \"London\"] }},\n    {\"id\": 3, \"vector\": [0.36, 0.55, 0.47, 0.94], \"payload\": {\"city\": [\"Berlin\", \"Moscow\"] }},\n    {\"id\": 4, \"vector\": [0.18, 0.01, 0.85, 0.80], \"payload\": {\"city\": [\"London\", \"Moscow\"] }},\n    {\"id\": 5, \"vector\": [0.24, 0.18, 0.22, 0.44], \"payload\": {\"count\": [0]}},\n    {\"id\": 6, \"vector\": [0.35, 0.08, 0.11, 0.44]}\n  ]\n}'\n```\nExpected response:\n```\n{\n    \"result\": {\n        \"operation_id\": 0,\n        \"status\": \"completed\"\n    },\n    \"status\": \"ok\",\n    \"time\": 0.000206061\n}\n```\n### Search with filtering\nLet's start with a basic request:\n```\ncurl -L -X POST 'http://localhost:6333/collections/test_collection/points/search' \\ -H 'Content-Type: application/json' \\ --data-raw '{\n    \"vector\": [0.2,0.1,0.9,0.7],\n    \"top\": 3\n}'\n```\nExpected response:\n```\n{\n    \"result\": [\n        { \"id\": 4, \"score\": 1.362, \"payload\": null, \"version\": 0 },\n        { \"id\": 1, \"score\": 1.273, \"payload\": null, \"version\": 0 },\n        { \"id\": 3, \"score\": 1.208, \"payload\": null, \"version\": 0 }\n    ],\n    \"status\": \"ok\",\n    \"time\": 0.000055785\n}\n```\nBut result is different if we add a filter:\n```\ncurl -L -X POST 'http://localhost:6333/collections/test_collection/points/search' \\ -H 'Content-Type: application/json' \\ --data-raw '{\n    \"filter\": {\n        \"should\": [\n            {\n                \"key\": \"city\",\n                \"match\": {\n                    \"value\": \"London\"\n                }\n            }\n        ]\n    },\n    \"vector\": [0.2, 0.1, 0.9, 0.7],\n    \"top\": 3\n}'\n```\nExpected response:\n```\n{\n    \"result\": [\n        { \"id\": 4, \"score\": 1.362, \"payload\": null, \"version\": 0 },\n        { \"id\": 2, \"score\": 0.871, \"payload\": null, \"version\": 0 }\n    ],\n    \"status\": \"ok\",\n    \"time\": 0.000093972\n}\n```\n", "contact": {"email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "master"}, "externalDocs": {"description": "Find out more about Qdrant applications and demo", "url": "https://qdrant.tech/documentation/"}, "servers": [{"url": "{protocol}://{hostname}:{port}", "variables": {"protocol": {"enum": ["http", "https"], "default": "http"}, "hostname": {"default": "localhost"}, "port": {"default": "6333"}}}], "tags": [{"name": "Collections", "description": "Searchable collections of points."}, {"name": "Points", "description": "Float-point vectors with payload."}, {"name": "Search", "description": "Find points in a collection."}, {"name": "Aliases", "description": "Additional names for existing collections."}, {"name": "Indexes", "description": "Indexes for payloads associated with points."}, {"name": "Distributed", "description": "Service distributed setup."}, {"name": "Snapshots", "description": "Storage and collections snapshots."}, {"name": "Service", "description": "Qdrant service utilities."}, {"name": "Beta", "description": "Beta features, do not depend on these yet."}], "components": {"securitySchemes": {"api-key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "api-key", "description": "Authorization key, either read-write or read-only"}, "bearerAuth": {"type": "http", "scheme": "bearer"}}, "schemas": {"ErrorResponse": {"type": "object", "properties": {"time": {"type": "number", "format": "float", "description": "Time spent to process this request"}, "status": {"type": "object", "properties": {"error": {"type": "string", "description": "Description of the occurred error."}}}, "result": {"type": "object", "nullable": true}}}, "CollectionsResponse": {"type": "object", "required": ["collections"], "properties": {"collections": {"type": "array", "items": {"$ref": "#/components/schemas/CollectionDescription"}}}, "example": {"collections": [{"name": "arivx-title"}, {"name": "arivx-abstract"}, {"name": "medium-title"}, {"name": "medium-text"}]}}, "CollectionDescription": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}}, "CollectionInfo": {"description": "Current statistics and configuration of the collection", "type": "object", "required": ["config", "optimizer_status", "payload_schema", "segments_count", "status"], "properties": {"status": {"$ref": "#/components/schemas/CollectionStatus"}, "optimizer_status": {"$ref": "#/components/schemas/OptimizersStatus"}, "vectors_count": {"description": "DEPRECATED: Approximate number of vectors in collection. All vectors in collection are available for querying. Calculated as `points_count x vectors_per_point`. Where `vectors_per_point` is a number of named vectors in schema.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "indexed_vectors_count": {"description": "Approximate number of indexed vectors in the collection. Indexed vectors in large segments are faster to query, as it is stored in a specialized vector index.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "points_count": {"description": "Approximate number of points (vectors + payloads) in collection. Each point could be accessed by unique id.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "segments_count": {"description": "Number of segments in collection. Each segment has independent vector as payload indexes", "type": "integer", "format": "uint", "minimum": 0}, "config": {"$ref": "#/components/schemas/CollectionConfig"}, "payload_schema": {"description": "Types of stored payload", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/PayloadIndexInfo"}}}}, "CollectionStatus": {"description": "Current state of the collection. `Green` - all good. `Yellow` - optimization is running, 'Grey' - optimizations are possible but not triggered, `Red` - some operations failed and was not recovered", "type": "string", "enum": ["green", "yellow", "grey", "red"]}, "OptimizersStatus": {"description": "Current state of the collection", "oneOf": [{"description": "Optimizers are reporting as expected", "type": "string", "enum": ["ok"]}, {"description": "Something wrong happened with optimizers", "type": "object", "required": ["error"], "properties": {"error": {"type": "string"}}, "additionalProperties": false}]}, "CollectionConfig": {"description": "Information about the collection configuration", "type": "object", "required": ["hnsw_config", "optimizer_config", "params"], "properties": {"params": {"$ref": "#/components/schemas/CollectionParams"}, "hnsw_config": {"$ref": "#/components/schemas/HnswConfig"}, "optimizer_config": {"$ref": "#/components/schemas/OptimizersConfig"}, "wal_config": {"anyOf": [{"$ref": "#/components/schemas/WalConfig"}, {"nullable": true}]}, "quantization_config": {"default": null, "anyOf": [{"$ref": "#/components/schemas/QuantizationConfig"}, {"nullable": true}]}, "strict_mode_config": {"anyOf": [{"$ref": "#/components/schemas/StrictModeConfig"}, {"nullable": true}]}}}, "CollectionParams": {"type": "object", "properties": {"vectors": {"$ref": "#/components/schemas/VectorsConfig"}, "shard_number": {"description": "Number of shards the collection has", "default": 1, "type": "integer", "format": "uint32", "minimum": 1}, "sharding_method": {"description": "Sharding method Default is Auto - points are distributed across all available shards Custom - points are distributed across shards according to shard key", "anyOf": [{"$ref": "#/components/schemas/ShardingMethod"}, {"nullable": true}]}, "replication_factor": {"description": "Number of replicas for each shard", "default": 1, "type": "integer", "format": "uint32", "minimum": 1}, "write_consistency_factor": {"description": "Defines how many replicas should apply the operation for us to consider it successful. Increasing this number will make the collection more resilient to inconsistencies, but will also make it fail if not enough replicas are available. Does not have any performance impact.", "default": 1, "type": "integer", "format": "uint32", "minimum": 1}, "read_fan_out_factor": {"description": "Defines how many additional replicas should be processing read request at the same time. Default value is Auto, which means that fan-out will be determined automatically based on the busyness of the local replica. Having more than 0 might be useful to smooth latency spikes of individual nodes.", "type": "integer", "format": "uint32", "minimum": 0, "nullable": true}, "on_disk_payload": {"description": "If true - point's payload will not be stored in memory. It will be read from the disk every time it is requested. This setting saves RAM by (slightly) increasing the response time. Note: those payload values that are involved in filtering and are indexed - remain in RAM.\n\nDefault: true", "default": true, "type": "boolean"}, "sparse_vectors": {"description": "Configuration of the sparse vector storage", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/SparseVectorParams"}, "nullable": true}}}, "VectorsConfig": {"description": "Vector params separator for single and multiple vector modes Single mode:\n\n{ \"size\": 128, \"distance\": \"Cosine\" }\n\nor multiple mode:\n\n{ \"default\": { \"size\": 128, \"distance\": \"Cosine\" } }", "anyOf": [{"$ref": "#/components/schemas/VectorParams"}, {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/VectorParams"}}]}, "VectorParams": {"description": "Params of single vector data storage", "type": "object", "required": ["distance", "size"], "properties": {"size": {"description": "Size of a vectors used", "type": "integer", "format": "uint64", "minimum": 1}, "distance": {"$ref": "#/components/schemas/Distance"}, "hnsw_config": {"description": "Custom params for HNSW index. If none - values from collection configuration are used.", "anyOf": [{"$ref": "#/components/schemas/HnswConfigDiff"}, {"nullable": true}]}, "quantization_config": {"description": "Custom params for quantization. If none - values from collection configuration are used.", "anyOf": [{"$ref": "#/components/schemas/QuantizationConfig"}, {"nullable": true}]}, "on_disk": {"description": "If true, vectors are served from disk, improving RAM usage at the cost of latency Default: false", "type": "boolean", "nullable": true}, "datatype": {"description": "Defines which datatype should be used to represent vectors in the storage. Choosing different datatypes allows to optimize memory usage and performance vs accuracy.\n\n- For `float32` datatype - vectors are stored as single-precision floating point numbers, 4 bytes. - For `float16` datatype - vectors are stored as half-precision floating point numbers, 2 bytes. - For `uint8` datatype - vectors are stored as unsigned 8-bit integers, 1 byte. It expects vector elements to be in range `[0, 255]`.", "anyOf": [{"$ref": "#/components/schemas/Datatype"}, {"nullable": true}]}, "multivector_config": {"anyOf": [{"$ref": "#/components/schemas/MultiVectorConfig"}, {"nullable": true}]}}}, "Distance": {"description": "Type of internal tags, build from payload Distance function types used to compare vectors", "type": "string", "enum": ["<PERSON><PERSON>e", "<PERSON><PERSON><PERSON>", "Dot", "Manhattan"]}, "HnswConfigDiff": {"type": "object", "properties": {"m": {"description": "Number of edges per node in the index graph. Larger the value - more accurate the search, more space required.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "ef_construct": {"description": "Number of neighbours to consider during the index building. Larger the value - more accurate the search, more time required to build the index.", "type": "integer", "format": "uint", "minimum": 4, "nullable": true}, "full_scan_threshold": {"description": "Minimal size (in kilobytes) of vectors for additional payload-based indexing. If payload chunk is smaller than `full_scan_threshold_kb` additional indexing won't be used - in this case full-scan search should be preferred by query planner and additional indexing is not required. Note: 1Kb = 1 vector of size 256", "type": "integer", "format": "uint", "minimum": 10, "nullable": true}, "max_indexing_threads": {"description": "Number of parallel threads used for background index building. If 0 - automatically select from 8 to 16. Best to keep between 8 and 16 to prevent likelihood of building broken/inefficient HNSW graphs. On small CPUs, less threads are used.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "on_disk": {"description": "Store HNSW index on disk. If set to false, the index will be stored in RAM. Default: false", "type": "boolean", "nullable": true}, "payload_m": {"description": "Custom M param for additional payload-aware HNSW links. If not set, default M will be used.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}}}, "QuantizationConfig": {"anyOf": [{"$ref": "#/components/schemas/ScalarQuantization"}, {"$ref": "#/components/schemas/ProductQuantization"}, {"$ref": "#/components/schemas/BinaryQuantization"}]}, "ScalarQuantization": {"type": "object", "required": ["scalar"], "properties": {"scalar": {"$ref": "#/components/schemas/ScalarQuantizationConfig"}}}, "ScalarQuantizationConfig": {"type": "object", "required": ["type"], "properties": {"type": {"$ref": "#/components/schemas/ScalarType"}, "quantile": {"description": "Quantile for quantization. Expected value range in [0.5, 1.0]. If not set - use the whole range of values", "type": "number", "format": "float", "maximum": 1, "minimum": 0.5, "nullable": true}, "always_ram": {"description": "If true - quantized vectors always will be stored in RAM, ignoring the config of main storage", "type": "boolean", "nullable": true}}}, "ScalarType": {"type": "string", "enum": ["int8"]}, "ProductQuantization": {"type": "object", "required": ["product"], "properties": {"product": {"$ref": "#/components/schemas/ProductQuantizationConfig"}}}, "ProductQuantizationConfig": {"type": "object", "required": ["compression"], "properties": {"compression": {"$ref": "#/components/schemas/CompressionRatio"}, "always_ram": {"type": "boolean", "nullable": true}}}, "CompressionRatio": {"type": "string", "enum": ["x4", "x8", "x16", "x32", "x64"]}, "BinaryQuantization": {"type": "object", "required": ["binary"], "properties": {"binary": {"$ref": "#/components/schemas/BinaryQuantizationConfig"}}}, "BinaryQuantizationConfig": {"type": "object", "properties": {"always_ram": {"type": "boolean", "nullable": true}}}, "Datatype": {"type": "string", "enum": ["float32", "uint8", "float16"]}, "MultiVectorConfig": {"type": "object", "required": ["comparator"], "properties": {"comparator": {"$ref": "#/components/schemas/MultiVectorComparator"}}}, "MultiVectorComparator": {"type": "string", "enum": ["max_sim"]}, "ShardingMethod": {"type": "string", "enum": ["auto", "custom"]}, "SparseVectorParams": {"description": "Params of single sparse vector data storage", "type": "object", "properties": {"index": {"description": "Custom params for index. If none - values from collection configuration are used.", "anyOf": [{"$ref": "#/components/schemas/SparseIndexParams"}, {"nullable": true}]}, "modifier": {"description": "Configures addition value modifications for sparse vectors. Default: none", "anyOf": [{"$ref": "#/components/schemas/Modifier"}, {"nullable": true}]}}}, "SparseIndexParams": {"description": "Configuration for sparse inverted index.", "type": "object", "properties": {"full_scan_threshold": {"description": "We prefer a full scan search upto (excluding) this number of vectors.\n\nNote: this is number of vectors, not KiloBytes.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "on_disk": {"description": "Store index on disk. If set to false, the index will be stored in RAM. Default: false", "type": "boolean", "nullable": true}, "datatype": {"description": "Defines which datatype should be used for the index. Choosing different datatypes allows to optimize memory usage and performance vs accuracy.\n\n- For `float32` datatype - vectors are stored as single-precision floating point numbers, 4 bytes. - For `float16` datatype - vectors are stored as half-precision floating point numbers, 2 bytes. - For `uint8` datatype - vectors are quantized to unsigned 8-bit integers, 1 byte. Quantization to fit byte range `[0, 255]` happens during indexing automatically, so the actual vector data does not need to conform to this range.", "anyOf": [{"$ref": "#/components/schemas/Datatype"}, {"nullable": true}]}}}, "Modifier": {"description": "If used, include weight modification, which will be applied to sparse vectors at query time: None - no modification (default) Idf - inverse document frequency, based on statistics of the collection", "type": "string", "enum": ["none", "idf"]}, "HnswConfig": {"description": "Config of HNSW index", "type": "object", "required": ["ef_construct", "full_scan_threshold", "m"], "properties": {"m": {"description": "Number of edges per node in the index graph. Larger the value - more accurate the search, more space required.", "type": "integer", "format": "uint", "minimum": 0}, "ef_construct": {"description": "Number of neighbours to consider during the index building. Larger the value - more accurate the search, more time required to build index.", "type": "integer", "format": "uint", "minimum": 4}, "full_scan_threshold": {"description": "Minimal size (in KiloBytes) of vectors for additional payload-based indexing. If payload chunk is smaller than `full_scan_threshold_kb` additional indexing won't be used - in this case full-scan search should be preferred by query planner and additional indexing is not required. Note: 1Kb = 1 vector of size 256", "type": "integer", "format": "uint", "minimum": 0}, "max_indexing_threads": {"description": "Number of parallel threads used for background index building. If 0 - automatically select from 8 to 16. Best to keep between 8 and 16 to prevent likelihood of slow building or broken/inefficient HNSW graphs. On small CPUs, less threads are used.", "default": 0, "type": "integer", "format": "uint", "minimum": 0}, "on_disk": {"description": "Store HNSW index on disk. If set to false, index will be stored in RAM. Default: false", "type": "boolean", "nullable": true}, "payload_m": {"description": "Custom M param for hnsw graph built for payload index. If not set, default M will be used.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}}}, "OptimizersConfig": {"type": "object", "required": ["default_segment_number", "deleted_threshold", "flush_interval_sec", "vacuum_min_vector_number"], "properties": {"deleted_threshold": {"description": "The minimal fraction of deleted vectors in a segment, required to perform segment optimization", "type": "number", "format": "double", "maximum": 1, "minimum": 0}, "vacuum_min_vector_number": {"description": "The minimal number of vectors in a segment, required to perform segment optimization", "type": "integer", "format": "uint", "minimum": 100}, "default_segment_number": {"description": "Target amount of segments optimizer will try to keep. Real amount of segments may vary depending on multiple parameters: - Amount of stored points - Current write RPS\n\nIt is recommended to select default number of segments as a factor of the number of search threads, so that each segment would be handled evenly by one of the threads. If `default_segment_number = 0`, will be automatically selected by the number of available CPUs.", "type": "integer", "format": "uint", "minimum": 0}, "max_segment_size": {"description": "Do not create segments larger this size (in kilobytes). Large segments might require disproportionately long indexation times, therefore it makes sense to limit the size of segments.\n\nIf indexing speed is more important - make this parameter lower. If search speed is more important - make this parameter higher. Note: 1Kb = 1 vector of size 256 If not set, will be automatically selected considering the number of available CPUs.", "default": null, "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "memmap_threshold": {"description": "Maximum size (in kilobytes) of vectors to store in-memory per segment. Segments larger than this threshold will be stored as read-only memmapped file.\n\nMemmap storage is disabled by default, to enable it, set this threshold to a reasonable value.\n\nTo disable memmap storage, set this to `0`. Internally it will use the largest threshold possible.\n\nNote: 1Kb = 1 vector of size 256", "default": null, "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "indexing_threshold": {"description": "Maximum size (in kilobytes) of vectors allowed for plain index, exceeding this threshold will enable vector indexing\n\nDefault value is 20,000, based on <https://github.com/google-research/google-research/blob/master/scann/docs/algorithms.md>.\n\nTo disable vector indexing, set to `0`.\n\nNote: 1kB = 1 vector of size 256.", "default": null, "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "flush_interval_sec": {"description": "Minimum interval between forced flushes.", "type": "integer", "format": "uint64", "minimum": 0}, "max_optimization_threads": {"description": "Max number of threads (jobs) for running optimizations per shard. Note: each optimization job will also use `max_indexing_threads` threads by itself for index building. If null - have no limit and choose dynamically to saturate CPU. If 0 - no optimization threads, optimizations will be disabled.", "default": null, "type": "integer", "format": "uint", "minimum": 0, "nullable": true}}}, "WalConfig": {"type": "object", "required": ["wal_capacity_mb", "wal_segments_ahead"], "properties": {"wal_capacity_mb": {"description": "Size of a single WAL segment in MB", "type": "integer", "format": "uint", "minimum": 1}, "wal_segments_ahead": {"description": "Number of WAL segments to create ahead of actually used ones", "type": "integer", "format": "uint", "minimum": 0}}}, "StrictModeConfig": {"type": "object", "properties": {"enabled": {"description": "Whether strict mode is enabled for a collection or not.", "type": "boolean", "nullable": true}, "max_query_limit": {"description": "<PERSON> allowed `limit` parameter for all APIs that don't have their own max limit.", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "max_timeout": {"description": "<PERSON> allowed `timeout` parameter.", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "unindexed_filtering_retrieve": {"description": "Allow usage of unindexed fields in retrieval based (eg. search) filters.", "type": "boolean", "nullable": true}, "unindexed_filtering_update": {"description": "Allow usage of unindexed fields in filtered updates (eg. delete by payload).", "type": "boolean", "nullable": true}, "search_max_hnsw_ef": {"description": "Max HNSW value allowed in search parameters.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "search_allow_exact": {"description": "Whether exact search is allowed or not.", "type": "boolean", "nullable": true}, "search_max_oversampling": {"description": "Max oversampling value allowed in search.", "type": "number", "format": "double", "nullable": true}, "upsert_max_batchsize": {"description": "Max batchsize when upserting", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "max_collection_vector_size_bytes": {"description": "Max size of a collections vector storage in bytes, ignoring replicas.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "read_rate_limit": {"description": "Max number of read operations per minute per replica", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "write_rate_limit": {"description": "Max number of write operations per minute per replica", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "max_collection_payload_size_bytes": {"description": "Max size of a collections payload storage in bytes", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "filter_max_conditions": {"description": "Max conditions a filter can have.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "condition_max_size": {"description": "Max size of a condition, eg. items in `MatchAny`.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "multivector_config": {"description": "Multivector configuration", "anyOf": [{"$ref": "#/components/schemas/StrictModeMultivectorConfig"}, {"nullable": true}]}, "sparse_config": {"description": "Sparse vector configuration", "anyOf": [{"$ref": "#/components/schemas/StrictModeSparseConfig"}, {"nullable": true}]}}}, "StrictModeMultivectorConfig": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/StrictModeMultivector"}}, "StrictModeMultivector": {"type": "object", "properties": {"max_vectors": {"description": "Max number of vectors in a multivector", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}}}, "StrictModeSparseConfig": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/StrictModeSparse"}}, "StrictModeSparse": {"type": "object", "properties": {"max_length": {"description": "Max length of sparse vector", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}}}, "PayloadIndexInfo": {"description": "Display payload field type & index information", "type": "object", "required": ["data_type", "points"], "properties": {"data_type": {"$ref": "#/components/schemas/PayloadSchemaType"}, "params": {"anyOf": [{"$ref": "#/components/schemas/PayloadSchemaParams"}, {"nullable": true}]}, "points": {"description": "Number of points indexed with this index", "type": "integer", "format": "uint", "minimum": 0}}}, "PayloadSchemaType": {"description": "All possible names of payload types", "type": "string", "enum": ["keyword", "integer", "float", "geo", "text", "bool", "datetime", "uuid"]}, "PayloadSchemaParams": {"description": "Payload type with parameters", "anyOf": [{"$ref": "#/components/schemas/KeywordIndexParams"}, {"$ref": "#/components/schemas/IntegerIndexParams"}, {"$ref": "#/components/schemas/FloatIndexParams"}, {"$ref": "#/components/schemas/GeoIndexParams"}, {"$ref": "#/components/schemas/TextIndexParams"}, {"$ref": "#/components/schemas/BoolIndexParams"}, {"$ref": "#/components/schemas/DatetimeIndexParams"}, {"$ref": "#/components/schemas/UuidIndexParams"}]}, "KeywordIndexParams": {"type": "object", "required": ["type"], "properties": {"type": {"$ref": "#/components/schemas/KeywordIndexType"}, "is_tenant": {"description": "If true - used for tenant optimization. Default: false.", "type": "boolean", "nullable": true}, "on_disk": {"description": "If true, store the index on disk. Default: false.", "type": "boolean", "nullable": true}}}, "KeywordIndexType": {"type": "string", "enum": ["keyword"]}, "IntegerIndexParams": {"type": "object", "required": ["type"], "properties": {"type": {"$ref": "#/components/schemas/IntegerIndexType"}, "lookup": {"description": "If true - support direct lookups.", "type": "boolean", "nullable": true}, "range": {"description": "If true - support ranges filters.", "type": "boolean", "nullable": true}, "is_principal": {"description": "If true - use this key to organize storage of the collection data. This option assumes that this key will be used in majority of filtered requests.", "type": "boolean", "nullable": true}, "on_disk": {"description": "If true, store the index on disk. Default: false.", "type": "boolean", "nullable": true}}}, "IntegerIndexType": {"type": "string", "enum": ["integer"]}, "FloatIndexParams": {"type": "object", "required": ["type"], "properties": {"type": {"$ref": "#/components/schemas/FloatIndexType"}, "is_principal": {"description": "If true - use this key to organize storage of the collection data. This option assumes that this key will be used in majority of filtered requests.", "type": "boolean", "nullable": true}, "on_disk": {"description": "If true, store the index on disk. Default: false.", "type": "boolean", "nullable": true}}}, "FloatIndexType": {"type": "string", "enum": ["float"]}, "GeoIndexParams": {"type": "object", "required": ["type"], "properties": {"type": {"$ref": "#/components/schemas/GeoIndexType"}, "on_disk": {"description": "If true, store the index on disk. Default: false.", "type": "boolean", "nullable": true}}}, "GeoIndexType": {"type": "string", "enum": ["geo"]}, "TextIndexParams": {"type": "object", "required": ["type"], "properties": {"type": {"$ref": "#/components/schemas/TextIndexType"}, "tokenizer": {"$ref": "#/components/schemas/TokenizerType"}, "min_token_len": {"description": "Minimum characters to be tokenized.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "max_token_len": {"description": "Maximum characters to be tokenized.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "lowercase": {"description": "If true, lowercase all tokens. Default: true.", "type": "boolean", "nullable": true}, "on_disk": {"description": "If true, store the index on disk. Default: false.", "type": "boolean", "nullable": true}}}, "TextIndexType": {"type": "string", "enum": ["text"]}, "TokenizerType": {"type": "string", "enum": ["prefix", "whitespace", "word", "multilingual"]}, "BoolIndexParams": {"type": "object", "required": ["type"], "properties": {"type": {"$ref": "#/components/schemas/BoolIndexType"}, "on_disk": {"description": "If true, store the index on disk. Default: false.", "type": "boolean", "nullable": true}}}, "BoolIndexType": {"type": "string", "enum": ["bool"]}, "DatetimeIndexParams": {"type": "object", "required": ["type"], "properties": {"type": {"$ref": "#/components/schemas/DatetimeIndexType"}, "is_principal": {"description": "If true - use this key to organize storage of the collection data. This option assumes that this key will be used in majority of filtered requests.", "type": "boolean", "nullable": true}, "on_disk": {"description": "If true, store the index on disk. Default: false.", "type": "boolean", "nullable": true}}}, "DatetimeIndexType": {"type": "string", "enum": ["datetime"]}, "UuidIndexParams": {"type": "object", "required": ["type"], "properties": {"type": {"$ref": "#/components/schemas/UuidIndexType"}, "is_tenant": {"description": "If true - used for tenant optimization.", "type": "boolean", "nullable": true}, "on_disk": {"description": "If true, store the index on disk. Default: false.", "type": "boolean", "nullable": true}}}, "UuidIndexType": {"type": "string", "enum": ["uuid"]}, "PointRequest": {"type": "object", "required": ["ids"], "properties": {"shard_key": {"description": "Specify in which shards to look for the points, if not specified - look in all shards", "anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "ids": {"description": "Look for points with ids", "type": "array", "items": {"$ref": "#/components/schemas/ExtendedPointId"}}, "with_payload": {"description": "Select which payload to return with the response. Default is true.", "anyOf": [{"$ref": "#/components/schemas/WithPayloadInterface"}, {"nullable": true}]}, "with_vector": {"$ref": "#/components/schemas/WithVector"}}}, "ShardKeySelector": {"anyOf": [{"$ref": "#/components/schemas/Shard<PERSON>ey"}, {"type": "array", "items": {"$ref": "#/components/schemas/Shard<PERSON>ey"}}]}, "ShardKey": {"anyOf": [{"type": "string", "example": "region_1"}, {"type": "integer", "format": "uint64", "minimum": 0, "example": 12}]}, "ExtendedPointId": {"description": "Type, used for specifying point ID in user interface", "anyOf": [{"type": "integer", "format": "uint64", "minimum": 0, "example": 42}, {"type": "string", "format": "uuid", "example": "550e8400-e29b-41d4-a716-************"}]}, "WithPayloadInterface": {"description": "Options for specifying which payload to include or not", "anyOf": [{"description": "If `true` - return all payload, If `false` - do not return payload", "type": "boolean"}, {"description": "Specify which fields to return", "type": "array", "items": {"type": "string"}}, {"$ref": "#/components/schemas/PayloadSelector"}]}, "PayloadSelector": {"description": "Specifies how to treat payload selector", "anyOf": [{"$ref": "#/components/schemas/PayloadSelectorInclude"}, {"$ref": "#/components/schemas/PayloadSelectorExclude"}]}, "PayloadSelectorInclude": {"type": "object", "required": ["include"], "properties": {"include": {"description": "Only include this payload keys", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, "PayloadSelectorExclude": {"type": "object", "required": ["exclude"], "properties": {"exclude": {"description": "Exclude this fields from returning payload", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, "WithVector": {"description": "Options for specifying which vector to include", "anyOf": [{"description": "If `true` - return all vector, If `false` - do not return vector", "type": "boolean"}, {"description": "Specify which vector to return", "type": "array", "items": {"type": "string"}}]}, "Record": {"description": "Point data", "type": "object", "required": ["id"], "properties": {"id": {"$ref": "#/components/schemas/ExtendedPointId"}, "payload": {"description": "Payload - values assigned to the point", "anyOf": [{"$ref": "#/components/schemas/Payload"}, {"nullable": true}]}, "vector": {"description": "Vector of the point", "anyOf": [{"$ref": "#/components/schemas/VectorStructOutput"}, {"nullable": true}]}, "shard_key": {"description": "<PERSON><PERSON>", "anyOf": [{"$ref": "#/components/schemas/Shard<PERSON>ey"}, {"nullable": true}]}, "order_value": {"anyOf": [{"$ref": "#/components/schemas/OrderValue"}, {"nullable": true}]}}}, "Payload": {"type": "object", "additionalProperties": true, "example": {"city": "London", "color": "green"}}, "VectorStructOutput": {"description": "Vector data stored in Point", "anyOf": [{"type": "array", "items": {"type": "number", "format": "float"}, "example": [0.875, 0.140625, 0.897599995136261]}, {"type": "array", "items": {"type": "array", "items": {"type": "number", "format": "float"}}, "example": [[0.875, 0.140625, 0.11020000278949738], [0.7580000162124634, 0.28126001358032227, 0.9687100052833557], [0.6209999918937683, 0.42187801003456116, 0.9375]]}, {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/VectorOutput"}, "example": {"image-embeddings": [0.8730000257492065, 0.140625, 0.897599995136261]}}]}, "VectorOutput": {"description": "Vector Data stored in Point", "anyOf": [{"type": "array", "items": {"type": "number", "format": "float"}}, {"$ref": "#/components/schemas/SparseVector"}, {"type": "array", "items": {"type": "array", "items": {"type": "number", "format": "float"}}}]}, "SparseVector": {"description": "Sparse vector structure", "type": "object", "required": ["indices", "values"], "properties": {"indices": {"description": "Indices must be unique", "type": "array", "items": {"type": "integer", "format": "uint32", "minimum": 0}}, "values": {"description": "Values and indices must be the same length", "type": "array", "items": {"type": "number", "format": "float"}}}}, "OrderValue": {"anyOf": [{"type": "integer", "format": "int64", "example": 42}, {"type": "number", "format": "double", "example": 42.5}]}, "SearchRequest": {"description": "Search request. Holds all conditions and parameters for the search of most similar points by vector similarity given the filtering restrictions.", "type": "object", "required": ["limit", "vector"], "properties": {"shard_key": {"description": "Specify in which shards to look for the points, if not specified - look in all shards", "anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "vector": {"$ref": "#/components/schemas/NamedVectorStruct"}, "filter": {"description": "Look only for points which satisfies this conditions", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "params": {"description": "Additional search params", "anyOf": [{"$ref": "#/components/schemas/SearchParams"}, {"nullable": true}]}, "limit": {"description": "Max number of result to return", "type": "integer", "format": "uint", "minimum": 1}, "offset": {"description": "Offset of the first result to return. May be used to paginate results. Note: large offset values may cause performance issues.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "with_payload": {"description": "Select which payload to return with the response. Default is false.", "anyOf": [{"$ref": "#/components/schemas/WithPayloadInterface"}, {"nullable": true}]}, "with_vector": {"description": "Options for specifying which vectors to include into response. Default is false.", "default": null, "anyOf": [{"$ref": "#/components/schemas/WithVector"}, {"nullable": true}]}, "score_threshold": {"description": "Define a minimal score threshold for the result. If defined, less similar results will not be returned. Score of the returned result might be higher or smaller than the threshold depending on the Distance function used. E.g. for cosine similarity only higher scores will be returned.", "type": "number", "format": "float", "nullable": true}}}, "NamedVectorStruct": {"description": "Vector data separator for named and unnamed modes Unnamed mode:\n\n{ \"vector\": [1.0, 2.0, 3.0] }\n\nor named mode:\n\n{ \"vector\": { \"vector\": [1.0, 2.0, 3.0], \"name\": \"image-embeddings\" } }", "anyOf": [{"type": "array", "items": {"type": "number", "format": "float"}}, {"$ref": "#/components/schemas/NamedVector"}, {"$ref": "#/components/schemas/NamedSparseVector"}]}, "NamedVector": {"description": "Dense vector data with name", "type": "object", "required": ["name", "vector"], "properties": {"name": {"description": "Name of vector data", "type": "string"}, "vector": {"description": "Vector data", "type": "array", "items": {"type": "number", "format": "float"}}}}, "NamedSparseVector": {"description": "Sparse vector data with name", "type": "object", "required": ["name", "vector"], "properties": {"name": {"description": "Name of vector data", "type": "string"}, "vector": {"$ref": "#/components/schemas/SparseVector"}}}, "Filter": {"type": "object", "properties": {"should": {"description": "At least one of those conditions should match", "default": null, "anyOf": [{"$ref": "#/components/schemas/Condition"}, {"type": "array", "items": {"$ref": "#/components/schemas/Condition"}}, {"nullable": true}]}, "min_should": {"description": "At least minimum amount of given conditions should match", "anyOf": [{"$ref": "#/components/schemas/MinShould"}, {"nullable": true}]}, "must": {"description": "All conditions must match", "default": null, "anyOf": [{"$ref": "#/components/schemas/Condition"}, {"type": "array", "items": {"$ref": "#/components/schemas/Condition"}}, {"nullable": true}]}, "must_not": {"description": "All conditions must NOT match", "default": null, "anyOf": [{"$ref": "#/components/schemas/Condition"}, {"type": "array", "items": {"$ref": "#/components/schemas/Condition"}}, {"nullable": true}]}}, "additionalProperties": false}, "Condition": {"anyOf": [{"$ref": "#/components/schemas/FieldCondition"}, {"$ref": "#/components/schemas/IsEmptyCondition"}, {"$ref": "#/components/schemas/IsNullCondition"}, {"$ref": "#/components/schemas/HasIdCondition"}, {"$ref": "#/components/schemas/HasVectorCondition"}, {"$ref": "#/components/schemas/NestedCondition"}, {"$ref": "#/components/schemas/Filter"}]}, "FieldCondition": {"description": "All possible payload filtering conditions", "type": "object", "required": ["key"], "properties": {"key": {"description": "Payload key", "type": "string"}, "match": {"description": "Check if point has field with a given value", "anyOf": [{"$ref": "#/components/schemas/Match"}, {"nullable": true}]}, "range": {"description": "Check if points value lies in a given range", "anyOf": [{"$ref": "#/components/schemas/RangeInterface"}, {"nullable": true}]}, "geo_bounding_box": {"description": "Check if points geo location lies in a given area", "anyOf": [{"$ref": "#/components/schemas/GeoBoundingBox"}, {"nullable": true}]}, "geo_radius": {"description": "Check if geo point is within a given radius", "anyOf": [{"$ref": "#/components/schemas/GeoRadius"}, {"nullable": true}]}, "geo_polygon": {"description": "Check if geo point is within a given polygon", "anyOf": [{"$ref": "#/components/schemas/GeoPolygon"}, {"nullable": true}]}, "values_count": {"description": "Check number of values of the field", "anyOf": [{"$ref": "#/components/schemas/ValuesCount"}, {"nullable": true}]}}}, "Match": {"description": "Match filter request", "anyOf": [{"$ref": "#/components/schemas/MatchValue"}, {"$ref": "#/components/schemas/MatchText"}, {"$ref": "#/components/schemas/MatchAny"}, {"$ref": "#/components/schemas/MatchExcept"}]}, "MatchValue": {"description": "Exact match of the given value", "type": "object", "required": ["value"], "properties": {"value": {"$ref": "#/components/schemas/ValueVariants"}}}, "ValueVariants": {"anyOf": [{"type": "string"}, {"type": "integer", "format": "int64"}, {"type": "boolean"}]}, "MatchText": {"description": "Full-text match of the strings.", "type": "object", "required": ["text"], "properties": {"text": {"type": "string"}}}, "MatchAny": {"description": "Exact match on any of the given values", "type": "object", "required": ["any"], "properties": {"any": {"$ref": "#/components/schemas/AnyVariants"}}}, "AnyVariants": {"anyOf": [{"type": "array", "items": {"type": "string"}, "uniqueItems": true}, {"type": "array", "items": {"type": "integer", "format": "int64"}, "uniqueItems": true}]}, "MatchExcept": {"description": "Should have at least one value not matching the any given values", "type": "object", "required": ["except"], "properties": {"except": {"$ref": "#/components/schemas/AnyVariants"}}}, "RangeInterface": {"anyOf": [{"$ref": "#/components/schemas/Range"}, {"$ref": "#/components/schemas/DatetimeRange"}]}, "Range": {"description": "Range filter request", "type": "object", "properties": {"lt": {"description": "point.key < range.lt", "type": "number", "format": "double", "nullable": true}, "gt": {"description": "point.key > range.gt", "type": "number", "format": "double", "nullable": true}, "gte": {"description": "point.key >= range.gte", "type": "number", "format": "double", "nullable": true}, "lte": {"description": "point.key <= range.lte", "type": "number", "format": "double", "nullable": true}}}, "DatetimeRange": {"description": "Range filter request", "type": "object", "properties": {"lt": {"description": "point.key < range.lt", "type": "string", "format": "date-time", "nullable": true}, "gt": {"description": "point.key > range.gt", "type": "string", "format": "date-time", "nullable": true}, "gte": {"description": "point.key >= range.gte", "type": "string", "format": "date-time", "nullable": true}, "lte": {"description": "point.key <= range.lte", "type": "string", "format": "date-time", "nullable": true}}}, "GeoBoundingBox": {"description": "Geo filter request\n\nMatches coordinates inside the rectangle, described by coordinates of lop-left and bottom-right edges", "type": "object", "required": ["bottom_right", "top_left"], "properties": {"top_left": {"$ref": "#/components/schemas/GeoPoint"}, "bottom_right": {"$ref": "#/components/schemas/GeoPoint"}}}, "GeoPoint": {"description": "Geo point payload schema", "type": "object", "required": ["lat", "lon"], "properties": {"lon": {"type": "number", "format": "double"}, "lat": {"type": "number", "format": "double"}}}, "GeoRadius": {"description": "Geo filter request\n\nMatches coordinates inside the circle of `radius` and center with coordinates `center`", "type": "object", "required": ["center", "radius"], "properties": {"center": {"$ref": "#/components/schemas/GeoPoint"}, "radius": {"description": "Radius of the area in meters", "type": "number", "format": "double"}}}, "GeoPolygon": {"description": "Geo filter request\n\nMatches coordinates inside the polygon, defined by `exterior` and `interiors`", "type": "object", "required": ["exterior"], "properties": {"exterior": {"$ref": "#/components/schemas/GeoLineString"}, "interiors": {"description": "Interior lines (if present) bound holes within the surface each GeoLineString must consist of a minimum of 4 points, and the first and last points must be the same.", "type": "array", "items": {"$ref": "#/components/schemas/GeoLineString"}, "nullable": true}}}, "GeoLineString": {"description": "Ordered sequence of GeoPoints representing the line", "type": "object", "required": ["points"], "properties": {"points": {"type": "array", "items": {"$ref": "#/components/schemas/GeoPoint"}}}}, "ValuesCount": {"description": "Values count filter request", "type": "object", "properties": {"lt": {"description": "point.key.length() < values_count.lt", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "gt": {"description": "point.key.length() > values_count.gt", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "gte": {"description": "point.key.length() >= values_count.gte", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "lte": {"description": "point.key.length() <= values_count.lte", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}}}, "IsEmptyCondition": {"description": "Select points with empty payload for a specified field", "type": "object", "required": ["is_empty"], "properties": {"is_empty": {"$ref": "#/components/schemas/PayloadField"}}}, "PayloadField": {"description": "Payload field", "type": "object", "required": ["key"], "properties": {"key": {"description": "Payload field name", "type": "string"}}}, "IsNullCondition": {"description": "Select points with null payload for a specified field", "type": "object", "required": ["is_null"], "properties": {"is_null": {"$ref": "#/components/schemas/PayloadField"}}}, "HasIdCondition": {"description": "ID-based filtering condition", "type": "object", "required": ["has_id"], "properties": {"has_id": {"type": "array", "items": {"$ref": "#/components/schemas/ExtendedPointId"}, "uniqueItems": true}}}, "HasVectorCondition": {"description": "Filter points which have specific vector assigned", "type": "object", "required": ["has_vector"], "properties": {"has_vector": {"type": "string"}}}, "NestedCondition": {"type": "object", "required": ["nested"], "properties": {"nested": {"$ref": "#/components/schemas/Nested"}}}, "Nested": {"description": "Select points with payload for a specified nested field", "type": "object", "required": ["filter", "key"], "properties": {"key": {"type": "string"}, "filter": {"$ref": "#/components/schemas/Filter"}}}, "MinShould": {"type": "object", "required": ["conditions", "min_count"], "properties": {"conditions": {"type": "array", "items": {"$ref": "#/components/schemas/Condition"}}, "min_count": {"type": "integer", "format": "uint", "minimum": 0}}}, "SearchParams": {"description": "Additional parameters of the search", "type": "object", "properties": {"hnsw_ef": {"description": "Params relevant to HNSW index Size of the beam in a beam-search. Larger the value - more accurate the result, more time required for search.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "exact": {"description": "Search without approximation. If set to true, search may run long but with exact results.", "default": false, "type": "boolean"}, "quantization": {"description": "Quantization params", "default": null, "anyOf": [{"$ref": "#/components/schemas/QuantizationSearchParams"}, {"nullable": true}]}, "indexed_only": {"description": "If enabled, the engine will only perform search among indexed or small segments. Using this option prevents slow searches in case of delayed index, but does not guarantee that all uploaded vectors will be included in search results", "default": false, "type": "boolean"}}}, "QuantizationSearchParams": {"description": "Additional parameters of the search", "type": "object", "properties": {"ignore": {"description": "If true, quantized vectors are ignored. Default is false.", "default": false, "type": "boolean"}, "rescore": {"description": "If true, use original vectors to re-score top-k results. Might require more time in case if original vectors are stored on disk. If not set, qdrant decides automatically apply rescoring or not.", "default": null, "type": "boolean", "nullable": true}, "oversampling": {"description": "Oversampling factor for quantization. Default is 1.0.\n\nDefines how many extra vectors should be pre-selected using quantized index, and then re-scored using original vectors.\n\nFor example, if `oversampling` is 2.4 and `limit` is 100, then 240 vectors will be pre-selected using quantized index, and then top-100 will be returned after re-scoring.", "default": null, "type": "number", "format": "double", "minimum": 1, "nullable": true}}}, "ScoredPoint": {"description": "Search result", "type": "object", "required": ["id", "score", "version"], "properties": {"id": {"$ref": "#/components/schemas/ExtendedPointId"}, "version": {"description": "Point version", "type": "integer", "format": "uint64", "minimum": 0, "example": 3}, "score": {"description": "Points vector distance to the query vector", "type": "number", "format": "float", "example": 0.75}, "payload": {"description": "Payload - values assigned to the point", "anyOf": [{"$ref": "#/components/schemas/Payload"}, {"nullable": true}]}, "vector": {"description": "Vector of the point", "anyOf": [{"$ref": "#/components/schemas/VectorStructOutput"}, {"nullable": true}]}, "shard_key": {"description": "<PERSON><PERSON>", "anyOf": [{"$ref": "#/components/schemas/Shard<PERSON>ey"}, {"nullable": true}]}, "order_value": {"description": "Order-by value", "anyOf": [{"$ref": "#/components/schemas/OrderValue"}, {"nullable": true}]}}}, "UpdateResult": {"type": "object", "required": ["status"], "properties": {"operation_id": {"description": "Sequential number of the operation", "type": "integer", "format": "uint64", "minimum": 0, "nullable": true}, "status": {"$ref": "#/components/schemas/UpdateStatus"}}}, "UpdateStatus": {"description": "`Acknowledged` - Request is saved to WAL and will be process in a queue. `Completed` - Request is completed, changes are actual.", "type": "string", "enum": ["acknowledged", "completed"]}, "RecommendRequest": {"description": "Recommendation request. Provides positive and negative examples of the vectors, which can be ids of points that are already stored in the collection, raw vectors, or even ids and vectors combined.\n\nService should look for the points which are closer to positive examples and at the same time further to negative examples. The concrete way of how to compare negative and positive distances is up to the `strategy` chosen.", "type": "object", "required": ["limit"], "properties": {"shard_key": {"description": "Specify in which shards to look for the points, if not specified - look in all shards", "anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "positive": {"description": "Look for vectors closest to those", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/RecommendExample"}}, "negative": {"description": "Try to avoid vectors like this", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/RecommendExample"}}, "strategy": {"description": "How to use positive and negative examples to find the results", "anyOf": [{"$ref": "#/components/schemas/RecommendStrategy"}, {"nullable": true}]}, "filter": {"description": "Look only for points which satisfies this conditions", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "params": {"description": "Additional search params", "anyOf": [{"$ref": "#/components/schemas/SearchParams"}, {"nullable": true}]}, "limit": {"description": "Max number of result to return", "type": "integer", "format": "uint", "minimum": 1}, "offset": {"description": "Offset of the first result to return. May be used to paginate results. Note: large offset values may cause performance issues.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "with_payload": {"description": "Select which payload to return with the response. Default is false.", "anyOf": [{"$ref": "#/components/schemas/WithPayloadInterface"}, {"nullable": true}]}, "with_vector": {"description": "Options for specifying which vectors to include into response. Default is false.", "default": null, "anyOf": [{"$ref": "#/components/schemas/WithVector"}, {"nullable": true}]}, "score_threshold": {"description": "Define a minimal score threshold for the result. If defined, less similar results will not be returned. Score of the returned result might be higher or smaller than the threshold depending on the Distance function used. E.g. for cosine similarity only higher scores will be returned.", "type": "number", "format": "float", "nullable": true}, "using": {"description": "Define which vector to use for recommendation, if not specified - try to use default vector", "default": null, "anyOf": [{"$ref": "#/components/schemas/UsingVector"}, {"nullable": true}]}, "lookup_from": {"description": "The location used to lookup vectors. If not specified - use current collection. Note: the other collection should have the same vector size as the current collection", "default": null, "anyOf": [{"$ref": "#/components/schemas/LookupLocation"}, {"nullable": true}]}}}, "RecommendExample": {"anyOf": [{"$ref": "#/components/schemas/ExtendedPointId"}, {"type": "array", "items": {"type": "number", "format": "float"}}, {"$ref": "#/components/schemas/SparseVector"}]}, "RecommendStrategy": {"description": "How to use positive and negative examples to find the results, default is `average_vector`:\n\n* `average_vector` - Average positive and negative vectors and create a single query with the formula `query = avg_pos + avg_pos - avg_neg`. Then performs normal search.\n\n* `best_score` - Uses custom search objective. Each candidate is compared against all examples, its score is then chosen from the `max(max_pos_score, max_neg_score)`. If the `max_neg_score` is chosen then it is squared and negated, otherwise it is just the `max_pos_score`.", "type": "string", "enum": ["average_vector", "best_score"]}, "UsingVector": {"anyOf": [{"type": "string"}]}, "LookupLocation": {"description": "Defines a location to use for looking up the vector. Specifies collection and vector field name.", "type": "object", "required": ["collection"], "properties": {"collection": {"description": "Name of the collection used for lookup", "type": "string"}, "vector": {"description": "Optional name of the vector field within the collection. If not provided, the default vector field will be used.", "default": null, "type": "string", "nullable": true}, "shard_key": {"description": "Specify in which shards to look for the points, if not specified - look in all shards", "anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}}}, "ScrollRequest": {"description": "Scroll request - paginate over all points which matches given condition", "type": "object", "properties": {"shard_key": {"description": "Specify in which shards to look for the points, if not specified - look in all shards", "anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "offset": {"description": "Start ID to read points from.", "anyOf": [{"$ref": "#/components/schemas/ExtendedPointId"}, {"nullable": true}]}, "limit": {"description": "Page size. De<PERSON>ult: 10", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "filter": {"description": "Look only for points which satisfies this conditions. If not provided - all points.", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "with_payload": {"description": "Select which payload to return with the response. Default is true.", "anyOf": [{"$ref": "#/components/schemas/WithPayloadInterface"}, {"nullable": true}]}, "with_vector": {"$ref": "#/components/schemas/WithVector"}, "order_by": {"description": "Order the records by a payload field.", "anyOf": [{"$ref": "#/components/schemas/OrderByInterface"}, {"nullable": true}]}}}, "OrderByInterface": {"anyOf": [{"type": "string"}, {"$ref": "#/components/schemas/OrderBy"}]}, "OrderBy": {"type": "object", "required": ["key"], "properties": {"key": {"description": "Payload key to order by", "type": "string"}, "direction": {"description": "Direction of ordering: `asc` or `desc`. <PERSON><PERSON><PERSON> is ascending.", "anyOf": [{"$ref": "#/components/schemas/Direction"}, {"nullable": true}]}, "start_from": {"description": "Which payload value to start scrolling from. Default is the lowest value for `asc` and the highest for `desc`", "anyOf": [{"$ref": "#/components/schemas/StartFrom"}, {"nullable": true}]}}}, "Direction": {"type": "string", "enum": ["asc", "desc"]}, "StartFrom": {"anyOf": [{"type": "integer", "format": "int64"}, {"type": "number", "format": "double"}, {"type": "string", "format": "date-time"}]}, "ScrollResult": {"description": "Result of the points read request", "type": "object", "required": ["points"], "properties": {"points": {"description": "List of retrieved points", "type": "array", "items": {"$ref": "#/components/schemas/Record"}, "example": [{"id": 40, "payload": {"city": "London", "color": "green"}, "vector": [0.875, 0.140625, 0.897599995136261], "shard_key": "region_1"}, {"id": 41, "payload": {"city": "Paris", "color": "red"}, "vector": [0.75, 0.640625, 0.8945000171661377], "shard_key": "region_1"}]}, "next_page_offset": {"description": "Offset which should be used to retrieve a next page result", "anyOf": [{"$ref": "#/components/schemas/ExtendedPointId"}, {"nullable": true}]}}}, "CreateCollection": {"description": "Operation for creating new collection and (optionally) specify index params", "type": "object", "properties": {"vectors": {"$ref": "#/components/schemas/VectorsConfig"}, "shard_number": {"description": "For auto sharding: Number of shards in collection. - Default is 1 for standalone, otherwise equal to the number of nodes - Minimum is 1\n\nFor custom sharding: Number of shards in collection per shard group. - Default is 1, meaning that each shard key will be mapped to a single shard - Minimum is 1", "default": null, "type": "integer", "format": "uint32", "minimum": 1, "nullable": true}, "sharding_method": {"description": "Sharding method Default is Auto - points are distributed across all available shards Custom - points are distributed across shards according to shard key", "default": null, "anyOf": [{"$ref": "#/components/schemas/ShardingMethod"}, {"nullable": true}]}, "replication_factor": {"description": "Number of shards replicas. Default is 1 Minimum is 1", "default": null, "type": "integer", "format": "uint32", "minimum": 1, "nullable": true}, "write_consistency_factor": {"description": "Defines how many replicas should apply the operation for us to consider it successful. Increasing this number will make the collection more resilient to inconsistencies, but will also make it fail if not enough replicas are available. Does not have any performance impact.", "default": null, "type": "integer", "format": "uint32", "minimum": 1, "nullable": true}, "on_disk_payload": {"description": "If true - point's payload will not be stored in memory. It will be read from the disk every time it is requested. This setting saves RAM by (slightly) increasing the response time. Note: those payload values that are involved in filtering and are indexed - remain in RAM.\n\nDefault: true", "default": null, "type": "boolean", "nullable": true}, "hnsw_config": {"description": "Custom params for HNSW index. If none - values from service configuration file are used.", "anyOf": [{"$ref": "#/components/schemas/HnswConfigDiff"}, {"nullable": true}]}, "wal_config": {"description": "Custom params for WAL. If none - values from service configuration file are used.", "anyOf": [{"$ref": "#/components/schemas/WalConfigDiff"}, {"nullable": true}]}, "optimizers_config": {"description": "Custom params for Optimizers.  If none - values from service configuration file are used.", "anyOf": [{"$ref": "#/components/schemas/OptimizersConfigDiff"}, {"nullable": true}]}, "init_from": {"description": "Specify other collection to copy data from.", "default": null, "anyOf": [{"$ref": "#/components/schemas/InitFrom"}, {"nullable": true}]}, "quantization_config": {"description": "Quantization parameters. If none - quantization is disabled.", "default": null, "anyOf": [{"$ref": "#/components/schemas/QuantizationConfig"}, {"nullable": true}]}, "sparse_vectors": {"description": "Sparse vector data config.", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/SparseVectorParams"}, "nullable": true}, "strict_mode_config": {"description": "Strict-mode config.", "anyOf": [{"$ref": "#/components/schemas/StrictModeConfig"}, {"nullable": true}]}}}, "WalConfigDiff": {"type": "object", "properties": {"wal_capacity_mb": {"description": "Size of a single WAL segment in MB", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "wal_segments_ahead": {"description": "Number of WAL segments to create ahead of actually used ones", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}}}, "OptimizersConfigDiff": {"type": "object", "properties": {"deleted_threshold": {"description": "The minimal fraction of deleted vectors in a segment, required to perform segment optimization", "type": "number", "format": "double", "nullable": true}, "vacuum_min_vector_number": {"description": "The minimal number of vectors in a segment, required to perform segment optimization", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "default_segment_number": {"description": "Target amount of segments optimizer will try to keep. Real amount of segments may vary depending on multiple parameters: - Amount of stored points - Current write RPS\n\nIt is recommended to select default number of segments as a factor of the number of search threads, so that each segment would be handled evenly by one of the threads If `default_segment_number = 0`, will be automatically selected by the number of available CPUs", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "max_segment_size": {"description": "Do not create segments larger this size (in kilobytes). Large segments might require disproportionately long indexation times, therefore it makes sense to limit the size of segments.\n\nIf indexation speed have more priority for your - make this parameter lower. If search speed is more important - make this parameter higher. Note: 1Kb = 1 vector of size 256", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "memmap_threshold": {"description": "Maximum size (in kilobytes) of vectors to store in-memory per segment. Segments larger than this threshold will be stored as read-only memmapped file.\n\nMemmap storage is disabled by default, to enable it, set this threshold to a reasonable value.\n\nTo disable memmap storage, set this to `0`.\n\nNote: 1Kb = 1 vector of size 256", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "indexing_threshold": {"description": "Maximum size (in kilobytes) of vectors allowed for plain index, exceeding this threshold will enable vector indexing\n\nDefault value is 20,000, based on <https://github.com/google-research/google-research/blob/master/scann/docs/algorithms.md>.\n\nTo disable vector indexing, set to `0`.\n\nNote: 1kB = 1 vector of size 256.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "flush_interval_sec": {"description": "Minimum interval between forced flushes.", "type": "integer", "format": "uint64", "minimum": 0, "nullable": true}, "max_optimization_threads": {"description": "Max number of threads (jobs) for running optimizations per shard. Note: each optimization job will also use `max_indexing_threads` threads by itself for index building. If \"auto\" - have no limit and choose dynamically to saturate CPU. If 0 - no optimization threads, optimizations will be disabled.", "anyOf": [{"$ref": "#/components/schemas/MaxOptimizationThreads"}, {"nullable": true}]}}}, "MaxOptimizationThreads": {"anyOf": [{"$ref": "#/components/schemas/MaxOptimizationThreadsSetting"}, {"type": "integer", "format": "uint", "minimum": 0}]}, "MaxOptimizationThreadsSetting": {"type": "string", "enum": ["auto"]}, "InitFrom": {"description": "Operation for creating new collection and (optionally) specify index params", "type": "object", "required": ["collection"], "properties": {"collection": {"type": "string"}}}, "UpdateCollection": {"description": "Operation for updating parameters of the existing collection", "type": "object", "properties": {"vectors": {"description": "Map of vector data parameters to update for each named vector. To update parameters in a collection having a single unnamed vector, use an empty string as name.", "anyOf": [{"$ref": "#/components/schemas/VectorsConfigDiff"}, {"nullable": true}]}, "optimizers_config": {"description": "Custom params for Optimizers.  If none - it is left unchanged. This operation is blocking, it will only proceed once all current optimizations are complete", "anyOf": [{"$ref": "#/components/schemas/OptimizersConfigDiff"}, {"nullable": true}]}, "params": {"description": "Collection base params. If none - it is left unchanged.", "anyOf": [{"$ref": "#/components/schemas/CollectionParamsDiff"}, {"nullable": true}]}, "hnsw_config": {"description": "HNSW parameters to update for the collection index. If none - it is left unchanged.", "anyOf": [{"$ref": "#/components/schemas/HnswConfigDiff"}, {"nullable": true}]}, "quantization_config": {"description": "Quantization parameters to update. If none - it is left unchanged.", "default": null, "anyOf": [{"$ref": "#/components/schemas/QuantizationConfigDiff"}, {"nullable": true}]}, "sparse_vectors": {"description": "Map of sparse vector data parameters to update for each sparse vector.", "anyOf": [{"$ref": "#/components/schemas/SparseVectorsConfig"}, {"nullable": true}]}, "strict_mode_config": {"anyOf": [{"$ref": "#/components/schemas/StrictModeConfig"}, {"nullable": true}]}}}, "VectorsConfigDiff": {"description": "Vector update params for multiple vectors\n\n{ \"vector_name\": { \"hnsw_config\": { \"m\": 8 } } }", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/VectorParamsDiff"}}, "VectorParamsDiff": {"type": "object", "properties": {"hnsw_config": {"description": "Update params for HNSW index. If empty object - it will be unset.", "anyOf": [{"$ref": "#/components/schemas/HnswConfigDiff"}, {"nullable": true}]}, "quantization_config": {"description": "Update params for quantization. If none - it is left unchanged.", "anyOf": [{"$ref": "#/components/schemas/QuantizationConfigDiff"}, {"nullable": true}]}, "on_disk": {"description": "If true, vectors are served from disk, improving RAM usage at the cost of latency", "type": "boolean", "nullable": true}}}, "QuantizationConfigDiff": {"anyOf": [{"$ref": "#/components/schemas/ScalarQuantization"}, {"$ref": "#/components/schemas/ProductQuantization"}, {"$ref": "#/components/schemas/BinaryQuantization"}, {"$ref": "#/components/schemas/Disabled"}]}, "Disabled": {"type": "string", "enum": ["Disabled"]}, "CollectionParamsDiff": {"type": "object", "properties": {"replication_factor": {"description": "Number of replicas for each shard", "type": "integer", "format": "uint32", "minimum": 1, "nullable": true}, "write_consistency_factor": {"description": "Minimal number successful responses from replicas to consider operation successful", "type": "integer", "format": "uint32", "minimum": 1, "nullable": true}, "read_fan_out_factor": {"description": "Fan-out every read request to these many additional remote nodes (and return first available response)", "type": "integer", "format": "uint32", "minimum": 0, "nullable": true}, "on_disk_payload": {"description": "If true - point's payload will not be stored in memory. It will be read from the disk every time it is requested. This setting saves RAM by (slightly) increasing the response time. Note: those payload values that are involved in filtering and are indexed - remain in RAM.", "default": null, "type": "boolean", "nullable": true}}}, "SparseVectorsConfig": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/SparseVectorParams"}}, "ChangeAliasesOperation": {"description": "Operation for performing changes of collection aliases. Alias changes are atomic, meaning that no collection modifications can happen between alias operations.", "type": "object", "required": ["actions"], "properties": {"actions": {"type": "array", "items": {"$ref": "#/components/schemas/AliasOperations"}}}}, "AliasOperations": {"description": "Group of all the possible operations related to collection aliases", "anyOf": [{"$ref": "#/components/schemas/CreateAliasOperation"}, {"$ref": "#/components/schemas/DeleteAliasOperation"}, {"$ref": "#/components/schemas/RenameAliasOperation"}]}, "CreateAliasOperation": {"type": "object", "required": ["create_alias"], "properties": {"create_alias": {"$ref": "#/components/schemas/CreateAlias"}}}, "CreateAlias": {"description": "Create alternative name for a collection. Collection will be available under both names for search, retrieve,", "type": "object", "required": ["alias_name", "collection_name"], "properties": {"collection_name": {"type": "string"}, "alias_name": {"type": "string"}}}, "DeleteAliasOperation": {"description": "Delete alias if exists", "type": "object", "required": ["delete_alias"], "properties": {"delete_alias": {"$ref": "#/components/schemas/DeleteAlias"}}}, "DeleteAlias": {"description": "Delete alias if exists", "type": "object", "required": ["alias_name"], "properties": {"alias_name": {"type": "string"}}}, "RenameAliasOperation": {"description": "Change alias to a new one", "type": "object", "required": ["rename_alias"], "properties": {"rename_alias": {"$ref": "#/components/schemas/RenameAlias"}}}, "RenameAlias": {"description": "Change alias to a new one", "type": "object", "required": ["new_alias_name", "old_alias_name"], "properties": {"old_alias_name": {"type": "string"}, "new_alias_name": {"type": "string"}}}, "CreateFieldIndex": {"type": "object", "required": ["field_name"], "properties": {"field_name": {"type": "string"}, "field_schema": {"anyOf": [{"$ref": "#/components/schemas/PayloadFieldSchema"}, {"nullable": true}]}}}, "PayloadFieldSchema": {"anyOf": [{"$ref": "#/components/schemas/PayloadSchemaType"}, {"$ref": "#/components/schemas/PayloadSchemaParams"}]}, "PointsSelector": {"anyOf": [{"$ref": "#/components/schemas/PointIdsList"}, {"$ref": "#/components/schemas/FilterSelector"}]}, "PointIdsList": {"type": "object", "required": ["points"], "properties": {"points": {"type": "array", "items": {"$ref": "#/components/schemas/ExtendedPointId"}}, "shard_key": {"anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}}}, "FilterSelector": {"type": "object", "required": ["filter"], "properties": {"filter": {"$ref": "#/components/schemas/Filter"}, "shard_key": {"anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}}}, "PointInsertOperations": {"anyOf": [{"$ref": "#/components/schemas/PointsBatch"}, {"$ref": "#/components/schemas/PointsList"}]}, "PointsBatch": {"type": "object", "required": ["batch"], "properties": {"batch": {"$ref": "#/components/schemas/Batch"}, "shard_key": {"anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}}}, "Batch": {"type": "object", "required": ["ids", "vectors"], "properties": {"ids": {"type": "array", "items": {"$ref": "#/components/schemas/ExtendedPointId"}}, "vectors": {"$ref": "#/components/schemas/BatchVectorStruct"}, "payloads": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/Payload"}, {"nullable": true}]}, "nullable": true}}}, "BatchVectorStruct": {"anyOf": [{"type": "array", "items": {"type": "array", "items": {"type": "number", "format": "float"}}}, {"type": "array", "items": {"type": "array", "items": {"type": "array", "items": {"type": "number", "format": "float"}}}}, {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/Vector"}}}, {"type": "array", "items": {"$ref": "#/components/schemas/Document"}}, {"type": "array", "items": {"$ref": "#/components/schemas/Image"}}, {"type": "array", "items": {"$ref": "#/components/schemas/InferenceObject"}}]}, "Vector": {"description": "Vector Data Vectors can be described directly with values Or specified with source \"objects\" for inference", "anyOf": [{"type": "array", "items": {"type": "number", "format": "float"}}, {"$ref": "#/components/schemas/SparseVector"}, {"type": "array", "items": {"type": "array", "items": {"type": "number", "format": "float"}}}, {"$ref": "#/components/schemas/Document"}, {"$ref": "#/components/schemas/Image"}, {"$ref": "#/components/schemas/InferenceObject"}]}, "Document": {"description": "WARN: Work-in-progress, unimplemented\n\nText document for embedding. Requires inference infrastructure, unimplemented.", "type": "object", "required": ["model", "text"], "properties": {"text": {"description": "Text of the document This field will be used as input for the embedding model", "type": "string", "example": "This is a document text"}, "model": {"description": "Name of the model used to generate the vector List of available models depends on a provider", "type": "string", "minLength": 1, "example": "jinaai/jina-embeddings-v2-base-en"}, "options": {"description": "Parameters for the model Values of the parameters are model-specific", "type": "object", "additionalProperties": true, "nullable": true}}}, "Image": {"description": "WARN: Work-in-progress, unimplemented\n\nImage object for embedding. Requires inference infrastructure, unimplemented.", "type": "object", "required": ["image", "model"], "properties": {"image": {"description": "Image data: base64 encoded image or an URL", "example": "https://example.com/image.jpg"}, "model": {"description": "Name of the model used to generate the vector List of available models depends on a provider", "type": "string", "minLength": 1, "example": "Qdrant/clip-ViT-B-32-vision"}, "options": {"description": "Parameters for the model Values of the parameters are model-specific", "type": "object", "additionalProperties": true, "nullable": true}}}, "InferenceObject": {"description": "WARN: Work-in-progress, unimplemented\n\nCustom object for embedding. Requires inference infrastructure, unimplemented.", "type": "object", "required": ["model", "object"], "properties": {"object": {"description": "Arbitrary data, used as input for the embedding model Used if the model requires more than one input or a custom input"}, "model": {"description": "Name of the model used to generate the vector List of available models depends on a provider", "type": "string", "minLength": 1, "example": "jinaai/jina-embeddings-v2-base-en"}, "options": {"description": "Parameters for the model Values of the parameters are model-specific", "type": "object", "additionalProperties": true, "nullable": true}}}, "PointsList": {"type": "object", "required": ["points"], "properties": {"points": {"type": "array", "items": {"$ref": "#/components/schemas/PointStruct"}}, "shard_key": {"anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}}}, "PointStruct": {"type": "object", "required": ["id", "vector"], "properties": {"id": {"$ref": "#/components/schemas/ExtendedPointId"}, "vector": {"$ref": "#/components/schemas/VectorStruct"}, "payload": {"description": "Payload values (optional)", "anyOf": [{"$ref": "#/components/schemas/Payload"}, {"nullable": true}]}}}, "VectorStruct": {"description": "Full vector data per point separator with single and multiple vector modes", "anyOf": [{"type": "array", "items": {"type": "number", "format": "float"}, "example": [0.875, 0.140625, 0.897599995136261]}, {"type": "array", "items": {"type": "array", "items": {"type": "number", "format": "float"}}, "example": [[0.875, 0.140625, 0.11020000278949738], [0.7580000162124634, 0.28126001358032227, 0.9687100052833557], [0.6209999918937683, 0.42187801003456116, 0.9375]]}, {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Vector"}, "example": {"image-embeddings": [0.8730000257492065, 0.140625, 0.897599995136261]}}, {"$ref": "#/components/schemas/Document"}, {"$ref": "#/components/schemas/Image"}, {"$ref": "#/components/schemas/InferenceObject"}]}, "SetPayload": {"description": "This data structure is used in API interface and applied across multiple shards", "type": "object", "required": ["payload"], "properties": {"payload": {"$ref": "#/components/schemas/Payload"}, "points": {"description": "Assigns payload to each point in this list", "type": "array", "items": {"$ref": "#/components/schemas/ExtendedPointId"}, "nullable": true}, "filter": {"description": "Assigns payload to each point that satisfy this filter condition", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "shard_key": {"anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "key": {"description": "Assigns payload to each point that satisfy this path of property", "type": "string", "nullable": true}}}, "DeletePayload": {"description": "This data structure is used in API interface and applied across multiple shards", "type": "object", "required": ["keys"], "properties": {"keys": {"description": "List of payload keys to remove from payload", "type": "array", "items": {"type": "string"}}, "points": {"description": "Deletes values from each point in this list", "type": "array", "items": {"$ref": "#/components/schemas/ExtendedPointId"}, "nullable": true}, "filter": {"description": "Deletes values from points that satisfy this filter condition", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "shard_key": {"anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}}}, "ClusterStatus": {"description": "Information about current cluster status and structure", "oneOf": [{"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["disabled"]}}}, {"description": "Description of enabled cluster", "type": "object", "required": ["consensus_thread_status", "message_send_failures", "peer_id", "peers", "raft_info", "status"], "properties": {"status": {"type": "string", "enum": ["enabled"]}, "peer_id": {"description": "ID of this peer", "type": "integer", "format": "uint64", "minimum": 0}, "peers": {"description": "Peers composition of the cluster with main information", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/PeerInfo"}}, "raft_info": {"$ref": "#/components/schemas/RaftInfo"}, "consensus_thread_status": {"$ref": "#/components/schemas/ConsensusThreadStatus"}, "message_send_failures": {"description": "Consequent failures of message send operations in consensus by peer address. On the first success to send to that peer - entry is removed from this hashmap.", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/MessageSendErrors"}}}}]}, "PeerInfo": {"description": "Information of a peer in the cluster", "type": "object", "required": ["uri"], "properties": {"uri": {"type": "string"}}}, "RaftInfo": {"description": "Summary information about the current raft state", "type": "object", "required": ["commit", "is_voter", "pending_operations", "term"], "properties": {"term": {"description": "Raft divides time into terms of arbitrary length, each beginning with an election. If a candidate wins the election, it remains the leader for the rest of the term. The term number increases monotonically. Each server stores the current term number which is also exchanged in every communication.", "type": "integer", "format": "uint64", "minimum": 0}, "commit": {"description": "The index of the latest committed (finalized) operation that this peer is aware of.", "type": "integer", "format": "uint64", "minimum": 0}, "pending_operations": {"description": "Number of consensus operations pending to be applied on this peer", "type": "integer", "format": "uint", "minimum": 0}, "leader": {"description": "Leader of the current term", "type": "integer", "format": "uint64", "minimum": 0, "nullable": true}, "role": {"description": "Role of this peer in the current term", "anyOf": [{"$ref": "#/components/schemas/StateRole"}, {"nullable": true}]}, "is_voter": {"description": "Is this peer a voter or a learner", "type": "boolean"}}}, "StateRole": {"description": "Role of the peer in the consensus", "type": "string", "enum": ["Follower", "Candidate", "Leader", "PreCandidate"]}, "ConsensusThreadStatus": {"description": "Information about current consensus thread status", "oneOf": [{"type": "object", "required": ["consensus_thread_status", "last_update"], "properties": {"consensus_thread_status": {"type": "string", "enum": ["working"]}, "last_update": {"type": "string", "format": "date-time"}}}, {"type": "object", "required": ["consensus_thread_status"], "properties": {"consensus_thread_status": {"type": "string", "enum": ["stopped"]}}}, {"type": "object", "required": ["consensus_thread_status", "err"], "properties": {"consensus_thread_status": {"type": "string", "enum": ["stopped_with_err"]}, "err": {"type": "string"}}}]}, "MessageSendErrors": {"description": "Message send failures for a particular peer", "type": "object", "required": ["count"], "properties": {"count": {"type": "integer", "format": "uint", "minimum": 0}, "latest_error": {"type": "string", "nullable": true}, "latest_error_timestamp": {"description": "Timestamp of the latest error", "type": "string", "format": "date-time", "nullable": true}}}, "SnapshotDescription": {"type": "object", "required": ["name", "size"], "properties": {"name": {"type": "string"}, "creation_time": {"type": "string", "format": "partial-date-time", "nullable": true}, "size": {"type": "integer", "format": "uint64", "minimum": 0}, "checksum": {"type": "string", "nullable": true}}, "example": {"name": "my-collection-3766212330831337-2024-07-22-08-31-55.snapshot", "creation_time": "2022-08-04T10:49:10", "size": 1000000, "checksum": "a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0"}}, "CountRequest": {"description": "Count Request Counts the number of points which satisfy the given filter. If filter is not provided, the count of all points in the collection will be returned.", "type": "object", "properties": {"shard_key": {"description": "Specify in which shards to look for the points, if not specified - look in all shards", "anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "filter": {"description": "Look only for points which satisfies this conditions", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "exact": {"description": "If true, count exact number of points. If false, count approximate number of points faster. Approximate count might be unreliable during the indexing process. Default: true", "default": true, "type": "boolean"}}}, "CountResult": {"type": "object", "required": ["count"], "properties": {"count": {"description": "Number of points which satisfy the conditions", "type": "integer", "format": "uint", "minimum": 0}}}, "CollectionClusterInfo": {"description": "Current clustering distribution for the collection", "type": "object", "required": ["local_shards", "peer_id", "remote_shards", "shard_count", "shard_transfers"], "properties": {"peer_id": {"description": "ID of this peer", "type": "integer", "format": "uint64", "minimum": 0}, "shard_count": {"description": "Total number of shards", "type": "integer", "format": "uint", "minimum": 0}, "local_shards": {"description": "Local shards", "type": "array", "items": {"$ref": "#/components/schemas/LocalShardInfo"}}, "remote_shards": {"description": "Remote shards", "type": "array", "items": {"$ref": "#/components/schemas/RemoteShardInfo"}}, "shard_transfers": {"description": "Shard transfers", "type": "array", "items": {"$ref": "#/components/schemas/ShardTransferInfo"}}, "resharding_operations": {"description": "Resharding operations", "type": "array", "items": {"$ref": "#/components/schemas/ReshardingInfo"}, "nullable": true}}}, "LocalShardInfo": {"type": "object", "required": ["points_count", "shard_id", "state"], "properties": {"shard_id": {"description": "Local shard id", "type": "integer", "format": "uint32", "minimum": 0}, "shard_key": {"description": "User-defined sharding key", "anyOf": [{"$ref": "#/components/schemas/Shard<PERSON>ey"}, {"nullable": true}]}, "points_count": {"description": "Number of points in the shard", "type": "integer", "format": "uint", "minimum": 0}, "state": {"$ref": "#/components/schemas/ReplicaState"}}}, "ReplicaState": {"description": "State of the single shard within a replica set.", "type": "string", "enum": ["Active", "Dead", "Partial", "Initializing", "Listener", "PartialSnapshot", "Recovery", "Resharding", "ReshardingScaleDown"]}, "RemoteShardInfo": {"type": "object", "required": ["peer_id", "shard_id", "state"], "properties": {"shard_id": {"description": "Remote shard id", "type": "integer", "format": "uint32", "minimum": 0}, "shard_key": {"description": "User-defined sharding key", "anyOf": [{"$ref": "#/components/schemas/Shard<PERSON>ey"}, {"nullable": true}]}, "peer_id": {"description": "Remote peer id", "type": "integer", "format": "uint64", "minimum": 0}, "state": {"$ref": "#/components/schemas/ReplicaState"}}}, "ShardTransferInfo": {"type": "object", "required": ["from", "shard_id", "sync", "to"], "properties": {"shard_id": {"type": "integer", "format": "uint32", "minimum": 0}, "to_shard_id": {"description": "Target shard ID if different than source shard ID\n\nUsed exclusively with `ReshardStreamRecords` transfer method.", "type": "integer", "format": "uint32", "minimum": 0, "nullable": true}, "from": {"description": "Source peer id", "type": "integer", "format": "uint64", "minimum": 0}, "to": {"description": "Destination peer id", "type": "integer", "format": "uint64", "minimum": 0}, "sync": {"description": "If `true` transfer is a synchronization of a replicas If `false` transfer is a moving of a shard from one peer to another", "type": "boolean"}, "method": {"anyOf": [{"$ref": "#/components/schemas/ShardTransferMethod"}, {"nullable": true}]}, "comment": {"description": "A human-readable report of the transfer progress. Available only on the source peer.", "type": "string", "nullable": true}}}, "ShardTransferMethod": {"description": "Methods for transferring a shard from one node to another.", "oneOf": [{"description": "Stream all shard records in batches until the whole shard is transferred.", "type": "string", "enum": ["stream_records"]}, {"description": "Snapshot the shard, transfer and restore it on the receiver.", "type": "string", "enum": ["snapshot"]}, {"description": "Attempt to transfer shard difference by WAL delta.", "type": "string", "enum": ["wal_delta"]}, {"description": "Shard transfer for resharding: stream all records in batches until all points are transferred.", "type": "string", "enum": ["resharding_stream_records"]}]}, "ReshardingInfo": {"type": "object", "required": ["direction", "peer_id", "shard_id"], "properties": {"direction": {"$ref": "#/components/schemas/ReshardingDirection"}, "shard_id": {"type": "integer", "format": "uint32", "minimum": 0}, "peer_id": {"type": "integer", "format": "uint64", "minimum": 0}, "shard_key": {"anyOf": [{"$ref": "#/components/schemas/Shard<PERSON>ey"}, {"nullable": true}]}}}, "ReshardingDirection": {"description": "Resharding direction, scale up or down in number of shards", "oneOf": [{"description": "Scale up, add a new shard", "type": "string", "enum": ["up"]}, {"description": "Scale down, remove a shard", "type": "string", "enum": ["down"]}]}, "TelemetryData": {"type": "object", "required": ["app", "collections", "id"], "properties": {"id": {"type": "string"}, "app": {"$ref": "#/components/schemas/AppBuildTelemetry"}, "collections": {"$ref": "#/components/schemas/CollectionsTelemetry"}, "cluster": {"anyOf": [{"$ref": "#/components/schemas/ClusterTelemetry"}, {"nullable": true}]}, "requests": {"anyOf": [{"$ref": "#/components/schemas/RequestsTelemetry"}, {"nullable": true}]}, "memory": {"anyOf": [{"$ref": "#/components/schemas/MemoryTelemetry"}, {"nullable": true}]}, "hardware": {"anyOf": [{"$ref": "#/components/schemas/HardwareTelemetry"}, {"nullable": true}]}}}, "AppBuildTelemetry": {"type": "object", "required": ["name", "startup", "version"], "properties": {"name": {"type": "string"}, "version": {"type": "string"}, "features": {"anyOf": [{"$ref": "#/components/schemas/AppFeaturesTelemetry"}, {"nullable": true}]}, "system": {"anyOf": [{"$ref": "#/components/schemas/RunningEnvironmentTelemetry"}, {"nullable": true}]}, "jwt_rbac": {"type": "boolean", "nullable": true}, "hide_jwt_dashboard": {"type": "boolean", "nullable": true}, "startup": {"type": "string", "format": "date-time"}}}, "AppFeaturesTelemetry": {"type": "object", "required": ["debug", "gpu", "recovery_mode", "service_debug_feature", "web_feature"], "properties": {"debug": {"type": "boolean"}, "web_feature": {"type": "boolean"}, "service_debug_feature": {"type": "boolean"}, "recovery_mode": {"type": "boolean"}, "gpu": {"type": "boolean"}}}, "RunningEnvironmentTelemetry": {"type": "object", "required": ["cpu_flags", "is_docker"], "properties": {"distribution": {"type": "string", "nullable": true}, "distribution_version": {"type": "string", "nullable": true}, "is_docker": {"type": "boolean"}, "cores": {"type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "ram_size": {"type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "disk_size": {"type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "cpu_flags": {"type": "string"}, "cpu_endian": {"anyOf": [{"$ref": "#/components/schemas/CpuEndian"}, {"nullable": true}]}, "gpu_devices": {"type": "array", "items": {"$ref": "#/components/schemas/GpuDeviceTelemetry"}, "nullable": true}}}, "CpuEndian": {"type": "string", "enum": ["little", "big", "other"]}, "GpuDeviceTelemetry": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}}, "CollectionsTelemetry": {"type": "object", "required": ["number_of_collections"], "properties": {"number_of_collections": {"type": "integer", "format": "uint", "minimum": 0}, "collections": {"type": "array", "items": {"$ref": "#/components/schemas/CollectionTelemetryEnum"}, "nullable": true}}}, "CollectionTelemetryEnum": {"anyOf": [{"$ref": "#/components/schemas/CollectionTelemetry"}, {"$ref": "#/components/schemas/CollectionsAggregatedTelemetry"}]}, "CollectionTelemetry": {"type": "object", "required": ["config", "id", "init_time_ms", "resharding", "shard_clean_tasks", "shards", "transfers"], "properties": {"id": {"type": "string"}, "init_time_ms": {"type": "integer", "format": "uint64", "minimum": 0}, "config": {"$ref": "#/components/schemas/CollectionConfigInternal"}, "shards": {"type": "array", "items": {"$ref": "#/components/schemas/ReplicaSetTelemetry"}}, "transfers": {"type": "array", "items": {"$ref": "#/components/schemas/ShardTransferInfo"}}, "resharding": {"type": "array", "items": {"$ref": "#/components/schemas/ReshardingInfo"}}, "shard_clean_tasks": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ShardCleanStatusTelemetry"}}}}, "CollectionConfigInternal": {"type": "object", "required": ["hnsw_config", "optimizer_config", "params", "wal_config"], "properties": {"params": {"$ref": "#/components/schemas/CollectionParams"}, "hnsw_config": {"$ref": "#/components/schemas/HnswConfig"}, "optimizer_config": {"$ref": "#/components/schemas/OptimizersConfig"}, "wal_config": {"$ref": "#/components/schemas/WalConfig"}, "quantization_config": {"default": null, "anyOf": [{"$ref": "#/components/schemas/QuantizationConfig"}, {"nullable": true}]}, "strict_mode_config": {"anyOf": [{"$ref": "#/components/schemas/StrictModeConfig"}, {"nullable": true}]}, "uuid": {"default": null, "type": "string", "format": "uuid", "nullable": true}}}, "ReplicaSetTelemetry": {"type": "object", "required": ["id", "remote", "replicate_states"], "properties": {"id": {"type": "integer", "format": "uint32", "minimum": 0}, "key": {"anyOf": [{"$ref": "#/components/schemas/Shard<PERSON>ey"}, {"nullable": true}]}, "local": {"anyOf": [{"$ref": "#/components/schemas/LocalShardTelemetry"}, {"nullable": true}]}, "remote": {"type": "array", "items": {"$ref": "#/components/schemas/RemoteShardTelemetry"}}, "replicate_states": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ReplicaState"}}}}, "LocalShardTelemetry": {"type": "object", "required": ["optimizations", "segments", "total_optimized_points"], "properties": {"variant_name": {"type": "string", "nullable": true}, "status": {"anyOf": [{"$ref": "#/components/schemas/ShardStatus"}, {"nullable": true}]}, "total_optimized_points": {"description": "Total number of optimized points since the last start.", "type": "integer", "format": "uint", "minimum": 0}, "segments": {"type": "array", "items": {"$ref": "#/components/schemas/SegmentTelemetry"}}, "optimizations": {"$ref": "#/components/schemas/OptimizerTelemetry"}, "async_scorer": {"type": "boolean", "nullable": true}}}, "ShardStatus": {"description": "Current state of the shard (supports same states as the collection)\n\n`Green` - all good. `Yellow` - optimization is running, 'Grey' - optimizations are possible but not triggered, `Red` - some operations failed and was not recovered", "type": "string", "enum": ["green", "yellow", "grey", "red"]}, "SegmentTelemetry": {"type": "object", "required": ["config", "info", "payload_field_indices", "vector_index_searches"], "properties": {"info": {"$ref": "#/components/schemas/SegmentInfo"}, "config": {"$ref": "#/components/schemas/SegmentConfig"}, "vector_index_searches": {"type": "array", "items": {"$ref": "#/components/schemas/VectorIndexSearchesTelemetry"}}, "payload_field_indices": {"type": "array", "items": {"$ref": "#/components/schemas/PayloadIndexTelemetry"}}}}, "SegmentInfo": {"description": "Aggregated information about segment", "type": "object", "required": ["disk_usage_bytes", "index_schema", "is_appendable", "num_deleted_vectors", "num_indexed_vectors", "num_points", "num_vectors", "payloads_size_bytes", "ram_usage_bytes", "segment_type", "vector_data", "vectors_size_bytes"], "properties": {"segment_type": {"$ref": "#/components/schemas/SegmentType"}, "num_vectors": {"type": "integer", "format": "uint", "minimum": 0}, "num_points": {"type": "integer", "format": "uint", "minimum": 0}, "num_indexed_vectors": {"type": "integer", "format": "uint", "minimum": 0}, "num_deleted_vectors": {"type": "integer", "format": "uint", "minimum": 0}, "vectors_size_bytes": {"description": "An ESTIMATION of effective amount of bytes used for vectors Do NOT rely on this number unless you know what you are doing", "type": "integer", "format": "uint", "minimum": 0}, "payloads_size_bytes": {"description": "An estimation of the effective amount of bytes used for payloads", "type": "integer", "format": "uint", "minimum": 0}, "ram_usage_bytes": {"type": "integer", "format": "uint", "minimum": 0}, "disk_usage_bytes": {"type": "integer", "format": "uint", "minimum": 0}, "is_appendable": {"type": "boolean"}, "index_schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/PayloadIndexInfo"}}, "vector_data": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/VectorDataInfo"}}}}, "SegmentType": {"description": "Type of segment", "type": "string", "enum": ["plain", "indexed", "special"]}, "VectorDataInfo": {"type": "object", "required": ["num_deleted_vectors", "num_indexed_vectors", "num_vectors"], "properties": {"num_vectors": {"type": "integer", "format": "uint", "minimum": 0}, "num_indexed_vectors": {"type": "integer", "format": "uint", "minimum": 0}, "num_deleted_vectors": {"type": "integer", "format": "uint", "minimum": 0}}}, "SegmentConfig": {"type": "object", "required": ["payload_storage_type"], "properties": {"vector_data": {"default": {}, "type": "object", "additionalProperties": {"$ref": "#/components/schemas/VectorDataConfig"}}, "sparse_vector_data": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/SparseVectorDataConfig"}}, "payload_storage_type": {"$ref": "#/components/schemas/PayloadStorageType"}}}, "VectorDataConfig": {"description": "Config of single vector data storage", "type": "object", "required": ["distance", "index", "size", "storage_type"], "properties": {"size": {"description": "Size/dimensionality of the vectors used", "type": "integer", "format": "uint", "minimum": 0}, "distance": {"$ref": "#/components/schemas/Distance"}, "storage_type": {"$ref": "#/components/schemas/VectorStorageType"}, "index": {"$ref": "#/components/schemas/Indexes"}, "quantization_config": {"description": "Vector specific quantization config that overrides collection config", "anyOf": [{"$ref": "#/components/schemas/QuantizationConfig"}, {"nullable": true}]}, "multivector_config": {"description": "Vector specific configuration to enable multiple vectors per point", "anyOf": [{"$ref": "#/components/schemas/MultiVectorConfig"}, {"nullable": true}]}, "datatype": {"description": "Vector specific configuration to set specific storage element type", "anyOf": [{"$ref": "#/components/schemas/VectorStorageDatatype"}, {"nullable": true}]}}}, "VectorStorageType": {"description": "Storage types for vectors", "oneOf": [{"description": "Storage in memory (RAM)\n\nWill be very fast at the cost of consuming a lot of memory.", "type": "string", "enum": ["Memory"]}, {"description": "Storage in mmap file, not appendable\n\nSearch performance is defined by disk speed and the fraction of vectors that fit in memory.", "type": "string", "enum": ["Mmap"]}, {"description": "Storage in chunked mmap files, appendable\n\nSearch performance is defined by disk speed and the fraction of vectors that fit in memory.", "type": "string", "enum": ["ChunkedMmap"]}, {"description": "Same as `ChunkedMmap`, but vectors are forced to be locked in RAM In this way we avoid cold requests to disk, but risk to run out of memory\n\nDesigned as a replacement for `Memory`, which doesn't depend on RocksDB", "type": "string", "enum": ["InRamChunkedMmap"]}]}, "Indexes": {"description": "Vector index configuration", "oneOf": [{"description": "Do not use any index, scan whole vector collection during search. Guarantee 100% precision, but may be time consuming on large collections.", "type": "object", "required": ["options", "type"], "properties": {"type": {"type": "string", "enum": ["plain"]}, "options": {"type": "object"}}}, {"description": "Use filterable HNSW index for approximate search. Is very fast even on a very huge collections, but require additional space to store index and additional time to build it.", "type": "object", "required": ["options", "type"], "properties": {"type": {"type": "string", "enum": ["hnsw"]}, "options": {"$ref": "#/components/schemas/HnswConfig"}}}]}, "VectorStorageDatatype": {"description": "Storage types for vectors", "type": "string", "enum": ["float32", "float16", "uint8"]}, "SparseVectorDataConfig": {"description": "Config of single sparse vector data storage", "type": "object", "required": ["index"], "properties": {"index": {"$ref": "#/components/schemas/SparseIndexConfig"}, "storage_type": {"$ref": "#/components/schemas/SparseVectorStorageType"}}}, "SparseIndexConfig": {"description": "Configuration for sparse inverted index.", "type": "object", "required": ["index_type"], "properties": {"full_scan_threshold": {"description": "We prefer a full scan search upto (excluding) this number of vectors.\n\nNote: this is number of vectors, not KiloBytes.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "index_type": {"$ref": "#/components/schemas/SparseIndexType"}, "datatype": {"description": "Datatype used to store weights in the index.", "anyOf": [{"$ref": "#/components/schemas/VectorStorageDatatype"}, {"nullable": true}]}}}, "SparseIndexType": {"description": "Sparse index types", "oneOf": [{"description": "Mutable RAM sparse index", "type": "string", "enum": ["MutableRam"]}, {"description": "Immutable RAM sparse index", "type": "string", "enum": ["ImmutableRam"]}, {"description": "Mmap sparse index", "type": "string", "enum": ["Mmap"]}]}, "SparseVectorStorageType": {"oneOf": [{"description": "Storage on disk", "type": "string", "enum": ["on_disk"]}, {"description": "Storage in memory maps", "type": "string", "enum": ["mmap"]}]}, "PayloadStorageType": {"description": "Type of payload storage", "oneOf": [{"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["in_memory"]}}}, {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["on_disk"]}}}, {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["mmap"]}}}]}, "VectorIndexSearchesTelemetry": {"type": "object", "required": ["filtered_exact", "filtered_large_cardinality", "filtered_plain", "filtered_small_cardinality", "filtered_sparse", "unfiltered_exact", "unfiltered_hnsw", "unfiltered_plain", "unfiltered_sparse"], "properties": {"index_name": {"type": "string", "nullable": true}, "unfiltered_plain": {"$ref": "#/components/schemas/OperationDurationStatistics"}, "unfiltered_hnsw": {"$ref": "#/components/schemas/OperationDurationStatistics"}, "unfiltered_sparse": {"$ref": "#/components/schemas/OperationDurationStatistics"}, "filtered_plain": {"$ref": "#/components/schemas/OperationDurationStatistics"}, "filtered_small_cardinality": {"$ref": "#/components/schemas/OperationDurationStatistics"}, "filtered_large_cardinality": {"$ref": "#/components/schemas/OperationDurationStatistics"}, "filtered_exact": {"$ref": "#/components/schemas/OperationDurationStatistics"}, "filtered_sparse": {"$ref": "#/components/schemas/OperationDurationStatistics"}, "unfiltered_exact": {"$ref": "#/components/schemas/OperationDurationStatistics"}}}, "OperationDurationStatistics": {"type": "object", "required": ["count", "total_duration_micros"], "properties": {"count": {"type": "integer", "format": "uint", "minimum": 0}, "fail_count": {"type": "integer", "format": "uint", "minimum": 0}, "avg_duration_micros": {"description": "The average time taken by 128 latest operations, calculated as a weighted mean.", "type": "number", "format": "float", "nullable": true}, "min_duration_micros": {"description": "The minimum duration of the operations across all the measurements.", "type": "number", "format": "float", "nullable": true}, "max_duration_micros": {"description": "The maximum duration of the operations across all the measurements.", "type": "number", "format": "float", "nullable": true}, "total_duration_micros": {"description": "The total duration of all operations in microseconds.", "type": "integer", "format": "uint64", "minimum": 0}, "last_responded": {"type": "string", "format": "date-time", "nullable": true}}}, "PayloadIndexTelemetry": {"type": "object", "required": ["points_count", "points_values_count"], "properties": {"field_name": {"type": "string", "nullable": true}, "points_values_count": {"description": "The amount of values indexed for all points.", "type": "integer", "format": "uint", "minimum": 0}, "points_count": {"description": "The amount of points that have at least one value indexed.", "type": "integer", "format": "uint", "minimum": 0}, "histogram_bucket_size": {"type": "integer", "format": "uint", "minimum": 0, "nullable": true}}}, "OptimizerTelemetry": {"type": "object", "required": ["log", "optimizations", "status"], "properties": {"status": {"$ref": "#/components/schemas/OptimizersStatus"}, "optimizations": {"$ref": "#/components/schemas/OperationDurationStatistics"}, "log": {"type": "array", "items": {"$ref": "#/components/schemas/TrackerTelemetry"}}}}, "TrackerTelemetry": {"description": "Tracker object used in telemetry", "type": "object", "required": ["name", "segment_ids", "start_at", "status"], "properties": {"name": {"description": "Name of the optimizer", "type": "string"}, "segment_ids": {"description": "Segment IDs being optimized", "type": "array", "items": {"type": "integer", "format": "uint", "minimum": 0}}, "status": {"$ref": "#/components/schemas/TrackerStatus"}, "start_at": {"description": "Start time of the optimizer", "type": "string", "format": "date-time"}, "end_at": {"description": "End time of the optimizer", "type": "string", "format": "date-time", "nullable": true}}}, "TrackerStatus": {"description": "Represents the current state of the optimizer being tracked", "oneOf": [{"type": "string", "enum": ["optimizing", "done"]}, {"type": "object", "required": ["cancelled"], "properties": {"cancelled": {"type": "string"}}, "additionalProperties": false}, {"type": "object", "required": ["error"], "properties": {"error": {"type": "string"}}, "additionalProperties": false}]}, "RemoteShardTelemetry": {"type": "object", "required": ["searches", "shard_id", "updates"], "properties": {"shard_id": {"type": "integer", "format": "uint32", "minimum": 0}, "peer_id": {"type": "integer", "format": "uint64", "minimum": 0, "nullable": true}, "searches": {"$ref": "#/components/schemas/OperationDurationStatistics"}, "updates": {"$ref": "#/components/schemas/OperationDurationStatistics"}}}, "ShardCleanStatusTelemetry": {"oneOf": [{"type": "string", "enum": ["started", "done", "cancelled"]}, {"type": "object", "required": ["progress"], "properties": {"progress": {"$ref": "#/components/schemas/ShardCleanStatusProgressTelemetry"}}, "additionalProperties": false}, {"type": "object", "required": ["failed"], "properties": {"failed": {"$ref": "#/components/schemas/ShardCleanStatusFailedTelemetry"}}, "additionalProperties": false}]}, "ShardCleanStatusProgressTelemetry": {"type": "object", "required": ["deleted_points"], "properties": {"deleted_points": {"type": "integer", "format": "uint", "minimum": 0}}}, "ShardCleanStatusFailedTelemetry": {"type": "object", "required": ["reason"], "properties": {"reason": {"type": "string"}}}, "CollectionsAggregatedTelemetry": {"type": "object", "required": ["optimizers_status", "params", "vectors"], "properties": {"vectors": {"type": "integer", "format": "uint", "minimum": 0}, "optimizers_status": {"$ref": "#/components/schemas/OptimizersStatus"}, "params": {"$ref": "#/components/schemas/CollectionParams"}}}, "ClusterTelemetry": {"type": "object", "required": ["enabled"], "properties": {"enabled": {"type": "boolean"}, "status": {"anyOf": [{"$ref": "#/components/schemas/ClusterStatusTelemetry"}, {"nullable": true}]}, "config": {"anyOf": [{"$ref": "#/components/schemas/ClusterConfigTelemetry"}, {"nullable": true}]}, "peers": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/PeerInfo"}, "nullable": true}, "metadata": {"type": "object", "additionalProperties": true, "nullable": true}}}, "ClusterStatusTelemetry": {"type": "object", "required": ["commit", "consensus_thread_status", "is_voter", "number_of_peers", "pending_operations", "term"], "properties": {"number_of_peers": {"type": "integer", "format": "uint", "minimum": 0}, "term": {"type": "integer", "format": "uint64", "minimum": 0}, "commit": {"type": "integer", "format": "uint64", "minimum": 0}, "pending_operations": {"type": "integer", "format": "uint", "minimum": 0}, "role": {"anyOf": [{"$ref": "#/components/schemas/StateRole"}, {"nullable": true}]}, "is_voter": {"type": "boolean"}, "peer_id": {"type": "integer", "format": "uint64", "minimum": 0, "nullable": true}, "consensus_thread_status": {"$ref": "#/components/schemas/ConsensusThreadStatus"}}}, "ClusterConfigTelemetry": {"type": "object", "required": ["consensus", "grpc_timeout_ms", "p2p"], "properties": {"grpc_timeout_ms": {"type": "integer", "format": "uint64", "minimum": 0}, "p2p": {"$ref": "#/components/schemas/P2pConfigTelemetry"}, "consensus": {"$ref": "#/components/schemas/ConsensusConfigTelemetry"}}}, "P2pConfigTelemetry": {"type": "object", "required": ["connection_pool_size"], "properties": {"connection_pool_size": {"type": "integer", "format": "uint", "minimum": 0}}}, "ConsensusConfigTelemetry": {"type": "object", "required": ["bootstrap_timeout_sec", "max_message_queue_size", "tick_period_ms"], "properties": {"max_message_queue_size": {"type": "integer", "format": "uint", "minimum": 0}, "tick_period_ms": {"type": "integer", "format": "uint64", "minimum": 0}, "bootstrap_timeout_sec": {"type": "integer", "format": "uint64", "minimum": 0}}}, "RequestsTelemetry": {"type": "object", "required": ["grpc", "rest"], "properties": {"rest": {"$ref": "#/components/schemas/WebApiTelemetry"}, "grpc": {"$ref": "#/components/schemas/GrpcTelemetry"}}}, "WebApiTelemetry": {"type": "object", "required": ["responses"], "properties": {"responses": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/OperationDurationStatistics"}}}}}, "GrpcTelemetry": {"type": "object", "required": ["responses"], "properties": {"responses": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/OperationDurationStatistics"}}}}, "MemoryTelemetry": {"type": "object", "required": ["active_bytes", "allocated_bytes", "metadata_bytes", "resident_bytes", "retained_bytes"], "properties": {"active_bytes": {"description": "Total number of bytes in active pages allocated by the application", "type": "integer", "format": "uint", "minimum": 0}, "allocated_bytes": {"description": "Total number of bytes allocated by the application", "type": "integer", "format": "uint", "minimum": 0}, "metadata_bytes": {"description": "Total number of bytes dedicated to metadata", "type": "integer", "format": "uint", "minimum": 0}, "resident_bytes": {"description": "Maximum number of bytes in physically resident data pages mapped", "type": "integer", "format": "uint", "minimum": 0}, "retained_bytes": {"description": "Total number of bytes in virtual memory mappings", "type": "integer", "format": "uint", "minimum": 0}}}, "HardwareTelemetry": {"type": "object", "required": ["collection_data"], "properties": {"collection_data": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/HardwareUsage"}}}}, "HardwareUsage": {"description": "Usage of the hardware resources, spent to process the request", "type": "object", "required": ["cpu", "io_read", "io_write"], "properties": {"cpu": {"type": "integer", "format": "uint", "minimum": 0}, "io_read": {"type": "integer", "format": "uint", "minimum": 0}, "io_write": {"type": "integer", "format": "uint", "minimum": 0}}}, "ClusterOperations": {"anyOf": [{"$ref": "#/components/schemas/MoveShardOperation"}, {"$ref": "#/components/schemas/ReplicateShardOperation"}, {"$ref": "#/components/schemas/AbortTransferOperation"}, {"$ref": "#/components/schemas/DropReplicaOperation"}, {"$ref": "#/components/schemas/CreateShardingKeyOperation"}, {"$ref": "#/components/schemas/DropShardingKeyOperation"}, {"$ref": "#/components/schemas/RestartTransferOperation"}, {"$ref": "#/components/schemas/StartReshardingOperation"}, {"$ref": "#/components/schemas/AbortReshardingOperation"}]}, "MoveShardOperation": {"type": "object", "required": ["move_shard"], "properties": {"move_shard": {"$ref": "#/components/schemas/MoveShard"}}}, "MoveShard": {"type": "object", "required": ["from_peer_id", "shard_id", "to_peer_id"], "properties": {"shard_id": {"type": "integer", "format": "uint32", "minimum": 0}, "to_peer_id": {"type": "integer", "format": "uint64", "minimum": 0}, "from_peer_id": {"type": "integer", "format": "uint64", "minimum": 0}, "method": {"description": "Method for transferring the shard from one node to another", "anyOf": [{"$ref": "#/components/schemas/ShardTransferMethod"}, {"nullable": true}]}}}, "ReplicateShardOperation": {"type": "object", "required": ["replicate_shard"], "properties": {"replicate_shard": {"$ref": "#/components/schemas/ReplicateShard"}}}, "ReplicateShard": {"type": "object", "required": ["from_peer_id", "shard_id", "to_peer_id"], "properties": {"shard_id": {"type": "integer", "format": "uint32", "minimum": 0}, "to_peer_id": {"type": "integer", "format": "uint64", "minimum": 0}, "from_peer_id": {"type": "integer", "format": "uint64", "minimum": 0}, "method": {"description": "Method for transferring the shard from one node to another", "anyOf": [{"$ref": "#/components/schemas/ShardTransferMethod"}, {"nullable": true}]}}}, "AbortTransferOperation": {"type": "object", "required": ["abort_transfer"], "properties": {"abort_transfer": {"$ref": "#/components/schemas/AbortShardTransfer"}}}, "AbortShardTransfer": {"type": "object", "required": ["from_peer_id", "shard_id", "to_peer_id"], "properties": {"shard_id": {"type": "integer", "format": "uint32", "minimum": 0}, "to_peer_id": {"type": "integer", "format": "uint64", "minimum": 0}, "from_peer_id": {"type": "integer", "format": "uint64", "minimum": 0}}}, "DropReplicaOperation": {"type": "object", "required": ["drop_replica"], "properties": {"drop_replica": {"$ref": "#/components/schemas/Replica"}}}, "Replica": {"type": "object", "required": ["peer_id", "shard_id"], "properties": {"shard_id": {"type": "integer", "format": "uint32", "minimum": 0}, "peer_id": {"type": "integer", "format": "uint64", "minimum": 0}}}, "CreateShardingKeyOperation": {"type": "object", "required": ["create_sharding_key"], "properties": {"create_sharding_key": {"$ref": "#/components/schemas/CreateShardingKey"}}}, "CreateShardingKey": {"type": "object", "required": ["shard_key"], "properties": {"shard_key": {"$ref": "#/components/schemas/Shard<PERSON>ey"}, "shards_number": {"description": "How many shards to create for this key If not specified, will use the default value from config", "type": "integer", "format": "uint32", "minimum": 1, "nullable": true}, "replication_factor": {"description": "How many replicas to create for each shard If not specified, will use the default value from config", "type": "integer", "format": "uint32", "minimum": 1, "nullable": true}, "placement": {"description": "Placement of shards for this key List of peer ids, that can be used to place shards for this key If not specified, will be randomly placed among all peers", "type": "array", "items": {"type": "integer", "format": "uint64", "minimum": 0}, "nullable": true}}}, "DropShardingKeyOperation": {"type": "object", "required": ["drop_sharding_key"], "properties": {"drop_sharding_key": {"$ref": "#/components/schemas/DropShardingKey"}}}, "DropShardingKey": {"type": "object", "required": ["shard_key"], "properties": {"shard_key": {"$ref": "#/components/schemas/Shard<PERSON>ey"}}}, "RestartTransferOperation": {"type": "object", "required": ["restart_transfer"], "properties": {"restart_transfer": {"$ref": "#/components/schemas/RestartTransfer"}}}, "RestartTransfer": {"type": "object", "required": ["from_peer_id", "method", "shard_id", "to_peer_id"], "properties": {"shard_id": {"type": "integer", "format": "uint32", "minimum": 0}, "from_peer_id": {"type": "integer", "format": "uint64", "minimum": 0}, "to_peer_id": {"type": "integer", "format": "uint64", "minimum": 0}, "method": {"$ref": "#/components/schemas/ShardTransferMethod"}}}, "StartReshardingOperation": {"type": "object", "required": ["start_resharding"], "properties": {"start_resharding": {"$ref": "#/components/schemas/StartResharding"}}}, "StartResharding": {"type": "object", "required": ["direction"], "properties": {"direction": {"$ref": "#/components/schemas/ReshardingDirection"}, "peer_id": {"type": "integer", "format": "uint64", "minimum": 0, "nullable": true}, "shard_key": {"anyOf": [{"$ref": "#/components/schemas/Shard<PERSON>ey"}, {"nullable": true}]}}}, "AbortReshardingOperation": {"type": "object", "required": ["abort_resharding"], "properties": {"abort_resharding": {"$ref": "#/components/schemas/AbortResharding"}}}, "AbortResharding": {"type": "object"}, "SearchRequestBatch": {"type": "object", "required": ["searches"], "properties": {"searches": {"type": "array", "items": {"$ref": "#/components/schemas/SearchRequest"}}}}, "RecommendRequestBatch": {"type": "object", "required": ["searches"], "properties": {"searches": {"type": "array", "items": {"$ref": "#/components/schemas/RecommendRequest"}}}}, "LocksOption": {"type": "object", "required": ["write"], "properties": {"error_message": {"type": "string", "nullable": true}, "write": {"type": "boolean"}}}, "SnapshotRecover": {"type": "object", "required": ["location"], "properties": {"location": {"description": "Examples: - URL `http://localhost:8080/collections/my_collection/snapshots/my_snapshot` - Local path `file:///qdrant/snapshots/test_collection-2022-08-04-10-49-10.snapshot`", "type": "string", "format": "uri"}, "priority": {"description": "Defines which data should be used as a source of truth if there are other replicas in the cluster. If set to `Snapshot`, the snapshot will be used as a source of truth, and the current state will be overwritten. If set to `Replica`, the current state will be used as a source of truth, and after recovery if will be synchronized with the snapshot.", "default": null, "anyOf": [{"$ref": "#/components/schemas/SnapshotPriority"}, {"nullable": true}]}, "checksum": {"description": "Optional SHA256 checksum to verify snapshot integrity before recovery.", "default": null, "type": "string", "nullable": true}, "api_key": {"description": "Optional API key used when fetching the snapshot from a remote URL.", "default": null, "type": "string", "nullable": true}}}, "SnapshotPriority": {"description": "Defines source of truth for snapshot recovery:\n\n`NoSync` means - restore snapshot without *any* additional synchronization. `Snapshot` means - prefer snapshot data over the current state. `Replica` means - prefer existing data over the snapshot.", "type": "string", "enum": ["no_sync", "snapshot", "replica"]}, "CollectionsAliasesResponse": {"type": "object", "required": ["aliases"], "properties": {"aliases": {"type": "array", "items": {"$ref": "#/components/schemas/AliasDescription"}}}}, "AliasDescription": {"type": "object", "required": ["alias_name", "collection_name"], "properties": {"alias_name": {"type": "string"}, "collection_name": {"type": "string"}}, "example": {"alias_name": "blogs-title", "collection_name": "arivx-title"}}, "WriteOrdering": {"description": "Defines write ordering guarantees for collection operations\n\n* `weak` - write operations may be reordered, works faster, default\n\n* `medium` - write operations go through dynamically selected leader, may be inconsistent for a short period of time in case of leader change\n\n* `strong` - Write operations go through the permanent leader, consistent, but may be unavailable if leader is down", "type": "string", "enum": ["weak", "medium", "strong"]}, "ReadConsistency": {"description": "Read consistency parameter\n\nDefines how many replicas should be queried to get the result\n\n* `N` - send N random request and return points, which present on all of them\n\n* `majority` - send N/2+1 random request and return points, which present on all of them\n\n* `quorum` - send requests to all nodes and return points which present on majority of them\n\n* `all` - send requests to all nodes and return points which present on all of them\n\nDefault value is `Factor(1)`", "anyOf": [{"type": "integer", "format": "uint", "minimum": 0}, {"$ref": "#/components/schemas/ReadConsistencyType"}]}, "ReadConsistencyType": {"description": "* `majority` - send N/2+1 random request and return points, which present on all of them\n\n* `quorum` - send requests to all nodes and return points which present on majority of nodes\n\n* `all` - send requests to all nodes and return points which present on all nodes", "type": "string", "enum": ["majority", "quorum", "all"]}, "UpdateVectors": {"type": "object", "required": ["points"], "properties": {"points": {"description": "Points with named vectors", "type": "array", "items": {"$ref": "#/components/schemas/PointVectors"}, "minItems": 1}, "shard_key": {"anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}}}, "PointVectors": {"type": "object", "required": ["id", "vector"], "properties": {"id": {"$ref": "#/components/schemas/ExtendedPointId"}, "vector": {"$ref": "#/components/schemas/VectorStruct"}}}, "DeleteVectors": {"type": "object", "required": ["vector"], "properties": {"points": {"description": "Deletes values from each point in this list", "type": "array", "items": {"$ref": "#/components/schemas/ExtendedPointId"}, "nullable": true}, "filter": {"description": "Deletes values from points that satisfy this filter condition", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "vector": {"description": "Vector names", "type": "array", "items": {"type": "string"}, "minItems": 1, "uniqueItems": true}, "shard_key": {"anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}}}, "PointGroup": {"type": "object", "required": ["hits", "id"], "properties": {"hits": {"description": "Scored points that have the same value of the group_by key", "type": "array", "items": {"$ref": "#/components/schemas/ScoredPoint"}}, "id": {"$ref": "#/components/schemas/GroupId"}, "lookup": {"description": "Record that has been looked up using the group id", "anyOf": [{"$ref": "#/components/schemas/Record"}, {"nullable": true}]}}}, "GroupId": {"description": "Value of the group_by key, shared across all the hits in the group", "anyOf": [{"type": "string"}, {"type": "integer", "format": "uint64", "minimum": 0}, {"type": "integer", "format": "int64"}]}, "SearchGroupsRequest": {"type": "object", "required": ["group_by", "group_size", "limit", "vector"], "properties": {"shard_key": {"description": "Specify in which shards to look for the points, if not specified - look in all shards", "anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "vector": {"$ref": "#/components/schemas/NamedVectorStruct"}, "filter": {"description": "Look only for points which satisfies this conditions", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "params": {"description": "Additional search params", "anyOf": [{"$ref": "#/components/schemas/SearchParams"}, {"nullable": true}]}, "with_payload": {"description": "Select which payload to return with the response. Default is false.", "anyOf": [{"$ref": "#/components/schemas/WithPayloadInterface"}, {"nullable": true}]}, "with_vector": {"description": "Options for specifying which vectors to include into response. Default is false.", "default": null, "anyOf": [{"$ref": "#/components/schemas/WithVector"}, {"nullable": true}]}, "score_threshold": {"description": "Define a minimal score threshold for the result. If defined, less similar results will not be returned. Score of the returned result might be higher or smaller than the threshold depending on the Distance function used. E.g. for cosine similarity only higher scores will be returned.", "type": "number", "format": "float", "nullable": true}, "group_by": {"description": "Payload field to group by, must be a string or number field. If the field contains more than 1 value, all values will be used for grouping. One point can be in multiple groups.", "type": "string", "minLength": 1}, "group_size": {"description": "Maximum amount of points to return per group", "type": "integer", "format": "uint32", "minimum": 1}, "limit": {"description": "Maximum amount of groups to return", "type": "integer", "format": "uint32", "minimum": 1}, "with_lookup": {"description": "Look for points in another collection using the group ids", "anyOf": [{"$ref": "#/components/schemas/WithLookupInterface"}, {"nullable": true}]}}}, "WithLookupInterface": {"anyOf": [{"type": "string"}, {"$ref": "#/components/schemas/WithLookup"}]}, "WithLookup": {"type": "object", "required": ["collection"], "properties": {"collection": {"description": "Name of the collection to use for points lookup", "type": "string"}, "with_payload": {"description": "Options for specifying which payload to include (or not)", "default": true, "anyOf": [{"$ref": "#/components/schemas/WithPayloadInterface"}, {"nullable": true}]}, "with_vectors": {"description": "Options for specifying which vectors to include (or not)", "default": null, "anyOf": [{"$ref": "#/components/schemas/WithVector"}, {"nullable": true}]}}}, "RecommendGroupsRequest": {"type": "object", "required": ["group_by", "group_size", "limit"], "properties": {"shard_key": {"description": "Specify in which shards to look for the points, if not specified - look in all shards", "anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "positive": {"description": "Look for vectors closest to those", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/RecommendExample"}}, "negative": {"description": "Try to avoid vectors like this", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/RecommendExample"}}, "strategy": {"description": "How to use positive and negative examples to find the results", "default": null, "anyOf": [{"$ref": "#/components/schemas/RecommendStrategy"}, {"nullable": true}]}, "filter": {"description": "Look only for points which satisfies this conditions", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "params": {"description": "Additional search params", "anyOf": [{"$ref": "#/components/schemas/SearchParams"}, {"nullable": true}]}, "with_payload": {"description": "Select which payload to return with the response. Default is false.", "anyOf": [{"$ref": "#/components/schemas/WithPayloadInterface"}, {"nullable": true}]}, "with_vector": {"description": "Options for specifying which vectors to include into response. Default is false.", "default": null, "anyOf": [{"$ref": "#/components/schemas/WithVector"}, {"nullable": true}]}, "score_threshold": {"description": "Define a minimal score threshold for the result. If defined, less similar results will not be returned. Score of the returned result might be higher or smaller than the threshold depending on the Distance function used. E.g. for cosine similarity only higher scores will be returned.", "type": "number", "format": "float", "nullable": true}, "using": {"description": "Define which vector to use for recommendation, if not specified - try to use default vector", "default": null, "anyOf": [{"$ref": "#/components/schemas/UsingVector"}, {"nullable": true}]}, "lookup_from": {"description": "The location used to lookup vectors. If not specified - use current collection. Note: the other collection should have the same vector size as the current collection", "default": null, "anyOf": [{"$ref": "#/components/schemas/LookupLocation"}, {"nullable": true}]}, "group_by": {"description": "Payload field to group by, must be a string or number field. If the field contains more than 1 value, all values will be used for grouping. One point can be in multiple groups.", "type": "string", "minLength": 1}, "group_size": {"description": "Maximum amount of points to return per group", "type": "integer", "format": "uint32", "minimum": 1}, "limit": {"description": "Maximum amount of groups to return", "type": "integer", "format": "uint32", "minimum": 1}, "with_lookup": {"description": "Look for points in another collection using the group ids", "anyOf": [{"$ref": "#/components/schemas/WithLookupInterface"}, {"nullable": true}]}}}, "GroupsResult": {"type": "object", "required": ["groups"], "properties": {"groups": {"type": "array", "items": {"$ref": "#/components/schemas/PointGroup"}}}}, "UpdateOperations": {"type": "object", "required": ["operations"], "properties": {"operations": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateOperation"}}}}, "UpdateOperation": {"anyOf": [{"$ref": "#/components/schemas/UpsertOperation"}, {"$ref": "#/components/schemas/DeleteOperation"}, {"$ref": "#/components/schemas/SetPayloadOperation"}, {"$ref": "#/components/schemas/OverwritePayloadOperation"}, {"$ref": "#/components/schemas/DeletePayloadOperation"}, {"$ref": "#/components/schemas/ClearPayloadOperation"}, {"$ref": "#/components/schemas/UpdateVectorsOperation"}, {"$ref": "#/components/schemas/DeleteVectorsOperation"}]}, "UpsertOperation": {"type": "object", "required": ["upsert"], "properties": {"upsert": {"$ref": "#/components/schemas/PointInsertOperations"}}}, "DeleteOperation": {"type": "object", "required": ["delete"], "properties": {"delete": {"$ref": "#/components/schemas/PointsSelector"}}}, "SetPayloadOperation": {"type": "object", "required": ["set_payload"], "properties": {"set_payload": {"$ref": "#/components/schemas/SetPayload"}}}, "OverwritePayloadOperation": {"type": "object", "required": ["overwrite_payload"], "properties": {"overwrite_payload": {"$ref": "#/components/schemas/SetPayload"}}}, "DeletePayloadOperation": {"type": "object", "required": ["delete_payload"], "properties": {"delete_payload": {"$ref": "#/components/schemas/DeletePayload"}}}, "ClearPayloadOperation": {"type": "object", "required": ["clear_payload"], "properties": {"clear_payload": {"$ref": "#/components/schemas/PointsSelector"}}}, "UpdateVectorsOperation": {"type": "object", "required": ["update_vectors"], "properties": {"update_vectors": {"$ref": "#/components/schemas/UpdateVectors"}}}, "DeleteVectorsOperation": {"type": "object", "required": ["delete_vectors"], "properties": {"delete_vectors": {"$ref": "#/components/schemas/DeleteVectors"}}}, "ShardSnapshotRecover": {"type": "object", "required": ["location"], "properties": {"location": {"$ref": "#/components/schemas/ShardSnapshotLocation"}, "priority": {"default": null, "anyOf": [{"$ref": "#/components/schemas/SnapshotPriority"}, {"nullable": true}]}, "checksum": {"description": "Optional SHA256 checksum to verify snapshot integrity before recovery.", "default": null, "type": "string", "nullable": true}, "api_key": {"description": "Optional API key used when fetching the snapshot from a remote URL.", "default": null, "type": "string", "nullable": true}}}, "ShardSnapshotLocation": {"anyOf": [{"type": "string", "format": "uri"}, {"type": "string"}]}, "DiscoverRequest": {"description": "Use context and a target to find the most similar points, constrained by the context.", "type": "object", "required": ["limit"], "properties": {"shard_key": {"description": "Specify in which shards to look for the points, if not specified - look in all shards", "anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "target": {"description": "Look for vectors closest to this.\n\nWhen using the target (with or without context), the integer part of the score represents the rank with respect to the context, while the decimal part of the score relates to the distance to the target.", "anyOf": [{"$ref": "#/components/schemas/RecommendExample"}, {"nullable": true}]}, "context": {"description": "Pairs of { positive, negative } examples to constrain the search.\n\nWhen using only the context (without a target), a special search - called context search - is performed where pairs of points are used to generate a loss that guides the search towards the zone where most positive examples overlap. This means that the score minimizes the scenario of finding a point closer to a negative than to a positive part of a pair.\n\nSince the score of a context relates to loss, the maximum score a point can get is 0.0, and it becomes normal that many points can have a score of 0.0.\n\nFor discovery search (when including a target), the context part of the score for each pair is calculated +1 if the point is closer to a positive than to a negative part of a pair, and -1 otherwise.", "type": "array", "items": {"$ref": "#/components/schemas/ContextExamplePair"}, "nullable": true}, "filter": {"description": "Look only for points which satisfies this conditions", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "params": {"description": "Additional search params", "anyOf": [{"$ref": "#/components/schemas/SearchParams"}, {"nullable": true}]}, "limit": {"description": "Max number of result to return", "type": "integer", "format": "uint", "minimum": 1}, "offset": {"description": "Offset of the first result to return. May be used to paginate results. Note: large offset values may cause performance issues.", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "with_payload": {"description": "Select which payload to return with the response. Default is false.", "anyOf": [{"$ref": "#/components/schemas/WithPayloadInterface"}, {"nullable": true}]}, "with_vector": {"description": "Options for specifying which vectors to include into response. Default is false.", "anyOf": [{"$ref": "#/components/schemas/WithVector"}, {"nullable": true}]}, "using": {"description": "Define which vector to use for recommendation, if not specified - try to use default vector", "default": null, "anyOf": [{"$ref": "#/components/schemas/UsingVector"}, {"nullable": true}]}, "lookup_from": {"description": "The location used to lookup vectors. If not specified - use current collection. Note: the other collection should have the same vector size as the current collection", "default": null, "anyOf": [{"$ref": "#/components/schemas/LookupLocation"}, {"nullable": true}]}}}, "ContextExamplePair": {"type": "object", "required": ["negative", "positive"], "properties": {"positive": {"$ref": "#/components/schemas/RecommendExample"}, "negative": {"$ref": "#/components/schemas/RecommendExample"}}}, "DiscoverRequestBatch": {"type": "object", "required": ["searches"], "properties": {"searches": {"type": "array", "items": {"$ref": "#/components/schemas/DiscoverRequest"}}}}, "VersionInfo": {"type": "object", "required": ["title", "version"], "properties": {"title": {"type": "string"}, "version": {"type": "string"}, "commit": {"type": "string", "nullable": true}}}, "CollectionExistence": {"description": "State of existence of a collection, true = exists, false = does not exist", "type": "object", "required": ["exists"], "properties": {"exists": {"type": "boolean"}}}, "QueryRequest": {"type": "object", "properties": {"shard_key": {"anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "prefetch": {"description": "Sub-requests to perform first. If present, the query will be performed on the results of the prefetch(es).", "default": null, "anyOf": [{"$ref": "#/components/schemas/Prefetch"}, {"type": "array", "items": {"$ref": "#/components/schemas/Prefetch"}}, {"nullable": true}]}, "query": {"description": "Query to perform. If missing without prefetches, returns points ordered by their IDs.", "anyOf": [{"$ref": "#/components/schemas/QueryInterface"}, {"nullable": true}]}, "using": {"description": "Define which vector name to use for querying. If missing, the default vector is used.", "type": "string", "nullable": true}, "filter": {"description": "Filter conditions - return only those points that satisfy the specified conditions.", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "params": {"description": "Search params for when there is no prefetch", "anyOf": [{"$ref": "#/components/schemas/SearchParams"}, {"nullable": true}]}, "score_threshold": {"description": "Return points with scores better than this threshold.", "type": "number", "format": "float", "nullable": true}, "limit": {"description": "Max number of points to return. <PERSON><PERSON><PERSON> is 10.", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "offset": {"description": "Offset of the result. Skip this many points. <PERSON><PERSON><PERSON> is 0", "type": "integer", "format": "uint", "minimum": 0, "nullable": true}, "with_vector": {"description": "Options for specifying which vectors to include into the response. Default is false.", "anyOf": [{"$ref": "#/components/schemas/WithVector"}, {"nullable": true}]}, "with_payload": {"description": "Options for specifying which payload to include or not. Default is false.", "anyOf": [{"$ref": "#/components/schemas/WithPayloadInterface"}, {"nullable": true}]}, "lookup_from": {"description": "The location to use for IDs lookup, if not specified - use the current collection and the 'using' vector Note: the other collection vectors should have the same vector size as the 'using' vector in the current collection", "default": null, "anyOf": [{"$ref": "#/components/schemas/LookupLocation"}, {"nullable": true}]}}}, "Prefetch": {"type": "object", "properties": {"prefetch": {"description": "Sub-requests to perform first. If present, the query will be performed on the results of the prefetches.", "default": null, "anyOf": [{"$ref": "#/components/schemas/Prefetch"}, {"type": "array", "items": {"$ref": "#/components/schemas/Prefetch"}}, {"nullable": true}]}, "query": {"description": "Query to perform. If missing without prefetches, returns points ordered by their IDs.", "anyOf": [{"$ref": "#/components/schemas/QueryInterface"}, {"nullable": true}]}, "using": {"description": "Define which vector name to use for querying. If missing, the default vector is used.", "type": "string", "nullable": true}, "filter": {"description": "Filter conditions - return only those points that satisfy the specified conditions.", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "params": {"description": "Search params for when there is no prefetch", "anyOf": [{"$ref": "#/components/schemas/SearchParams"}, {"nullable": true}]}, "score_threshold": {"description": "Return points with scores better than this threshold.", "type": "number", "format": "float", "nullable": true}, "limit": {"description": "Max number of points to return. <PERSON><PERSON><PERSON> is 10.", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "lookup_from": {"description": "The location to use for IDs lookup, if not specified - use the current collection and the 'using' vector Note: the other collection vectors should have the same vector size as the 'using' vector in the current collection", "default": null, "anyOf": [{"$ref": "#/components/schemas/LookupLocation"}, {"nullable": true}]}}}, "QueryInterface": {"anyOf": [{"$ref": "#/components/schemas/VectorInput"}, {"$ref": "#/components/schemas/Query"}]}, "VectorInput": {"anyOf": [{"type": "array", "items": {"type": "number", "format": "float"}}, {"$ref": "#/components/schemas/SparseVector"}, {"type": "array", "items": {"type": "array", "items": {"type": "number", "format": "float"}}}, {"$ref": "#/components/schemas/ExtendedPointId"}, {"$ref": "#/components/schemas/Document"}, {"$ref": "#/components/schemas/Image"}, {"$ref": "#/components/schemas/InferenceObject"}]}, "Query": {"anyOf": [{"$ref": "#/components/schemas/NearestQuery"}, {"$ref": "#/components/schemas/RecommendQuery"}, {"$ref": "#/components/schemas/DiscoverQuery"}, {"$ref": "#/components/schemas/ContextQuery"}, {"$ref": "#/components/schemas/OrderByQuery"}, {"$ref": "#/components/schemas/FusionQuery"}, {"$ref": "#/components/schemas/SampleQuery"}]}, "NearestQuery": {"type": "object", "required": ["nearest"], "properties": {"nearest": {"$ref": "#/components/schemas/VectorInput"}}}, "RecommendQuery": {"type": "object", "required": ["recommend"], "properties": {"recommend": {"$ref": "#/components/schemas/RecommendInput"}}}, "RecommendInput": {"type": "object", "properties": {"positive": {"description": "Look for vectors closest to the vectors from these points", "type": "array", "items": {"$ref": "#/components/schemas/VectorInput"}, "nullable": true}, "negative": {"description": "Try to avoid vectors like the vector from these points", "type": "array", "items": {"$ref": "#/components/schemas/VectorInput"}, "nullable": true}, "strategy": {"description": "How to use the provided vectors to find the results", "anyOf": [{"$ref": "#/components/schemas/RecommendStrategy"}, {"nullable": true}]}}}, "DiscoverQuery": {"type": "object", "required": ["discover"], "properties": {"discover": {"$ref": "#/components/schemas/DiscoverInput"}}}, "DiscoverInput": {"type": "object", "required": ["context", "target"], "properties": {"target": {"$ref": "#/components/schemas/VectorInput"}, "context": {"description": "Search space will be constrained by these pairs of vectors", "anyOf": [{"$ref": "#/components/schemas/ContextPair"}, {"type": "array", "items": {"$ref": "#/components/schemas/ContextPair"}}, {"nullable": true}]}}}, "ContextPair": {"type": "object", "required": ["negative", "positive"], "properties": {"positive": {"$ref": "#/components/schemas/VectorInput"}, "negative": {"$ref": "#/components/schemas/VectorInput"}}}, "ContextQuery": {"type": "object", "required": ["context"], "properties": {"context": {"$ref": "#/components/schemas/ContextInput"}}}, "ContextInput": {"anyOf": [{"$ref": "#/components/schemas/ContextPair"}, {"type": "array", "items": {"$ref": "#/components/schemas/ContextPair"}}, {"nullable": true}]}, "OrderByQuery": {"type": "object", "required": ["order_by"], "properties": {"order_by": {"$ref": "#/components/schemas/OrderByInterface"}}}, "FusionQuery": {"type": "object", "required": ["fusion"], "properties": {"fusion": {"$ref": "#/components/schemas/Fusion"}}}, "Fusion": {"description": "Fusion algorithm allows to combine results of multiple prefetches.\n\nAvailable fusion algorithms:\n\n* `rrf` - Reciprocal Rank Fusion * `dbsf` - Distribution-Based Score Fusion", "type": "string", "enum": ["rrf", "dbsf"]}, "SampleQuery": {"type": "object", "required": ["sample"], "properties": {"sample": {"$ref": "#/components/schemas/Sample"}}}, "Sample": {"type": "string", "enum": ["random"]}, "QueryRequestBatch": {"type": "object", "required": ["searches"], "properties": {"searches": {"type": "array", "items": {"$ref": "#/components/schemas/QueryRequest"}}}}, "QueryResponse": {"type": "object", "required": ["points"], "properties": {"points": {"type": "array", "items": {"$ref": "#/components/schemas/ScoredPoint"}}}}, "QueryGroupsRequest": {"type": "object", "required": ["group_by"], "properties": {"shard_key": {"anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "prefetch": {"description": "Sub-requests to perform first. If present, the query will be performed on the results of the prefetch(es).", "default": null, "anyOf": [{"$ref": "#/components/schemas/Prefetch"}, {"type": "array", "items": {"$ref": "#/components/schemas/Prefetch"}}, {"nullable": true}]}, "query": {"description": "Query to perform. If missing without prefetches, returns points ordered by their IDs.", "anyOf": [{"$ref": "#/components/schemas/QueryInterface"}, {"nullable": true}]}, "using": {"description": "Define which vector name to use for querying. If missing, the default vector is used.", "type": "string", "nullable": true}, "filter": {"description": "Filter conditions - return only those points that satisfy the specified conditions.", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "params": {"description": "Search params for when there is no prefetch", "anyOf": [{"$ref": "#/components/schemas/SearchParams"}, {"nullable": true}]}, "score_threshold": {"description": "Return points with scores better than this threshold.", "type": "number", "format": "float", "nullable": true}, "with_vector": {"description": "Options for specifying which vectors to include into the response. Default is false.", "anyOf": [{"$ref": "#/components/schemas/WithVector"}, {"nullable": true}]}, "with_payload": {"description": "Options for specifying which payload to include or not. Default is false.", "anyOf": [{"$ref": "#/components/schemas/WithPayloadInterface"}, {"nullable": true}]}, "lookup_from": {"description": "The location to use for IDs lookup, if not specified - use the current collection and the 'using' vector Note: the other collection vectors should have the same vector size as the 'using' vector in the current collection", "default": null, "anyOf": [{"$ref": "#/components/schemas/LookupLocation"}, {"nullable": true}]}, "group_by": {"description": "Payload field to group by, must be a string or number field. If the field contains more than 1 value, all values will be used for grouping. One point can be in multiple groups.", "type": "string", "minLength": 1}, "group_size": {"description": "Maximum amount of points to return per group. Default is 3.", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "limit": {"description": "Maximum amount of groups to return. Default is 10.", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "with_lookup": {"description": "Look for points in another collection using the group ids", "anyOf": [{"$ref": "#/components/schemas/WithLookupInterface"}, {"nullable": true}]}}}, "SearchMatrixRequest": {"type": "object", "properties": {"shard_key": {"description": "Specify in which shards to look for the points, if not specified - look in all shards", "anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "filter": {"description": "Look only for points which satisfies this conditions", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "sample": {"description": "How many points to select and search within. De<PERSON><PERSON> is 10.", "type": "integer", "format": "uint", "minimum": 2, "nullable": true}, "limit": {"description": "How many neighbours per sample to find. Default is 3.", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "using": {"description": "Define which vector name to use for querying. If missing, the default vector is used.", "type": "string", "nullable": true}}}, "SearchMatrixOffsetsResponse": {"type": "object", "required": ["ids", "offsets_col", "offsets_row", "scores"], "properties": {"offsets_row": {"description": "Row indices of the matrix", "type": "array", "items": {"type": "integer", "format": "uint64", "minimum": 0}}, "offsets_col": {"description": "Column indices of the matrix", "type": "array", "items": {"type": "integer", "format": "uint64", "minimum": 0}}, "scores": {"description": "Scores associated with matrix coordinates", "type": "array", "items": {"type": "number", "format": "float"}}, "ids": {"description": "Ids of the points in order", "type": "array", "items": {"$ref": "#/components/schemas/ExtendedPointId"}}}}, "SearchMatrixPairsResponse": {"type": "object", "required": ["pairs"], "properties": {"pairs": {"description": "List of pairs of points with scores", "type": "array", "items": {"$ref": "#/components/schemas/SearchMatrixPair"}}}}, "SearchMatrixPair": {"description": "Pair of points (a, b) with score", "type": "object", "required": ["a", "b", "score"], "properties": {"a": {"$ref": "#/components/schemas/ExtendedPointId"}, "b": {"$ref": "#/components/schemas/ExtendedPointId"}, "score": {"type": "number", "format": "float"}}}, "FacetRequest": {"type": "object", "required": ["key"], "properties": {"shard_key": {"anyOf": [{"$ref": "#/components/schemas/ShardKeySelector"}, {"nullable": true}]}, "key": {"description": "Payload key to use for faceting.", "type": "string"}, "limit": {"description": "Max number of hits to return. <PERSON><PERSON><PERSON> is 10.", "type": "integer", "format": "uint", "minimum": 1, "nullable": true}, "filter": {"description": "Filter conditions - only consider points that satisfy these conditions.", "anyOf": [{"$ref": "#/components/schemas/Filter"}, {"nullable": true}]}, "exact": {"description": "Whether to do a more expensive exact count for each of the values in the facet. Default is false.", "type": "boolean", "nullable": true}}}, "FacetResponse": {"type": "object", "required": ["hits"], "properties": {"hits": {"type": "array", "items": {"$ref": "#/components/schemas/FacetValueHit"}}}}, "FacetValueHit": {"type": "object", "required": ["count", "value"], "properties": {"value": {"$ref": "#/components/schemas/FacetValue"}, "count": {"type": "integer", "format": "uint", "minimum": 0}}}, "FacetValue": {"anyOf": [{"type": "string"}, {"type": "integer", "format": "int64"}, {"type": "boolean"}]}}}}