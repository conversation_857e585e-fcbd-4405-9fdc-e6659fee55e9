{"name": "qdrant-web-ui", "version": "0.1.39", "license": "Apache-2.0", "private": true, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mdx-js/react": "^2.3.0", "@mdx-js/rollup": "^2.3.0", "@monaco-editor/react": "^4.6.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/x-data-grid": "^7.29.1", "@qdrant/js-client-rest": "^1.14.0", "@saehrimnir/druidjs": "^0.6.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@textea/json-viewer": "^2.16.2", "@uppy/core": "^4.4.4", "@uppy/dashboard": "^4.3.3", "@uppy/drag-drop": "^4.1.2", "@uppy/progress-bar": "^4.0.2", "@uppy/react": "^4.2.3", "@uppy/xhr-upload": "^4.2.3", "@vitejs/plugin-react": "^4.4.1", "autocomplete-openapi": "0.1.6", "axios": "^1.9.0", "chart.js": "^4.4.9", "chroma-js": "^2.4.2", "force-graph": "^1.43.5", "jose": "^5.2.3", "jsonc-parser": "^3.2.0", "lodash": "^4.17.21", "monaco-editor": "^0.44.0", "mui-chips-input": "^7.0.1", "notistack": "^3.0.1", "openapi-client-axios": "^7.1.1", "pretty-bytes": "^6.1.1", "prism-react-renderer": "^2.0.6", "prismjs": "^1.29.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-diff-viewer-continued": "^3.4.0", "react-dom": "^18.2.0", "react-resizable-panels": "^0.0.51", "react-router-dom": "^6.8.1", "react-simple-code-editor": "^0.13.1", "unist-util-visit": "^5.0.0", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0", "web-vitals": "^2.1.4"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "*"}, "scripts": {"test": "vitest", "eject": "vite eject", "start": "vite", "build": "vite build --base './'", "build-qdrant": "vite build --base '/dashboard/'", "serve": "vite preview", "lint": "eslint  src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier ./src --write", "format:check": "prettier ./src --check"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"transformIgnorePatterns": ["/node_modules/(?!(axios|react-day-picker)/)"]}, "devDependencies": {"eslint": "^8.46.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-react": "^7.32.2", "jsdom": "^22.1.0", "prettier": "2.8.7", "vite-plugin-eslint": "^1.8.1", "vitest": "^3.1.2"}}