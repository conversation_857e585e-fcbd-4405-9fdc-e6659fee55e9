2025/05/19-15:45:12.074717 16a0 RocksDB version: 9.9.3
2025/05/19-15:45:12.074786 16a0 Compile date 2024-12-05 01:25:31
2025/05/19-15:45:12.074807 16a0 DB SUMMARY
2025/05/19-15:45:12.074820 16a0 Host name (Env):  VM-IA
2025/05/19-15:45:12.074832 16a0 DB Session ID:  Q9ASQCKA9G7MN6Q4KJ1D
2025/05/19-15:45:12.075079 16a0 SST files in ./qdrant_data\collections\documents\0\segments\d0d9ddb2-4210-4067-835c-e19c41f064b5 dir, Total Num: 0, files: 
2025/05/19-15:45:12.075099 16a0 Write Ahead Log file in ./qdrant_data\collections\documents\0\segments\d0d9ddb2-4210-4067-835c-e19c41f064b5: 
2025/05/19-15:45:12.075111 16a0                         Options.error_if_exists: 0
2025/05/19-15:45:12.075124 16a0                       Options.create_if_missing: 1
2025/05/19-15:45:12.075391 16a0                         Options.paranoid_checks: 1
2025/05/19-15:45:12.075399 16a0             Options.flush_verify_memtable_count: 1
2025/05/19-15:45:12.075403 16a0          Options.compaction_verify_record_count: 1
2025/05/19-15:45:12.075407 16a0                               Options.track_and_verify_wals_in_manifest: 0
2025/05/19-15:45:12.075411 16a0        Options.verify_sst_unique_id_in_manifest: 1
2025/05/19-15:45:12.075416 16a0                                     Options.env: 0000023DE8BA8DE0
2025/05/19-15:45:12.075420 16a0                                      Options.fs: WinFS
2025/05/19-15:45:12.075425 16a0                                Options.info_log: 0000023DEA989AA0
2025/05/19-15:45:12.075428 16a0                Options.max_file_opening_threads: 16
2025/05/19-15:45:12.075433 16a0                              Options.statistics: 0000000000000000
2025/05/19-15:45:12.075437 16a0                               Options.use_fsync: 0
2025/05/19-15:45:12.075441 16a0                       Options.max_log_file_size: 1048576
2025/05/19-15:45:12.075445 16a0                  Options.max_manifest_file_size: 1073741824
2025/05/19-15:45:12.075449 16a0                   Options.log_file_time_to_roll: 0
2025/05/19-15:45:12.075453 16a0                       Options.keep_log_file_num: 1
2025/05/19-15:45:12.075457 16a0                    Options.recycle_log_file_num: 0
2025/05/19-15:45:12.075461 16a0                         Options.allow_fallocate: 1
2025/05/19-15:45:12.075465 16a0                        Options.allow_mmap_reads: 0
2025/05/19-15:45:12.075468 16a0                       Options.allow_mmap_writes: 0
2025/05/19-15:45:12.075472 16a0                        Options.use_direct_reads: 0
2025/05/19-15:45:12.075476 16a0                        Options.use_direct_io_for_flush_and_compaction: 0
2025/05/19-15:45:12.075480 16a0          Options.create_missing_column_families: 1
2025/05/19-15:45:12.075484 16a0                              Options.db_log_dir: 
2025/05/19-15:45:12.075488 16a0                                 Options.wal_dir: 
2025/05/19-15:45:12.075492 16a0                Options.table_cache_numshardbits: 6
2025/05/19-15:45:12.075496 16a0                         Options.WAL_ttl_seconds: 0
2025/05/19-15:45:12.075500 16a0                       Options.WAL_size_limit_MB: 0
2025/05/19-15:45:12.075504 16a0                        Options.max_write_batch_group_size_bytes: 1048576
2025/05/19-15:45:12.075508 16a0             Options.manifest_preallocation_size: 4194304
2025/05/19-15:45:12.075512 16a0                     Options.is_fd_close_on_exec: 1
2025/05/19-15:45:12.075516 16a0                   Options.advise_random_on_open: 1
2025/05/19-15:45:12.075520 16a0                    Options.db_write_buffer_size: 0
2025/05/19-15:45:12.075524 16a0                    Options.write_buffer_manager: 0000023DEB9280C0
2025/05/19-15:45:12.075528 16a0           Options.random_access_max_buffer_size: 1048576
2025/05/19-15:45:12.075532 16a0                      Options.use_adaptive_mutex: 0
2025/05/19-15:45:12.075536 16a0                            Options.rate_limiter: 0000000000000000
2025/05/19-15:45:12.075540 16a0     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/05/19-15:45:12.075544 16a0                       Options.wal_recovery_mode: 0
2025/05/19-15:45:12.075548 16a0                  Options.enable_thread_tracking: 0
2025/05/19-15:45:12.075583 16a0                  Options.enable_pipelined_write: 0
2025/05/19-15:45:12.075589 16a0                  Options.unordered_write: 0
2025/05/19-15:45:12.075594 16a0         Options.allow_concurrent_memtable_write: 1
2025/05/19-15:45:12.075598 16a0      Options.enable_write_thread_adaptive_yield: 1
2025/05/19-15:45:12.075603 16a0             Options.write_thread_max_yield_usec: 100
2025/05/19-15:45:12.075607 16a0            Options.write_thread_slow_yield_usec: 3
2025/05/19-15:45:12.075611 16a0                               Options.row_cache: None
2025/05/19-15:45:12.075616 16a0                              Options.wal_filter: None
2025/05/19-15:45:12.075620 16a0             Options.avoid_flush_during_recovery: 0
2025/05/19-15:45:12.075624 16a0             Options.allow_ingest_behind: 0
2025/05/19-15:45:12.075629 16a0             Options.two_write_queues: 0
2025/05/19-15:45:12.075633 16a0             Options.manual_wal_flush: 0
2025/05/19-15:45:12.075637 16a0             Options.wal_compression: 0
2025/05/19-15:45:12.075642 16a0             Options.background_close_inactive_wals: 0
2025/05/19-15:45:12.075646 16a0             Options.atomic_flush: 0
2025/05/19-15:45:12.075650 16a0             Options.avoid_unnecessary_blocking_io: 0
2025/05/19-15:45:12.075654 16a0             Options.prefix_seek_opt_in_only: 0
2025/05/19-15:45:12.075659 16a0                 Options.persist_stats_to_disk: 0
2025/05/19-15:45:12.075664 16a0                 Options.write_dbid_to_manifest: 1
2025/05/19-15:45:12.075668 16a0                 Options.write_identity_file: 1
2025/05/19-15:45:12.075673 16a0                 Options.log_readahead_size: 0
2025/05/19-15:45:12.075677 16a0                 Options.file_checksum_gen_factory: Unknown
2025/05/19-15:45:12.075682 16a0                 Options.best_efforts_recovery: 0
2025/05/19-15:45:12.075686 16a0                Options.max_bgerror_resume_count: 2147483647
2025/05/19-15:45:12.075691 16a0            Options.bgerror_resume_retry_interval: 1000000
2025/05/19-15:45:12.075695 16a0             Options.allow_data_in_errors: 0
2025/05/19-15:45:12.075699 16a0             Options.db_host_id: __hostname__
2025/05/19-15:45:12.075704 16a0             Options.enforce_single_del_contracts: true
2025/05/19-15:45:12.075709 16a0             Options.metadata_write_temperature: kUnknown
2025/05/19-15:45:12.075714 16a0             Options.wal_write_temperature: kUnknown
2025/05/19-15:45:12.075719 16a0             Options.max_background_jobs: 2
2025/05/19-15:45:12.075723 16a0             Options.max_background_compactions: -1
2025/05/19-15:45:12.075728 16a0             Options.max_subcompactions: 1
2025/05/19-15:45:12.075732 16a0             Options.avoid_flush_during_shutdown: 0
2025/05/19-15:45:12.075736 16a0           Options.writable_file_max_buffer_size: 1048576
2025/05/19-15:45:12.075741 16a0             Options.delayed_write_rate : 16777216
2025/05/19-15:45:12.075745 16a0             Options.max_total_wal_size: 0
2025/05/19-15:45:12.075749 16a0             Options.delete_obsolete_files_period_micros: 180000000
2025/05/19-15:45:12.075754 16a0                   Options.stats_dump_period_sec: 600
2025/05/19-15:45:12.075758 16a0                 Options.stats_persist_period_sec: 600
2025/05/19-15:45:12.075762 16a0                 Options.stats_history_buffer_size: 1048576
2025/05/19-15:45:12.075766 16a0                          Options.max_open_files: 256
2025/05/19-15:45:12.075770 16a0                          Options.bytes_per_sync: 0
2025/05/19-15:45:12.075774 16a0                      Options.wal_bytes_per_sync: 0
2025/05/19-15:45:12.075778 16a0                   Options.strict_bytes_per_sync: 0
2025/05/19-15:45:12.075782 16a0       Options.compaction_readahead_size: 2097152
2025/05/19-15:45:12.075786 16a0                  Options.max_background_flushes: -1
2025/05/19-15:45:12.075791 16a0 Options.daily_offpeak_time_utc: 
2025/05/19-15:45:12.075795 16a0 Compression algorithms supported:
2025/05/19-15:45:12.075801 16a0 	kZSTD supported: 0
2025/05/19-15:45:12.075834 16a0 	kSnappyCompression supported: 1
2025/05/19-15:45:12.075839 16a0 	kBZip2Compression supported: 0
2025/05/19-15:45:12.075843 16a0 	kZlibCompression supported: 0
2025/05/19-15:45:12.075847 16a0 	kLZ4Compression supported: 1
2025/05/19-15:45:12.075852 16a0 	kXpressCompression supported: 0
2025/05/19-15:45:12.075856 16a0 	kLZ4HCCompression supported: 1
2025/05/19-15:45:12.075860 16a0 	kZSTDNotFinalCompression supported: 0
2025/05/19-15:45:12.075865 16a0 Fast CRC32 supported: Not supported on x86
2025/05/19-15:45:12.075871 16a0 DMutex implementation: std::mutex
2025/05/19-15:45:12.075875 16a0 Jemalloc supported: 0
2025/05/19-15:45:12.100082 16a0               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.100101 16a0           Options.merge_operator: None
2025/05/19-15:45:12.100106 16a0        Options.compaction_filter: None
2025/05/19-15:45:12.100111 16a0        Options.compaction_filter_factory: None
2025/05/19-15:45:12.100115 16a0  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.100120 16a0         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.100125 16a0            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.100160 16a0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB9181B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB405D30
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.100166 16a0        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.100170 16a0  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.100175 16a0          Options.compression: LZ4
2025/05/19-15:45:12.100180 16a0                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.100185 16a0       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.100189 16a0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.100194 16a0             Options.num_levels: 7
2025/05/19-15:45:12.100198 16a0        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.100203 16a0     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.100207 16a0     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.100212 16a0            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.100217 16a0                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.100222 16a0               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.100226 16a0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.100231 16a0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.100235 16a0         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.100240 16a0                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.100245 16a0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.100250 16a0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.100259 16a0            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.100266 16a0                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.100271 16a0               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.100275 16a0         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.100280 16a0         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.100285 16a0         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.100290 16a0         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.100294 16a0                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.100299 16a0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.100304 16a0      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.100309 16a0          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.100314 16a0              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.100319 16a0                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.100326 16a0             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.100331 16a0                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.100336 16a0 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.100341 16a0          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.100350 16a0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.100354 16a0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.100358 16a0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.100362 16a0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.100366 16a0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.100369 16a0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.100373 16a0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.100378 16a0       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.100382 16a0                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.100386 16a0                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.100390 16a0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.100394 16a0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.100398 16a0                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.100404 16a0                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.100408 16a0                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.100412 16a0 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.100429 16a0 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.100434 16a0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.100438 16a0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.100442 16a0 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.100447 16a0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.100451 16a0 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.100455 16a0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.100459 16a0 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.100466 16a0                   Options.table_properties_collectors: 
2025/05/19-15:45:12.100470 16a0                   Options.inplace_update_support: 0
2025/05/19-15:45:12.100474 16a0                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.100478 16a0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.100482 16a0               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.100486 16a0   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.100540 16a0                           Options.bloom_locality: 0
2025/05/19-15:45:12.100546 16a0                    Options.max_successive_merges: 0
2025/05/19-15:45:12.100550 16a0             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.100554 16a0                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.100558 16a0                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.100561 16a0                Options.force_consistency_checks: 1
2025/05/19-15:45:12.100565 16a0                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.100569 16a0                               Options.ttl: 2592000
2025/05/19-15:45:12.100573 16a0          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.100578 16a0                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.100582 16a0  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.100586 16a0    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.100589 16a0                       Options.enable_blob_files: false
2025/05/19-15:45:12.100594 16a0                           Options.min_blob_size: 0
2025/05/19-15:45:12.100597 16a0                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.100602 16a0                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.100606 16a0          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.100610 16a0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.100615 16a0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.100619 16a0          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.100623 16a0                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.100627 16a0         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.100631 16a0            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.113802 16a0               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.113816 16a0           Options.merge_operator: None
2025/05/19-15:45:12.113821 16a0        Options.compaction_filter: None
2025/05/19-15:45:12.113825 16a0        Options.compaction_filter_factory: None
2025/05/19-15:45:12.113829 16a0  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.113832 16a0         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.113837 16a0            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.113866 16a0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB9181B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB405D30
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.113871 16a0        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.113876 16a0  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.113880 16a0          Options.compression: LZ4
2025/05/19-15:45:12.113890 16a0                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.113898 16a0       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.113904 16a0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.113908 16a0             Options.num_levels: 7
2025/05/19-15:45:12.113912 16a0        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.113916 16a0     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.113920 16a0     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.113924 16a0            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.113928 16a0                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.113932 16a0               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.113936 16a0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.113941 16a0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.113945 16a0         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.113949 16a0                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.113953 16a0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.113958 16a0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.113962 16a0            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.113966 16a0                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.113970 16a0               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.113973 16a0         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.113977 16a0         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.113981 16a0         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.113985 16a0         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.113989 16a0                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.113993 16a0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.113997 16a0      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.114001 16a0          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.114005 16a0              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.114009 16a0                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.114013 16a0             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.114017 16a0                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.114021 16a0 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.114026 16a0          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.114030 16a0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.114034 16a0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.114038 16a0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.114042 16a0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.114046 16a0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.114050 16a0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.114054 16a0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.114060 16a0       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.114065 16a0                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.114069 16a0                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.114073 16a0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.114077 16a0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.114081 16a0                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.114086 16a0                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.114090 16a0                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.114095 16a0 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.114099 16a0 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.114104 16a0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.114108 16a0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.114113 16a0 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.114117 16a0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.114121 16a0 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.114125 16a0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.114130 16a0 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.114135 16a0                   Options.table_properties_collectors: 
2025/05/19-15:45:12.114140 16a0                   Options.inplace_update_support: 0
2025/05/19-15:45:12.114144 16a0                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.114148 16a0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.114152 16a0               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.114156 16a0   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.114160 16a0                           Options.bloom_locality: 0
2025/05/19-15:45:12.114165 16a0                    Options.max_successive_merges: 0
2025/05/19-15:45:12.114170 16a0             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.114174 16a0                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.114178 16a0                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.114182 16a0                Options.force_consistency_checks: 1
2025/05/19-15:45:12.114186 16a0                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.114190 16a0                               Options.ttl: 2592000
2025/05/19-15:45:12.114194 16a0          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.114198 16a0                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.114202 16a0  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.114206 16a0    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.114211 16a0                       Options.enable_blob_files: false
2025/05/19-15:45:12.114215 16a0                           Options.min_blob_size: 0
2025/05/19-15:45:12.114219 16a0                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.114223 16a0                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.114227 16a0          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.114232 16a0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.114237 16a0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.114241 16a0          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.114245 16a0                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.114249 16a0         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.114253 16a0            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.114908 16a0               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.114921 16a0           Options.merge_operator: None
2025/05/19-15:45:12.114925 16a0        Options.compaction_filter: None
2025/05/19-15:45:12.114930 16a0        Options.compaction_filter_factory: None
2025/05/19-15:45:12.114934 16a0  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.114939 16a0         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.114943 16a0            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.114974 16a0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB9181B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB405D30
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.114982 16a0        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.114989 16a0  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.114994 16a0          Options.compression: LZ4
2025/05/19-15:45:12.114999 16a0                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.115004 16a0       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.115009 16a0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.115014 16a0             Options.num_levels: 7
2025/05/19-15:45:12.115019 16a0        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.115024 16a0     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.115029 16a0     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.115034 16a0            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.115039 16a0                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.115044 16a0               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.115049 16a0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.115054 16a0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.115060 16a0         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.115065 16a0                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.115070 16a0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.115076 16a0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.115081 16a0            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.115086 16a0                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.115091 16a0               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.115096 16a0         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.115101 16a0         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.115106 16a0         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.115111 16a0         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.115116 16a0                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.115121 16a0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.115126 16a0      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.115131 16a0          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.115136 16a0              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.115140 16a0                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.115145 16a0             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.115150 16a0                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.115155 16a0 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.115161 16a0          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.115212 16a0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.115219 16a0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.115224 16a0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.115229 16a0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.115234 16a0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.115239 16a0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.115244 16a0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.115249 16a0       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.115254 16a0                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.115259 16a0                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.115264 16a0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.115269 16a0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.115274 16a0                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.115281 16a0                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.115287 16a0                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.115292 16a0 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.115297 16a0 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.115302 16a0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.115307 16a0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.115312 16a0 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.115318 16a0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.115323 16a0 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.115328 16a0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.115333 16a0 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.115340 16a0                   Options.table_properties_collectors: 
2025/05/19-15:45:12.115345 16a0                   Options.inplace_update_support: 0
2025/05/19-15:45:12.115350 16a0                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.115355 16a0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.115361 16a0               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.115365 16a0   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.115370 16a0                           Options.bloom_locality: 0
2025/05/19-15:45:12.115375 16a0                    Options.max_successive_merges: 0
2025/05/19-15:45:12.115380 16a0             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.115385 16a0                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.115390 16a0                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.115396 16a0                Options.force_consistency_checks: 1
2025/05/19-15:45:12.115401 16a0                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.115406 16a0                               Options.ttl: 2592000
2025/05/19-15:45:12.115413 16a0          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.115418 16a0                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.115423 16a0  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.115428 16a0    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.115433 16a0                       Options.enable_blob_files: false
2025/05/19-15:45:12.115438 16a0                           Options.min_blob_size: 0
2025/05/19-15:45:12.115443 16a0                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.115448 16a0                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.115455 16a0          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.115461 16a0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.115467 16a0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.115473 16a0          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.115478 16a0                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.115483 16a0         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.115488 16a0            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.116260 16a0               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.116271 16a0           Options.merge_operator: None
2025/05/19-15:45:12.116275 16a0        Options.compaction_filter: None
2025/05/19-15:45:12.116280 16a0        Options.compaction_filter_factory: None
2025/05/19-15:45:12.116285 16a0  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.116289 16a0         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.116293 16a0            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.116335 16a0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB9181B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB405D30
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.116343 16a0        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.116347 16a0  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.116351 16a0          Options.compression: LZ4
2025/05/19-15:45:12.116355 16a0                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.116360 16a0       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.116364 16a0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.116369 16a0             Options.num_levels: 7
2025/05/19-15:45:12.116373 16a0        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.116377 16a0     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.116382 16a0     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.116386 16a0            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.116391 16a0                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.116396 16a0               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.116400 16a0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.116405 16a0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.116410 16a0         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.116414 16a0                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.116420 16a0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.116425 16a0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.116431 16a0            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.116438 16a0                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.116444 16a0               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.116448 16a0         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.116452 16a0         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.116457 16a0         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.116461 16a0         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.116465 16a0                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.116470 16a0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.116474 16a0      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.116478 16a0          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.116483 16a0              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.116487 16a0                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.116491 16a0             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.116496 16a0                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.116500 16a0 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.116505 16a0          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.116509 16a0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.116514 16a0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.116518 16a0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.116523 16a0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.116527 16a0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.116531 16a0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.116535 16a0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.116539 16a0       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.116543 16a0                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.116547 16a0                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.116551 16a0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.116555 16a0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.116559 16a0                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.116564 16a0                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.116569 16a0                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.116573 16a0 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.116577 16a0 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.116582 16a0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.116586 16a0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.116591 16a0 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.116595 16a0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.116600 16a0 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.116604 16a0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.116609 16a0 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.116701 16a0                   Options.table_properties_collectors: 
2025/05/19-15:45:12.116711 16a0                   Options.inplace_update_support: 0
2025/05/19-15:45:12.116716 16a0                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.116722 16a0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.116727 16a0               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.116732 16a0   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.116742 16a0                           Options.bloom_locality: 0
2025/05/19-15:45:12.116749 16a0                    Options.max_successive_merges: 0
2025/05/19-15:45:12.116753 16a0             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.116757 16a0                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.116761 16a0                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.116766 16a0                Options.force_consistency_checks: 1
2025/05/19-15:45:12.116770 16a0                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.116774 16a0                               Options.ttl: 2592000
2025/05/19-15:45:12.116779 16a0          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.116784 16a0                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.116789 16a0  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.116797 16a0    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.116804 16a0                       Options.enable_blob_files: false
2025/05/19-15:45:12.116810 16a0                           Options.min_blob_size: 0
2025/05/19-15:45:12.116816 16a0                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.116821 16a0                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.116826 16a0          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.116832 16a0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.116837 16a0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.116843 16a0          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.116849 16a0                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.116855 16a0         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.116860 16a0            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.117597 16a0               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.117606 16a0           Options.merge_operator: None
2025/05/19-15:45:12.117610 16a0        Options.compaction_filter: None
2025/05/19-15:45:12.117614 16a0        Options.compaction_filter_factory: None
2025/05/19-15:45:12.117618 16a0  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.117622 16a0         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.117626 16a0            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.117658 16a0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB9181B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB405D30
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.117663 16a0        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.117667 16a0  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.117670 16a0          Options.compression: LZ4
2025/05/19-15:45:12.117675 16a0                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.117679 16a0       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.117686 16a0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.117691 16a0             Options.num_levels: 7
2025/05/19-15:45:12.117695 16a0        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.117706 16a0     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.117710 16a0     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.117715 16a0            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.117719 16a0                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.117723 16a0               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.117727 16a0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.117731 16a0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.117735 16a0         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.117740 16a0                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.117744 16a0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.117748 16a0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.117752 16a0            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.117757 16a0                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.117761 16a0               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.117765 16a0         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.117770 16a0         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.117774 16a0         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.117779 16a0         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.117783 16a0                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.117787 16a0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.117791 16a0      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.117795 16a0          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.117799 16a0              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.117803 16a0                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.117807 16a0             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.117811 16a0                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.117815 16a0 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.117821 16a0          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.117826 16a0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.117830 16a0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.117841 16a0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.117846 16a0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.117850 16a0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.117861 16a0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.117866 16a0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.117871 16a0       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.117875 16a0                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.117880 16a0                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.117884 16a0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.117889 16a0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.117893 16a0                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.117898 16a0                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.117903 16a0                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.117910 16a0 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.117915 16a0 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.117920 16a0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.117924 16a0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.117929 16a0 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.117934 16a0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.117939 16a0 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.117943 16a0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.117948 16a0 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.117954 16a0                   Options.table_properties_collectors: 
2025/05/19-15:45:12.117958 16a0                   Options.inplace_update_support: 0
2025/05/19-15:45:12.117962 16a0                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.117968 16a0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.117972 16a0               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.117977 16a0   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.117981 16a0                           Options.bloom_locality: 0
2025/05/19-15:45:12.117985 16a0                    Options.max_successive_merges: 0
2025/05/19-15:45:12.117990 16a0             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.117994 16a0                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.117998 16a0                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.118002 16a0                Options.force_consistency_checks: 1
2025/05/19-15:45:12.118006 16a0                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.118010 16a0                               Options.ttl: 2592000
2025/05/19-15:45:12.118014 16a0          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.118018 16a0                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.118023 16a0  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.118027 16a0    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.118031 16a0                       Options.enable_blob_files: false
2025/05/19-15:45:12.118036 16a0                           Options.min_blob_size: 0
2025/05/19-15:45:12.118041 16a0                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.118045 16a0                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.118058 16a0          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.118063 16a0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.118067 16a0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.118072 16a0          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.118076 16a0                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.118081 16a0         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.118085 16a0            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.141387 16a0 DB pointer 0000023DEB2DF240
