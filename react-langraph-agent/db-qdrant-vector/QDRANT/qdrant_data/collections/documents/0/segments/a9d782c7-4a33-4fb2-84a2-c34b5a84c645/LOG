2025/05/19-15:45:12.075195 580 RocksDB version: 9.9.3
2025/05/19-15:45:12.075270 580 Compile date 2024-12-05 01:25:31
2025/05/19-15:45:12.075295 580 DB SUMMARY
2025/05/19-15:45:12.075319 580 Host name (Env):  VM-IA
2025/05/19-15:45:12.075334 580 DB Session ID:  Q9ASQCKA9G7MN6Q4KJ1E
2025/05/19-15:45:12.075753 580 SST files in ./qdrant_data\collections\documents\0\segments\a9d782c7-4a33-4fb2-84a2-c34b5a84c645 dir, Total Num: 0, files: 
2025/05/19-15:45:12.075782 580 Write Ahead Log file in ./qdrant_data\collections\documents\0\segments\a9d782c7-4a33-4fb2-84a2-c34b5a84c645: 
2025/05/19-15:45:12.075808 580                         Options.error_if_exists: 0
2025/05/19-15:45:12.075824 580                       Options.create_if_missing: 1
2025/05/19-15:45:12.075840 580                         Options.paranoid_checks: 1
2025/05/19-15:45:12.076070 580             Options.flush_verify_memtable_count: 1
2025/05/19-15:45:12.076078 580          Options.compaction_verify_record_count: 1
2025/05/19-15:45:12.076083 580                               Options.track_and_verify_wals_in_manifest: 0
2025/05/19-15:45:12.076088 580        Options.verify_sst_unique_id_in_manifest: 1
2025/05/19-15:45:12.076093 580                                     Options.env: 0000023DE8BA8DE0
2025/05/19-15:45:12.076098 580                                      Options.fs: WinFS
2025/05/19-15:45:12.076103 580                                Options.info_log: 0000023DEA989350
2025/05/19-15:45:12.076107 580                Options.max_file_opening_threads: 16
2025/05/19-15:45:12.076111 580                              Options.statistics: 0000000000000000
2025/05/19-15:45:12.076115 580                               Options.use_fsync: 0
2025/05/19-15:45:12.076120 580                       Options.max_log_file_size: 1048576
2025/05/19-15:45:12.076127 580                  Options.max_manifest_file_size: 1073741824
2025/05/19-15:45:12.076131 580                   Options.log_file_time_to_roll: 0
2025/05/19-15:45:12.076135 580                       Options.keep_log_file_num: 1
2025/05/19-15:45:12.076140 580                    Options.recycle_log_file_num: 0
2025/05/19-15:45:12.076144 580                         Options.allow_fallocate: 1
2025/05/19-15:45:12.076149 580                        Options.allow_mmap_reads: 0
2025/05/19-15:45:12.076153 580                       Options.allow_mmap_writes: 0
2025/05/19-15:45:12.076157 580                        Options.use_direct_reads: 0
2025/05/19-15:45:12.076162 580                        Options.use_direct_io_for_flush_and_compaction: 0
2025/05/19-15:45:12.076166 580          Options.create_missing_column_families: 1
2025/05/19-15:45:12.076171 580                              Options.db_log_dir: 
2025/05/19-15:45:12.076175 580                                 Options.wal_dir: 
2025/05/19-15:45:12.076179 580                Options.table_cache_numshardbits: 6
2025/05/19-15:45:12.076184 580                         Options.WAL_ttl_seconds: 0
2025/05/19-15:45:12.076188 580                       Options.WAL_size_limit_MB: 0
2025/05/19-15:45:12.076193 580                        Options.max_write_batch_group_size_bytes: 1048576
2025/05/19-15:45:12.076197 580             Options.manifest_preallocation_size: 4194304
2025/05/19-15:45:12.076201 580                     Options.is_fd_close_on_exec: 1
2025/05/19-15:45:12.076206 580                   Options.advise_random_on_open: 1
2025/05/19-15:45:12.076210 580                    Options.db_write_buffer_size: 0
2025/05/19-15:45:12.076214 580                    Options.write_buffer_manager: 0000023DEB62E610
2025/05/19-15:45:12.076219 580           Options.random_access_max_buffer_size: 1048576
2025/05/19-15:45:12.076223 580                      Options.use_adaptive_mutex: 0
2025/05/19-15:45:12.076228 580                            Options.rate_limiter: 0000000000000000
2025/05/19-15:45:12.076232 580     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/05/19-15:45:12.076237 580                       Options.wal_recovery_mode: 0
2025/05/19-15:45:12.076241 580                  Options.enable_thread_tracking: 0
2025/05/19-15:45:12.076279 580                  Options.enable_pipelined_write: 0
2025/05/19-15:45:12.076285 580                  Options.unordered_write: 0
2025/05/19-15:45:12.076290 580         Options.allow_concurrent_memtable_write: 1
2025/05/19-15:45:12.076294 580      Options.enable_write_thread_adaptive_yield: 1
2025/05/19-15:45:12.076299 580             Options.write_thread_max_yield_usec: 100
2025/05/19-15:45:12.076307 580            Options.write_thread_slow_yield_usec: 3
2025/05/19-15:45:12.076311 580                               Options.row_cache: None
2025/05/19-15:45:12.076316 580                              Options.wal_filter: None
2025/05/19-15:45:12.076320 580             Options.avoid_flush_during_recovery: 0
2025/05/19-15:45:12.076324 580             Options.allow_ingest_behind: 0
2025/05/19-15:45:12.076328 580             Options.two_write_queues: 0
2025/05/19-15:45:12.076333 580             Options.manual_wal_flush: 0
2025/05/19-15:45:12.076337 580             Options.wal_compression: 0
2025/05/19-15:45:12.076341 580             Options.background_close_inactive_wals: 0
2025/05/19-15:45:12.076345 580             Options.atomic_flush: 0
2025/05/19-15:45:12.076350 580             Options.avoid_unnecessary_blocking_io: 0
2025/05/19-15:45:12.076354 580             Options.prefix_seek_opt_in_only: 0
2025/05/19-15:45:12.076357 580                 Options.persist_stats_to_disk: 0
2025/05/19-15:45:12.076362 580                 Options.write_dbid_to_manifest: 1
2025/05/19-15:45:12.076366 580                 Options.write_identity_file: 1
2025/05/19-15:45:12.076370 580                 Options.log_readahead_size: 0
2025/05/19-15:45:12.076375 580                 Options.file_checksum_gen_factory: Unknown
2025/05/19-15:45:12.076379 580                 Options.best_efforts_recovery: 0
2025/05/19-15:45:12.076384 580                Options.max_bgerror_resume_count: 2147483647
2025/05/19-15:45:12.076388 580            Options.bgerror_resume_retry_interval: 1000000
2025/05/19-15:45:12.076392 580             Options.allow_data_in_errors: 0
2025/05/19-15:45:12.076397 580             Options.db_host_id: __hostname__
2025/05/19-15:45:12.076401 580             Options.enforce_single_del_contracts: true
2025/05/19-15:45:12.076406 580             Options.metadata_write_temperature: kUnknown
2025/05/19-15:45:12.076410 580             Options.wal_write_temperature: kUnknown
2025/05/19-15:45:12.076414 580             Options.max_background_jobs: 2
2025/05/19-15:45:12.076419 580             Options.max_background_compactions: -1
2025/05/19-15:45:12.076423 580             Options.max_subcompactions: 1
2025/05/19-15:45:12.076427 580             Options.avoid_flush_during_shutdown: 0
2025/05/19-15:45:12.076431 580           Options.writable_file_max_buffer_size: 1048576
2025/05/19-15:45:12.076435 580             Options.delayed_write_rate : 16777216
2025/05/19-15:45:12.076439 580             Options.max_total_wal_size: 0
2025/05/19-15:45:12.076443 580             Options.delete_obsolete_files_period_micros: 180000000
2025/05/19-15:45:12.076447 580                   Options.stats_dump_period_sec: 600
2025/05/19-15:45:12.076451 580                 Options.stats_persist_period_sec: 600
2025/05/19-15:45:12.076455 580                 Options.stats_history_buffer_size: 1048576
2025/05/19-15:45:12.076459 580                          Options.max_open_files: 256
2025/05/19-15:45:12.076463 580                          Options.bytes_per_sync: 0
2025/05/19-15:45:12.076467 580                      Options.wal_bytes_per_sync: 0
2025/05/19-15:45:12.076471 580                   Options.strict_bytes_per_sync: 0
2025/05/19-15:45:12.076475 580       Options.compaction_readahead_size: 2097152
2025/05/19-15:45:12.076479 580                  Options.max_background_flushes: -1
2025/05/19-15:45:12.076483 580 Options.daily_offpeak_time_utc: 
2025/05/19-15:45:12.076487 580 Compression algorithms supported:
2025/05/19-15:45:12.076491 580 	kZSTD supported: 0
2025/05/19-15:45:12.076496 580 	kSnappyCompression supported: 1
2025/05/19-15:45:12.076500 580 	kBZip2Compression supported: 0
2025/05/19-15:45:12.076531 580 	kZlibCompression supported: 0
2025/05/19-15:45:12.076536 580 	kLZ4Compression supported: 1
2025/05/19-15:45:12.076540 580 	kXpressCompression supported: 0
2025/05/19-15:45:12.076544 580 	kLZ4HCCompression supported: 1
2025/05/19-15:45:12.076548 580 	kZSTDNotFinalCompression supported: 0
2025/05/19-15:45:12.076552 580 Fast CRC32 supported: Not supported on x86
2025/05/19-15:45:12.076556 580 DMutex implementation: std::mutex
2025/05/19-15:45:12.076560 580 Jemalloc supported: 0
2025/05/19-15:45:12.101748 580               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.101761 580           Options.merge_operator: None
2025/05/19-15:45:12.101765 580        Options.compaction_filter: None
2025/05/19-15:45:12.101768 580        Options.compaction_filter_factory: None
2025/05/19-15:45:12.101772 580  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.101776 580         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.101780 580            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.101810 580            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB3F91D0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB953700
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.101815 580        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.101819 580  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.101824 580          Options.compression: LZ4
2025/05/19-15:45:12.101828 580                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.101833 580       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.101837 580   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.101841 580             Options.num_levels: 7
2025/05/19-15:45:12.101846 580        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.101850 580     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.101854 580     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.101858 580            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.101862 580                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.101866 580               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.101871 580         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.101875 580         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.101879 580         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.101884 580                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.101889 580         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.101893 580         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.101898 580            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.101902 580                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.101909 580               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.101915 580         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.101920 580         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.101924 580         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.101929 580         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.101933 580                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.101937 580         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.101942 580      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.101946 580          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.101950 580              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.101955 580                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.101960 580             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.101964 580                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.101968 580 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.101973 580          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.101978 580 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.101982 580 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.101987 580 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.101991 580 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.101995 580 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.101999 580 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.102004 580 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.102008 580       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.102013 580                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.102017 580                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.102021 580   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.102026 580   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.102030 580                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.102039 580                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.102044 580                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.102048 580 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.102052 580 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.102057 580 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.102062 580 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.102066 580 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.102071 580 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.102077 580 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.102081 580 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.102086 580 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.102092 580                   Options.table_properties_collectors: 
2025/05/19-15:45:12.102096 580                   Options.inplace_update_support: 0
2025/05/19-15:45:12.102101 580                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.102105 580               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.102110 580               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.102114 580   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.102119 580                           Options.bloom_locality: 0
2025/05/19-15:45:12.102123 580                    Options.max_successive_merges: 0
2025/05/19-15:45:12.102195 580             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.102201 580                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.102205 580                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.102209 580                Options.force_consistency_checks: 1
2025/05/19-15:45:12.102213 580                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.102217 580                               Options.ttl: 2592000
2025/05/19-15:45:12.102221 580          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.102225 580                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.102229 580  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.102233 580    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.102237 580                       Options.enable_blob_files: false
2025/05/19-15:45:12.102241 580                           Options.min_blob_size: 0
2025/05/19-15:45:12.102245 580                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.102250 580                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.102254 580          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.102259 580      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.102264 580 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.102269 580          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.102273 580                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.102277 580         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.102282 580            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.115077 580               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.115092 580           Options.merge_operator: None
2025/05/19-15:45:12.115096 580        Options.compaction_filter: None
2025/05/19-15:45:12.115101 580        Options.compaction_filter_factory: None
2025/05/19-15:45:12.115105 580  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.115110 580         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.115114 580            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.115143 580            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB3F91D0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB953700
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.115149 580        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.115153 580  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.115157 580          Options.compression: LZ4
2025/05/19-15:45:12.115162 580                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.115166 580       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.115171 580   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.115175 580             Options.num_levels: 7
2025/05/19-15:45:12.115183 580        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.115189 580     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.115193 580     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.115199 580            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.115204 580                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.115209 580               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.115213 580         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.115218 580         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.115222 580         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.115227 580                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.115232 580         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.115236 580         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.115241 580            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.115245 580                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.115249 580               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.115254 580         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.115258 580         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.115263 580         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.115267 580         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.115272 580                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.115276 580         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.115281 580      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.115285 580          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.115289 580              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.115294 580                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.115298 580             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.115303 580                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.115307 580 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.115312 580          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.115325 580 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.115329 580 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.115334 580 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.115338 580 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.115342 580 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.115347 580 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.115351 580 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.115355 580       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.115359 580                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.115364 580                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.115369 580   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.115373 580   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.115378 580                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.115383 580                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.115388 580                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.115392 580 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.115396 580 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.115401 580 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.115407 580 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.115413 580 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.115418 580 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.115422 580 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.115427 580 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.115431 580 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.115443 580                   Options.table_properties_collectors: 
2025/05/19-15:45:12.115447 580                   Options.inplace_update_support: 0
2025/05/19-15:45:12.115452 580                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.115457 580               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.115461 580               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.115466 580   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.115470 580                           Options.bloom_locality: 0
2025/05/19-15:45:12.115474 580                    Options.max_successive_merges: 0
2025/05/19-15:45:12.115479 580             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.115483 580                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.115487 580                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.115492 580                Options.force_consistency_checks: 1
2025/05/19-15:45:12.115502 580                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.115506 580                               Options.ttl: 2592000
2025/05/19-15:45:12.115510 580          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.115514 580                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.115518 580  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.115522 580    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.115526 580                       Options.enable_blob_files: false
2025/05/19-15:45:12.115530 580                           Options.min_blob_size: 0
2025/05/19-15:45:12.115534 580                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.115539 580                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.115543 580          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.115548 580      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.115552 580 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.115556 580          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.115560 580                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.115564 580         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.115568 580            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.116500 580               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.116507 580           Options.merge_operator: None
2025/05/19-15:45:12.116511 580        Options.compaction_filter: None
2025/05/19-15:45:12.116516 580        Options.compaction_filter_factory: None
2025/05/19-15:45:12.116520 580  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.116524 580         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.116528 580            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.116558 580            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB3F91D0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB953700
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.116567 580        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.116573 580  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.116577 580          Options.compression: LZ4
2025/05/19-15:45:12.116581 580                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.116585 580       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.116589 580   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.116594 580             Options.num_levels: 7
2025/05/19-15:45:12.116598 580        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.116602 580     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.116606 580     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.116611 580            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.116615 580                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.116619 580               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.116623 580         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.116628 580         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.116632 580         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.116637 580                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.116641 580         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.116646 580         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.116650 580            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.116654 580                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.116659 580               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.116664 580         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.116669 580         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.116674 580         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.116678 580         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.116682 580                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.116686 580         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.116691 580      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.116695 580          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.116699 580              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.116703 580                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.116708 580             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.116713 580                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.116718 580 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.116723 580          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.116728 580 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.116733 580 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.116743 580 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.116748 580 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.116797 580 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.116803 580 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.116808 580 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.116812 580       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.116817 580                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.116821 580                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.116826 580   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.116831 580   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.116835 580                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.116840 580                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.116845 580                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.116849 580 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.116854 580 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.116858 580 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.116862 580 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.116867 580 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.116872 580 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.116876 580 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.116881 580 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.116885 580 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.116951 580                   Options.table_properties_collectors: 
2025/05/19-15:45:12.116957 580                   Options.inplace_update_support: 0
2025/05/19-15:45:12.116962 580                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.116967 580               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.116971 580               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.116976 580   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.116980 580                           Options.bloom_locality: 0
2025/05/19-15:45:12.116984 580                    Options.max_successive_merges: 0
2025/05/19-15:45:12.116989 580             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.116993 580                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.116998 580                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.117002 580                Options.force_consistency_checks: 1
2025/05/19-15:45:12.117010 580                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.117016 580                               Options.ttl: 2592000
2025/05/19-15:45:12.117022 580          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.117029 580                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.117034 580  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.117038 580    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.117042 580                       Options.enable_blob_files: false
2025/05/19-15:45:12.117046 580                           Options.min_blob_size: 0
2025/05/19-15:45:12.117050 580                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.117054 580                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.117058 580          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.117063 580      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.117067 580 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.117071 580          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.117076 580                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.117082 580         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.117094 580            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.117957 580               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.117969 580           Options.merge_operator: None
2025/05/19-15:45:12.117973 580        Options.compaction_filter: None
2025/05/19-15:45:12.117977 580        Options.compaction_filter_factory: None
2025/05/19-15:45:12.117982 580  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.117986 580         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.117990 580            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.118022 580            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB3F91D0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB953700
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.118029 580        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.118033 580  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.118038 580          Options.compression: LZ4
2025/05/19-15:45:12.118043 580                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.118047 580       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.118051 580   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.118056 580             Options.num_levels: 7
2025/05/19-15:45:12.118060 580        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.118064 580     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.118069 580     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.118074 580            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.118078 580                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.118083 580               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.118087 580         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.118092 580         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.118096 580         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.118101 580                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.118106 580         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.118110 580         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.118115 580            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.118119 580                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.118123 580               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.118128 580         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.118132 580         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.118137 580         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.118145 580         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.118159 580                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.118164 580         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.118168 580      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.118172 580          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.118177 580              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.118181 580                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.118185 580             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.118189 580                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.118194 580 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.118199 580          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.118203 580 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.118208 580 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.118212 580 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.118216 580 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.118221 580 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.118225 580 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.118230 580 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.118234 580       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.118239 580                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.118243 580                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.118248 580   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.118252 580   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.118256 580                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.118261 580                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.118266 580                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.118270 580 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.118274 580 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.118278 580 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.118283 580 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.118287 580 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.118291 580 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.118295 580 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.118299 580 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.118303 580 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.118308 580                   Options.table_properties_collectors: 
2025/05/19-15:45:12.118312 580                   Options.inplace_update_support: 0
2025/05/19-15:45:12.118316 580                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.118320 580               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.118325 580               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.118328 580   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.118332 580                           Options.bloom_locality: 0
2025/05/19-15:45:12.118336 580                    Options.max_successive_merges: 0
2025/05/19-15:45:12.118340 580             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.118344 580                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.118348 580                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.118352 580                Options.force_consistency_checks: 1
2025/05/19-15:45:12.118358 580                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.118363 580                               Options.ttl: 2592000
2025/05/19-15:45:12.118367 580          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.118371 580                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.118375 580  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.118379 580    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.118383 580                       Options.enable_blob_files: false
2025/05/19-15:45:12.118387 580                           Options.min_blob_size: 0
2025/05/19-15:45:12.118391 580                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.118395 580                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.118399 580          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.118403 580      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.118408 580 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.118412 580          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.118416 580                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.118420 580         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.118424 580            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.119350 580               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.119360 580           Options.merge_operator: None
2025/05/19-15:45:12.119364 580        Options.compaction_filter: None
2025/05/19-15:45:12.119369 580        Options.compaction_filter_factory: None
2025/05/19-15:45:12.119373 580  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.119378 580         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.119384 580            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.119423 580            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB3F91D0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB953700
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.119429 580        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.119433 580  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.119439 580          Options.compression: LZ4
2025/05/19-15:45:12.119445 580                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.119451 580       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.119458 580   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.119465 580             Options.num_levels: 7
2025/05/19-15:45:12.119471 580        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.119477 580     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.119482 580     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.119486 580            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.119496 580                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.119505 580               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.119509 580         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.119514 580         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.119518 580         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.119523 580                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.119528 580         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.119532 580         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.119537 580            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.119541 580                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.119545 580               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.119550 580         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.119554 580         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.119559 580         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.119563 580         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.119567 580                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.119572 580         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.119576 580      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.119580 580          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.119584 580              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.119589 580                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.119593 580             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.119598 580                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.119605 580 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.119610 580          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.119617 580 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.119622 580 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.119626 580 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.119630 580 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.119634 580 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.119638 580 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.119641 580 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.119647 580       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.119653 580                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.119658 580                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.119662 580   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.119666 580   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.119670 580                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.119674 580                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.119680 580                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.119686 580 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.119690 580 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.119695 580 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.119699 580 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.119704 580 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.119708 580 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.119714 580 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.119719 580 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.119723 580 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.119758 580                   Options.table_properties_collectors: 
2025/05/19-15:45:12.119763 580                   Options.inplace_update_support: 0
2025/05/19-15:45:12.119768 580                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.119772 580               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.119777 580               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.119781 580   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.119785 580                           Options.bloom_locality: 0
2025/05/19-15:45:12.119788 580                    Options.max_successive_merges: 0
2025/05/19-15:45:12.119793 580             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.119797 580                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.119800 580                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.119804 580                Options.force_consistency_checks: 1
2025/05/19-15:45:12.119808 580                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.119812 580                               Options.ttl: 2592000
2025/05/19-15:45:12.119817 580          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.119821 580                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.119827 580  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.119831 580    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.119835 580                       Options.enable_blob_files: false
2025/05/19-15:45:12.119839 580                           Options.min_blob_size: 0
2025/05/19-15:45:12.119843 580                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.119848 580                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.119857 580          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.119863 580      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.119868 580 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.119872 580          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.119876 580                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.119880 580         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.119884 580            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.138339 580 DB pointer 0000023DEB2E2C40
