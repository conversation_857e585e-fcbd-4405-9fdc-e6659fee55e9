2025/05/19-15:45:12.075179 1334 RocksDB version: 9.9.3
2025/05/19-15:45:12.075231 1334 Compile date 2024-12-05 01:25:31
2025/05/19-15:45:12.075248 1334 DB SUMMARY
2025/05/19-15:45:12.075260 1334 Host name (Env):  VM-IA
2025/05/19-15:45:12.075271 1334 DB Session ID:  Q9ASQCKA9G7MN6Q4KJ1F
2025/05/19-15:45:12.075655 1334 SST files in ./qdrant_data\collections\documents\0\segments\034004e8-2d45-451d-b6a8-9fe6dfb86377 dir, Total Num: 0, files: 
2025/05/19-15:45:12.075672 1334 Write Ahead Log file in ./qdrant_data\collections\documents\0\segments\034004e8-2d45-451d-b6a8-9fe6dfb86377: 
2025/05/19-15:45:12.075685 1334                         Options.error_if_exists: 0
2025/05/19-15:45:12.075698 1334                       Options.create_if_missing: 1
2025/05/19-15:45:12.075923 1334                         Options.paranoid_checks: 1
2025/05/19-15:45:12.075931 1334             Options.flush_verify_memtable_count: 1
2025/05/19-15:45:12.075935 1334          Options.compaction_verify_record_count: 1
2025/05/19-15:45:12.075940 1334                               Options.track_and_verify_wals_in_manifest: 0
2025/05/19-15:45:12.075944 1334        Options.verify_sst_unique_id_in_manifest: 1
2025/05/19-15:45:12.075949 1334                                     Options.env: 0000023DE8BA8DE0
2025/05/19-15:45:12.075954 1334                                      Options.fs: WinFS
2025/05/19-15:45:12.075958 1334                                Options.info_log: 0000023DEA98A6D0
2025/05/19-15:45:12.075963 1334                Options.max_file_opening_threads: 16
2025/05/19-15:45:12.075967 1334                              Options.statistics: 0000000000000000
2025/05/19-15:45:12.075972 1334                               Options.use_fsync: 0
2025/05/19-15:45:12.075976 1334                       Options.max_log_file_size: 1048576
2025/05/19-15:45:12.075981 1334                  Options.max_manifest_file_size: 1073741824
2025/05/19-15:45:12.075985 1334                   Options.log_file_time_to_roll: 0
2025/05/19-15:45:12.075989 1334                       Options.keep_log_file_num: 1
2025/05/19-15:45:12.075994 1334                    Options.recycle_log_file_num: 0
2025/05/19-15:45:12.075998 1334                         Options.allow_fallocate: 1
2025/05/19-15:45:12.076002 1334                        Options.allow_mmap_reads: 0
2025/05/19-15:45:12.076006 1334                       Options.allow_mmap_writes: 0
2025/05/19-15:45:12.076010 1334                        Options.use_direct_reads: 0
2025/05/19-15:45:12.076014 1334                        Options.use_direct_io_for_flush_and_compaction: 0
2025/05/19-15:45:12.076018 1334          Options.create_missing_column_families: 1
2025/05/19-15:45:12.076022 1334                              Options.db_log_dir: 
2025/05/19-15:45:12.076026 1334                                 Options.wal_dir: 
2025/05/19-15:45:12.076030 1334                Options.table_cache_numshardbits: 6
2025/05/19-15:45:12.076035 1334                         Options.WAL_ttl_seconds: 0
2025/05/19-15:45:12.076039 1334                       Options.WAL_size_limit_MB: 0
2025/05/19-15:45:12.076044 1334                        Options.max_write_batch_group_size_bytes: 1048576
2025/05/19-15:45:12.076048 1334             Options.manifest_preallocation_size: 4194304
2025/05/19-15:45:12.076053 1334                     Options.is_fd_close_on_exec: 1
2025/05/19-15:45:12.076057 1334                   Options.advise_random_on_open: 1
2025/05/19-15:45:12.076061 1334                    Options.db_write_buffer_size: 0
2025/05/19-15:45:12.076066 1334                    Options.write_buffer_manager: 0000023DEB609A30
2025/05/19-15:45:12.076070 1334           Options.random_access_max_buffer_size: 1048576
2025/05/19-15:45:12.076074 1334                      Options.use_adaptive_mutex: 0
2025/05/19-15:45:12.076079 1334                            Options.rate_limiter: 0000000000000000
2025/05/19-15:45:12.076083 1334     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/05/19-15:45:12.076088 1334                       Options.wal_recovery_mode: 0
2025/05/19-15:45:12.076093 1334                  Options.enable_thread_tracking: 0
2025/05/19-15:45:12.076127 1334                  Options.enable_pipelined_write: 0
2025/05/19-15:45:12.076133 1334                  Options.unordered_write: 0
2025/05/19-15:45:12.076138 1334         Options.allow_concurrent_memtable_write: 1
2025/05/19-15:45:12.076142 1334      Options.enable_write_thread_adaptive_yield: 1
2025/05/19-15:45:12.076146 1334             Options.write_thread_max_yield_usec: 100
2025/05/19-15:45:12.076151 1334            Options.write_thread_slow_yield_usec: 3
2025/05/19-15:45:12.076155 1334                               Options.row_cache: None
2025/05/19-15:45:12.076160 1334                              Options.wal_filter: None
2025/05/19-15:45:12.076164 1334             Options.avoid_flush_during_recovery: 0
2025/05/19-15:45:12.076169 1334             Options.allow_ingest_behind: 0
2025/05/19-15:45:12.076173 1334             Options.two_write_queues: 0
2025/05/19-15:45:12.076177 1334             Options.manual_wal_flush: 0
2025/05/19-15:45:12.076181 1334             Options.wal_compression: 0
2025/05/19-15:45:12.076186 1334             Options.background_close_inactive_wals: 0
2025/05/19-15:45:12.076190 1334             Options.atomic_flush: 0
2025/05/19-15:45:12.076194 1334             Options.avoid_unnecessary_blocking_io: 0
2025/05/19-15:45:12.076199 1334             Options.prefix_seek_opt_in_only: 0
2025/05/19-15:45:12.076203 1334                 Options.persist_stats_to_disk: 0
2025/05/19-15:45:12.076207 1334                 Options.write_dbid_to_manifest: 1
2025/05/19-15:45:12.076212 1334                 Options.write_identity_file: 1
2025/05/19-15:45:12.076216 1334                 Options.log_readahead_size: 0
2025/05/19-15:45:12.076220 1334                 Options.file_checksum_gen_factory: Unknown
2025/05/19-15:45:12.076226 1334                 Options.best_efforts_recovery: 0
2025/05/19-15:45:12.076231 1334                Options.max_bgerror_resume_count: 2147483647
2025/05/19-15:45:12.076235 1334            Options.bgerror_resume_retry_interval: 1000000
2025/05/19-15:45:12.076239 1334             Options.allow_data_in_errors: 0
2025/05/19-15:45:12.076244 1334             Options.db_host_id: __hostname__
2025/05/19-15:45:12.076248 1334             Options.enforce_single_del_contracts: true
2025/05/19-15:45:12.076252 1334             Options.metadata_write_temperature: kUnknown
2025/05/19-15:45:12.076257 1334             Options.wal_write_temperature: kUnknown
2025/05/19-15:45:12.076261 1334             Options.max_background_jobs: 2
2025/05/19-15:45:12.076265 1334             Options.max_background_compactions: -1
2025/05/19-15:45:12.076269 1334             Options.max_subcompactions: 1
2025/05/19-15:45:12.076273 1334             Options.avoid_flush_during_shutdown: 0
2025/05/19-15:45:12.076277 1334           Options.writable_file_max_buffer_size: 1048576
2025/05/19-15:45:12.076281 1334             Options.delayed_write_rate : 16777216
2025/05/19-15:45:12.076286 1334             Options.max_total_wal_size: 0
2025/05/19-15:45:12.076290 1334             Options.delete_obsolete_files_period_micros: 180000000
2025/05/19-15:45:12.076295 1334                   Options.stats_dump_period_sec: 600
2025/05/19-15:45:12.076299 1334                 Options.stats_persist_period_sec: 600
2025/05/19-15:45:12.076303 1334                 Options.stats_history_buffer_size: 1048576
2025/05/19-15:45:12.076308 1334                          Options.max_open_files: 256
2025/05/19-15:45:12.076312 1334                          Options.bytes_per_sync: 0
2025/05/19-15:45:12.076316 1334                      Options.wal_bytes_per_sync: 0
2025/05/19-15:45:12.076321 1334                   Options.strict_bytes_per_sync: 0
2025/05/19-15:45:12.076325 1334       Options.compaction_readahead_size: 2097152
2025/05/19-15:45:12.076329 1334                  Options.max_background_flushes: -1
2025/05/19-15:45:12.076334 1334 Options.daily_offpeak_time_utc: 
2025/05/19-15:45:12.076338 1334 Compression algorithms supported:
2025/05/19-15:45:12.076343 1334 	kZSTD supported: 0
2025/05/19-15:45:12.076373 1334 	kSnappyCompression supported: 1
2025/05/19-15:45:12.076379 1334 	kBZip2Compression supported: 0
2025/05/19-15:45:12.076383 1334 	kZlibCompression supported: 0
2025/05/19-15:45:12.076387 1334 	kLZ4Compression supported: 1
2025/05/19-15:45:12.076391 1334 	kXpressCompression supported: 0
2025/05/19-15:45:12.076396 1334 	kLZ4HCCompression supported: 1
2025/05/19-15:45:12.076400 1334 	kZSTDNotFinalCompression supported: 0
2025/05/19-15:45:12.076405 1334 Fast CRC32 supported: Not supported on x86
2025/05/19-15:45:12.076409 1334 DMutex implementation: std::mutex
2025/05/19-15:45:12.076413 1334 Jemalloc supported: 0
2025/05/19-15:45:12.101803 1334               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.101816 1334           Options.merge_operator: None
2025/05/19-15:45:12.101821 1334        Options.compaction_filter: None
2025/05/19-15:45:12.101826 1334        Options.compaction_filter_factory: None
2025/05/19-15:45:12.101830 1334  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.101834 1334         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.101839 1334            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.101868 1334            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB3F9500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB953920
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.101873 1334        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.101878 1334  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.101882 1334          Options.compression: LZ4
2025/05/19-15:45:12.101887 1334                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.101891 1334       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.101895 1334   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.101900 1334             Options.num_levels: 7
2025/05/19-15:45:12.101904 1334        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.101909 1334     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.101913 1334     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.101918 1334            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.101922 1334                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.101927 1334               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.101931 1334         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.101935 1334         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.101940 1334         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.101944 1334                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.101949 1334         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.101954 1334         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.101962 1334            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.101968 1334                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.101972 1334               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.101977 1334         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.101981 1334         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.101986 1334         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.101990 1334         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.101994 1334                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.101999 1334         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.102003 1334      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.102007 1334          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.102012 1334              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.102016 1334                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.102020 1334             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.102025 1334                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.102044 1334 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.102051 1334          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.102057 1334 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.102061 1334 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.102066 1334 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.102070 1334 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.102074 1334 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.102079 1334 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.102083 1334 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.102087 1334       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.102092 1334                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.102096 1334                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.102100 1334   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.102104 1334   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.102109 1334                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.102114 1334                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.102120 1334                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.102124 1334 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.102128 1334 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.102132 1334 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.102137 1334 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.102141 1334 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.102152 1334 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.102157 1334 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.102161 1334 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.102165 1334 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.102171 1334                   Options.table_properties_collectors: 
2025/05/19-15:45:12.102175 1334                   Options.inplace_update_support: 0
2025/05/19-15:45:12.102179 1334                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.102183 1334               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.102188 1334               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.102192 1334   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.102243 1334                           Options.bloom_locality: 0
2025/05/19-15:45:12.102249 1334                    Options.max_successive_merges: 0
2025/05/19-15:45:12.102254 1334             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.102258 1334                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.102263 1334                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.102267 1334                Options.force_consistency_checks: 1
2025/05/19-15:45:12.102271 1334                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.102276 1334                               Options.ttl: 2592000
2025/05/19-15:45:12.102280 1334          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.102285 1334                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.102289 1334  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.102293 1334    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.102297 1334                       Options.enable_blob_files: false
2025/05/19-15:45:12.102301 1334                           Options.min_blob_size: 0
2025/05/19-15:45:12.102305 1334                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.102309 1334                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.102313 1334          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.102318 1334      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.102322 1334 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.102327 1334          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.102331 1334                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.102335 1334         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.102339 1334            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.113155 1334               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.113169 1334           Options.merge_operator: None
2025/05/19-15:45:12.113173 1334        Options.compaction_filter: None
2025/05/19-15:45:12.113177 1334        Options.compaction_filter_factory: None
2025/05/19-15:45:12.113181 1334  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.113185 1334         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.113189 1334            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.113244 1334            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB3F9500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB953920
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.113250 1334        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.113254 1334  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.113259 1334          Options.compression: LZ4
2025/05/19-15:45:12.113262 1334                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.113269 1334       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.113275 1334   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.113279 1334             Options.num_levels: 7
2025/05/19-15:45:12.113283 1334        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.113287 1334     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.113291 1334     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.113295 1334            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.113300 1334                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.113304 1334               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.113308 1334         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.113312 1334         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.113316 1334         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.113320 1334                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.113324 1334         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.113328 1334         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.113332 1334            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.113336 1334                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.113340 1334               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.113344 1334         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.113348 1334         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.113353 1334         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.113357 1334         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.113362 1334                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.113366 1334         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.113370 1334      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.113374 1334          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.113378 1334              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.113382 1334                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.113387 1334             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.113392 1334                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.113396 1334 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.113400 1334          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.113411 1334 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.113415 1334 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.113419 1334 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.113423 1334 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.113426 1334 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.113430 1334 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.113434 1334 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.113438 1334       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.113442 1334                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.113446 1334                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.113450 1334   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.113454 1334   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.113458 1334                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.113463 1334                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.113468 1334                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.113474 1334 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.113478 1334 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.113483 1334 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.113487 1334 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.113492 1334 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.113497 1334 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.113501 1334 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.113505 1334 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.113509 1334 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.113516 1334                   Options.table_properties_collectors: 
2025/05/19-15:45:12.113520 1334                   Options.inplace_update_support: 0
2025/05/19-15:45:12.113524 1334                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.113528 1334               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.113532 1334               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.113536 1334   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.113540 1334                           Options.bloom_locality: 0
2025/05/19-15:45:12.113544 1334                    Options.max_successive_merges: 0
2025/05/19-15:45:12.113548 1334             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.113552 1334                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.113556 1334                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.113559 1334                Options.force_consistency_checks: 1
2025/05/19-15:45:12.113563 1334                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.113567 1334                               Options.ttl: 2592000
2025/05/19-15:45:12.113571 1334          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.113576 1334                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.113580 1334  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.113584 1334    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.113587 1334                       Options.enable_blob_files: false
2025/05/19-15:45:12.113592 1334                           Options.min_blob_size: 0
2025/05/19-15:45:12.113596 1334                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.113600 1334                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.113604 1334          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.113608 1334      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.113613 1334 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.113617 1334          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.113621 1334                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.113625 1334         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.113629 1334            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.114672 1334               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.114686 1334           Options.merge_operator: None
2025/05/19-15:45:12.114691 1334        Options.compaction_filter: None
2025/05/19-15:45:12.114695 1334        Options.compaction_filter_factory: None
2025/05/19-15:45:12.114699 1334  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.114704 1334         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.114708 1334            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.114741 1334            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB3F9500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB953920
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.114749 1334        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.114755 1334  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.114759 1334          Options.compression: LZ4
2025/05/19-15:45:12.114764 1334                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.114768 1334       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.114772 1334   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.114776 1334             Options.num_levels: 7
2025/05/19-15:45:12.114780 1334        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.114784 1334     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.114789 1334     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.114793 1334            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.114798 1334                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.114802 1334               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.114806 1334         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.114810 1334         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.114815 1334         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.114819 1334                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.114823 1334         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.114828 1334         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.114832 1334            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.114836 1334                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.114840 1334               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.114845 1334         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.114849 1334         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.114853 1334         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.114857 1334         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.114869 1334                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.114874 1334         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.114878 1334      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.114883 1334          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.114887 1334              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.114891 1334                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.114895 1334             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.114899 1334                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.114904 1334 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.114909 1334          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.114965 1334 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.114971 1334 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.114975 1334 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.114980 1334 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.114984 1334 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.114989 1334 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.114993 1334 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.114998 1334       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.115002 1334                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.115007 1334                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.115012 1334   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.115017 1334   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.115021 1334                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.115027 1334                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.115032 1334                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.115045 1334 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.115049 1334 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.115054 1334 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.115059 1334 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.115064 1334 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.115069 1334 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.115074 1334 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.115078 1334 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.115083 1334 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.115090 1334                   Options.table_properties_collectors: 
2025/05/19-15:45:12.115095 1334                   Options.inplace_update_support: 0
2025/05/19-15:45:12.115099 1334                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.115104 1334               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.115109 1334               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.115114 1334   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.115119 1334                           Options.bloom_locality: 0
2025/05/19-15:45:12.115126 1334                    Options.max_successive_merges: 0
2025/05/19-15:45:12.115131 1334             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.115136 1334                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.115140 1334                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.115144 1334                Options.force_consistency_checks: 1
2025/05/19-15:45:12.115149 1334                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.115153 1334                               Options.ttl: 2592000
2025/05/19-15:45:12.115158 1334          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.115163 1334                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.115168 1334  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.115172 1334    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.115177 1334                       Options.enable_blob_files: false
2025/05/19-15:45:12.115181 1334                           Options.min_blob_size: 0
2025/05/19-15:45:12.115186 1334                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.115191 1334                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.115197 1334          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.115203 1334      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.115209 1334 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.115214 1334          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.115219 1334                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.115223 1334         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.115228 1334            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.116048 1334               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.116058 1334           Options.merge_operator: None
2025/05/19-15:45:12.116062 1334        Options.compaction_filter: None
2025/05/19-15:45:12.116065 1334        Options.compaction_filter_factory: None
2025/05/19-15:45:12.116069 1334  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.116073 1334         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.116077 1334            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.116110 1334            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB3F9500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB953920
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.116116 1334        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.116120 1334  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.116124 1334          Options.compression: LZ4
2025/05/19-15:45:12.116128 1334                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.116132 1334       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.116136 1334   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.116140 1334             Options.num_levels: 7
2025/05/19-15:45:12.116143 1334        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.116147 1334     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.116151 1334     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.116155 1334            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.116159 1334                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.116163 1334               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.116167 1334         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.116171 1334         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.116175 1334         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.116179 1334                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.116183 1334         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.116187 1334         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.116191 1334            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.116197 1334                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.116203 1334               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.116206 1334         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.116210 1334         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.116214 1334         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.116218 1334         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.116222 1334                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.116226 1334         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.116230 1334      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.116234 1334          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.116238 1334              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.116241 1334                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.116245 1334             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.116249 1334                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.116253 1334 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.116258 1334          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.116262 1334 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.116266 1334 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.116270 1334 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.116275 1334 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.116279 1334 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.116283 1334 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.116287 1334 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.116292 1334       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.116296 1334                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.116300 1334                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.116317 1334   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.116322 1334   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.116326 1334                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.116330 1334                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.116334 1334                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.116338 1334 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.116343 1334 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.116347 1334 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.116352 1334 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.116356 1334 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.116361 1334 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.116365 1334 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.116369 1334 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.116374 1334 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.116380 1334                   Options.table_properties_collectors: 
2025/05/19-15:45:12.116384 1334                   Options.inplace_update_support: 0
2025/05/19-15:45:12.116388 1334                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.116393 1334               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.116397 1334               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.116402 1334   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.116408 1334                           Options.bloom_locality: 0
2025/05/19-15:45:12.116413 1334                    Options.max_successive_merges: 0
2025/05/19-15:45:12.116417 1334             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.116421 1334                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.116426 1334                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.116431 1334                Options.force_consistency_checks: 1
2025/05/19-15:45:12.116435 1334                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.116440 1334                               Options.ttl: 2592000
2025/05/19-15:45:12.116444 1334          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.116448 1334                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.116453 1334  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.116457 1334    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.116461 1334                       Options.enable_blob_files: false
2025/05/19-15:45:12.116466 1334                           Options.min_blob_size: 0
2025/05/19-15:45:12.116470 1334                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.116474 1334                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.116479 1334          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.116483 1334      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.116488 1334 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.116493 1334          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.116497 1334                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.116501 1334         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.116506 1334            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.117131 1334               Options.comparator: leveldb.BytewiseComparator
2025/05/19-15:45:12.117141 1334           Options.merge_operator: None
2025/05/19-15:45:12.117145 1334        Options.compaction_filter: None
2025/05/19-15:45:12.117148 1334        Options.compaction_filter_factory: None
2025/05/19-15:45:12.117152 1334  Options.sst_partitioner_factory: None
2025/05/19-15:45:12.117156 1334         Options.memtable_factory: SkipListFactory
2025/05/19-15:45:12.117160 1334            Options.table_factory: BlockBasedTable
2025/05/19-15:45:12.117195 1334            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DEB3F9500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DEB953920
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/05/19-15:45:12.117200 1334        Options.write_buffer_size: 10485760
2025/05/19-15:45:12.117204 1334  Options.max_write_buffer_number: 2
2025/05/19-15:45:12.117207 1334          Options.compression: LZ4
2025/05/19-15:45:12.117211 1334                  Options.bottommost_compression: Disabled
2025/05/19-15:45:12.117215 1334       Options.prefix_extractor: nullptr
2025/05/19-15:45:12.117222 1334   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/19-15:45:12.117227 1334             Options.num_levels: 7
2025/05/19-15:45:12.117231 1334        Options.min_write_buffer_number_to_merge: 1
2025/05/19-15:45:12.117235 1334     Options.max_write_buffer_number_to_maintain: 0
2025/05/19-15:45:12.117239 1334     Options.max_write_buffer_size_to_maintain: 0
2025/05/19-15:45:12.117243 1334            Options.bottommost_compression_opts.window_bits: -14
2025/05/19-15:45:12.117247 1334                  Options.bottommost_compression_opts.level: 32767
2025/05/19-15:45:12.117252 1334               Options.bottommost_compression_opts.strategy: 0
2025/05/19-15:45:12.117256 1334         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.117261 1334         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.117265 1334         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/19-15:45:12.117269 1334                  Options.bottommost_compression_opts.enabled: false
2025/05/19-15:45:12.117274 1334         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.117278 1334         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.117282 1334            Options.compression_opts.window_bits: -14
2025/05/19-15:45:12.117292 1334                  Options.compression_opts.level: 32767
2025/05/19-15:45:12.117297 1334               Options.compression_opts.strategy: 0
2025/05/19-15:45:12.117301 1334         Options.compression_opts.max_dict_bytes: 0
2025/05/19-15:45:12.117311 1334         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/19-15:45:12.117315 1334         Options.compression_opts.use_zstd_dict_trainer: true
2025/05/19-15:45:12.117319 1334         Options.compression_opts.parallel_threads: 1
2025/05/19-15:45:12.117323 1334                  Options.compression_opts.enabled: false
2025/05/19-15:45:12.117327 1334         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/19-15:45:12.117331 1334      Options.level0_file_num_compaction_trigger: 4
2025/05/19-15:45:12.117335 1334          Options.level0_slowdown_writes_trigger: 20
2025/05/19-15:45:12.117339 1334              Options.level0_stop_writes_trigger: 36
2025/05/19-15:45:12.117343 1334                   Options.target_file_size_base: 67108864
2025/05/19-15:45:12.117347 1334             Options.target_file_size_multiplier: 1
2025/05/19-15:45:12.117351 1334                Options.max_bytes_for_level_base: 268435456
2025/05/19-15:45:12.117354 1334 Options.level_compaction_dynamic_level_bytes: 1
2025/05/19-15:45:12.117359 1334          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/19-15:45:12.117363 1334 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/19-15:45:12.117367 1334 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/19-15:45:12.117371 1334 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/19-15:45:12.117375 1334 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/19-15:45:12.117379 1334 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/19-15:45:12.117382 1334 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/19-15:45:12.117387 1334 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/19-15:45:12.117391 1334       Options.max_sequential_skip_in_iterations: 8
2025/05/19-15:45:12.117395 1334                    Options.max_compaction_bytes: 1677721600
2025/05/19-15:45:12.117402 1334                        Options.arena_block_size: 1048576
2025/05/19-15:45:12.117406 1334   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/19-15:45:12.117410 1334   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/19-15:45:12.117414 1334                Options.disable_auto_compactions: 0
2025/05/19-15:45:12.117418 1334                        Options.compaction_style: kCompactionStyleLevel
2025/05/19-15:45:12.117422 1334                          Options.compaction_pri: kMinOverlappingRatio
2025/05/19-15:45:12.117430 1334 Options.compaction_options_universal.size_ratio: 1
2025/05/19-15:45:12.117435 1334 Options.compaction_options_universal.min_merge_width: 2
2025/05/19-15:45:12.117439 1334 Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/19-15:45:12.117443 1334 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/19-15:45:12.117447 1334 Options.compaction_options_universal.compression_size_percent: -1
2025/05/19-15:45:12.117451 1334 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/19-15:45:12.117455 1334 Options.compaction_options_universal.max_read_amp: -1
2025/05/19-15:45:12.117459 1334 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/19-15:45:12.117463 1334 Options.compaction_options_fifo.allow_compaction: 0
2025/05/19-15:45:12.117468 1334                   Options.table_properties_collectors: 
2025/05/19-15:45:12.117472 1334                   Options.inplace_update_support: 0
2025/05/19-15:45:12.117476 1334                 Options.inplace_update_num_locks: 10000
2025/05/19-15:45:12.117481 1334               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/19-15:45:12.117485 1334               Options.memtable_whole_key_filtering: 0
2025/05/19-15:45:12.117489 1334   Options.memtable_huge_page_size: 0
2025/05/19-15:45:12.117493 1334                           Options.bloom_locality: 0
2025/05/19-15:45:12.117496 1334                    Options.max_successive_merges: 0
2025/05/19-15:45:12.117500 1334             Options.strict_max_successive_merges: 0
2025/05/19-15:45:12.117504 1334                Options.optimize_filters_for_hits: 0
2025/05/19-15:45:12.117508 1334                Options.paranoid_file_checks: 0
2025/05/19-15:45:12.117512 1334                Options.force_consistency_checks: 1
2025/05/19-15:45:12.117516 1334                Options.report_bg_io_stats: 0
2025/05/19-15:45:12.117519 1334                               Options.ttl: 2592000
2025/05/19-15:45:12.117523 1334          Options.periodic_compaction_seconds: 0
2025/05/19-15:45:12.117527 1334                        Options.default_temperature: kUnknown
2025/05/19-15:45:12.117531 1334  Options.preclude_last_level_data_seconds: 0
2025/05/19-15:45:12.117535 1334    Options.preserve_internal_time_seconds: 0
2025/05/19-15:45:12.117539 1334                       Options.enable_blob_files: false
2025/05/19-15:45:12.117543 1334                           Options.min_blob_size: 0
2025/05/19-15:45:12.117547 1334                          Options.blob_file_size: 268435456
2025/05/19-15:45:12.117551 1334                   Options.blob_compression_type: NoCompression
2025/05/19-15:45:12.117555 1334          Options.enable_blob_garbage_collection: false
2025/05/19-15:45:12.117559 1334      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/19-15:45:12.117563 1334 Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/19-15:45:12.117567 1334          Options.blob_compaction_readahead_size: 0
2025/05/19-15:45:12.117571 1334                Options.blob_file_starting_level: 0
2025/05/19-15:45:12.117575 1334         Options.experimental_mempurge_threshold: 0.000000
2025/05/19-15:45:12.117579 1334            Options.memtable_max_range_deletions: 0
2025/05/19-15:45:12.141748 1334 DB pointer 0000023DEB8A8C40
