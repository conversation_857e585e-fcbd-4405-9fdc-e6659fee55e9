# Fonction pour afficher les logs avec couleur
function Write-ColorLog {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        
        [Parameter(Mandatory=$false)]
        [string]$ForegroundColor = "White"
    )
    
    Write-Host $Message -ForegroundColor $ForegroundColor
}

# Afficher l'en-tête
Write-ColorLog "====================================================" "Cyan"
Write-ColorLog "           EMA COMPANION - STARTUP SCRIPT           " "Cyan"
Write-ColorLog "====================================================" "Cyan"
Write-ColorLog "Starting services at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Yellow"
Write-ColorLog "----------------------------------------------------" "Cyan"

# Détermine la commande Python
$PYTHON_CMD = "python"
if (Get-Command python3 -ErrorAction SilentlyContinue) {
    $PYTHON_CMD = "python3"
    Write-ColorLog "Using Python command: $PYTHON_CMD" "Green"
} else {
    Write-ColorLog "Using Python command: $PYTHON_CMD" "Green"
}

# Stocke le répertoire racine
$ROOT_DIR = Get-Location
Write-ColorLog "Root directory: $ROOT_DIR" "Gray"

# Installer les dépendances Python
Write-ColorLog "Installing Python dependencies..." "Yellow"
& $PYTHON_CMD -m pip install -r requirements.txt
Write-ColorLog "Python dependencies installed" "Green"

# Pour db-api-service
Write-ColorLog "Starting db-api-service on port 8086..." "Yellow"
$DB_API_PROC = Start-Process -FilePath $PYTHON_CMD -ArgumentList "main.py" -WorkingDirectory "$ROOT_DIR\db-api-service" -PassThru
Write-ColorLog "db-api-service started with PID: $($DB_API_PROC.Id)" "Green"
Write-ColorLog "  - URL: http://**********:8086" "Magenta"
Write-ColorLog "  - Service: Database API" "Magenta"

# Pour db-vector-service
Write-ColorLog "Starting db-vector-service on port 8087..." "Yellow"
$DB_VECTOR_PROC = Start-Process -FilePath $PYTHON_CMD -ArgumentList "main.py" -WorkingDirectory "$ROOT_DIR\db-vector-service" -PassThru
Write-ColorLog "db-vector-service started with PID: $($DB_VECTOR_PROC.Id)" "Green"
Write-ColorLog "  - URL: http://**********:8087" "Magenta"
Write-ColorLog "  - Service: Vector Search API" "Magenta"

# Pour qdrant.exe (db-vector-service)
Write-ColorLog "Starting Qdrant vector database..." "Yellow"
# $QDRANT_PROC = Start-Process -FilePath "$ROOT_DIR\db-qdrant-vector\qdrant.exe" -WorkingDirectory "$ROOT_DIR\db-qdrant-vector" -PassThru
Write-ColorLog "Qdrant vector database started with PID: $($QDRANT_PROC.Id)" "Green"
Write-ColorLog "  - URL: http://**********:6333" "Magenta"
Write-ColorLog "  - Service: Qdrant Vector Database" "Magenta"

# Retour au répertoire racine
Set-Location $ROOT_DIR

# Attendre l'initialisation des services
Write-ColorLog "Waiting for services to initialize..." "Yellow"
Start-Sleep -Seconds 3
Write-ColorLog "Services initialized" "Green"

# Compiler les fichiers TypeScript
Write-ColorLog "Building TypeScript files..." "Yellow"
npm run build
Write-ColorLog "TypeScript build completed" "Green"

# Démarrer l'application principale
Write-ColorLog "Starting main application..." "Yellow"
Write-ColorLog "  - Frontend will be available at: http://**********:3000" "Magenta"
Write-ColorLog "  - Backend API will be available at: http://**********:3005" "Magenta"
npx concurrently "npm run frontend:dev" "node dist/server.js"

Write-ColorLog "Application processes started" "Green"
Write-ColorLog "----------------------------------------------------" "Cyan"
Write-ColorLog "All services are now running!" "Green"
Write-ColorLog "Press Ctrl+C to stop all services" "Yellow"

# À la fin, tuer les services
Write-ColorLog "====================================================" "Cyan"
Write-ColorLog "           SHUTTING DOWN SERVICES                   " "Cyan"
Write-ColorLog "====================================================" "Cyan"
Write-ColorLog "Shutting down services at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Yellow"

# Nettoyage
if ($DB_API_PROC -and !$DB_API_PROC.HasExited) {
    Write-ColorLog "Stopping db-api-service (PID: $($DB_API_PROC.Id))..." "Yellow"
    $DB_API_PROC.Kill()
    Write-ColorLog "db-api-service stopped" "Green"
}

if ($DB_VECTOR_PROC -and !$DB_VECTOR_PROC.HasExited) {
    Write-ColorLog "Stopping db-vector-service (PID: $($DB_VECTOR_PROC.Id))..." "Yellow"
    $DB_VECTOR_PROC.Kill()
    Write-ColorLog "db-vector-service stopped" "Green"
}

if ($QDRANT_PROC -and !$QDRANT_PROC.HasExited) {
    Write-ColorLog "Stopping Qdrant vector database (PID: $($QDRANT_PROC.Id))..." "Yellow"
    # $QDRANT_PROC.Kill()
    Write-ColorLog "Qdrant vector database stopped" "Green"
}

Write-ColorLog "All services have been stopped" "Green"
Write-ColorLog "====================================================" "Cyan"
