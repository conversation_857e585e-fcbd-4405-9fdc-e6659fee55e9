/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/frontend/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'sans': ['"Pathway Extreme"', 'Inter', 'system-ui', 'Avenir', 'Helvetica', 'Arial', 'sans-serif'],
      },
      colors: {
        primary: "#59B5C3", // Couleur 59B5C3 de Titre
        secondary: "#f3f4f6", // Light gray for backgrounds
        accent: "#4b5563", // Dark gray for text
        "nextchat-bg": "#f7f7f8", // Couleur de fond principale
        "sidebar-bg": "#f0f9ff", // Couleur de fond de la sidebar
        "chat-border": "#e5e7eb", // Couleur des bordures
        "selected-chat": "#e6f7ff", // Couleur de l'élément sélectionné
      },
      borderRadius: {
        'xl': '1rem',
      }
    },
  },
  plugins: [],
}
